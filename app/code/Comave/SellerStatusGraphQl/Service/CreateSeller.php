<?php

declare(strict_types=1);

namespace Comave\SellerStatusGraphQl\Service;

use Comave\SellerStatus\Api\RoleProviderInterface;
use Comave\SellerStatus\Api\RoleProviderManagementInterface;
use Magento\Company\Api\AclInterface;
use Magento\Company\Api\CompanyCustomerAssignmentInterface;
use Magento\Customer\Api\Data\CustomerInterface;
use Webkul\Marketplace\Model\Seller as Model;
use Webkul\Marketplace\Model\SellerFactory;
use Webkul\Marketplace\Model\ResourceModel\Seller as ResourceModel;

class CreateSeller
{
    /**
     * @param SellerFactory $sellerFactory
     * @param ResourceModel $sellerResource
     */
    public function __construct(
        private readonly SellerFactory $sellerFactory,
        private readonly ResourceModel $sellerResource,
    ) {
    }

    /**
     * @param CustomerInterface $customer
     * @param array{store_url?: string, company_phone: string, company_industry: string, company_name: string, company_country: string, industry: string} $inputData
     * @return Model
     * @throws \Magento\Framework\Exception\AlreadyExistsException
     */
    public function execute(
        CustomerInterface $customer,
        array $inputData
    ): Model {
        $seller = $this->sellerFactory->create();
        $seller->setData([
            'seller_id' => $customer->getId(),
            'store_id' => $customer->getStoreId(),
            'country_pic' => $inputData['company_country'],
            'is_seller' => 1,
            'shop_url' => $inputData['store_url'] ?? null,
            'email' => $customer->getEmail(),
            'shop_title' => strip_tags($inputData['company_name']),
            'contact_number' => $inputData['company_phone'],
            'others_info' => json_encode([
                'industry' => $inputData['company_industry'],
            ]),
        ]);
        $this->sellerResource->save($seller);

        return $seller;
    }
}
