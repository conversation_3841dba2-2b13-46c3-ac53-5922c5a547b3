type Mutation {
    createCustomerV2 (
        input: CustomerCreateInput! @doc(description: "An input object that defines the customer to be created.")
    ): CustomerOutput
    @resolver(class: "\\Comave\\SellerStatusGraphQl\\Model\\Resolver\\CreateCustomer")
    @doc(description:"Create a customer account (and seller based on input).")
}

input CustomerCreateInput {
    become_a_seller: SellerInputData
    @doc(description: "Sets the proper data in order to create a seller from graphql")
}

input SellerInputData {
    store_url: String @doc(description: "Store URL of the seller")
    company_name: String! @doc(description: "Company name of the seller")
    company_country: String! @doc(description: "ISO code of the seller country")
    company_industry: IndustryEnum! @doc(description: "One of RETAIL, SPORTS, CLUB")
    company_phone: String! @doc(description: "Seller contact phone number")
}

enum IndustryEnum {
    RETAIL
    SPORTS
    CLUB
}
