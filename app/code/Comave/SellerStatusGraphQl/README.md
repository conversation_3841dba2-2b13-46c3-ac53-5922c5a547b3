# Create New Customer Mutation

This GraphQL mutation creates a new customer account and optionally registers the customer as a seller with store details. It also supports custom attributes like referral codes, club membership, and contact details.

## 📌 Mutation Name

`createNewCustomer`

---

## 🧾 Overview

This mutation uses Magento 2's `createCustomerV2` API to register a new user and optionally sign them up as a seller by providing seller-specific input under `become_a_seller`.

---

## 🔐 Authentication

- This mutation typically does **not require authentication**, but Magento instance settings may vary. Ensure guest access to `createCustomerV2` is enabled if used without a token.
- Sensitive data like passwords is transmitted, so HTTPS is recommended.

---

## 🧪 Example Mutation

```graphql
mutation createNewCustomer($customerEmailReg: String!) {
  createCustomerV2(
    input: {
      become_a_seller: {
        store_url: "https://somestore.url"
        company_name: "My new company"
        company_country: "GB"
        company_phone: "+407407292111"
        company_industry: SPORTS
      }
      firstname: "<PERSON><PERSON>"
      lastname: "Coman"
      email: $customerEmailReg
      password: "Test1234!"
      is_subscribed: true
      custom_attributes: [
        {
          attribute_code: "customerclub"
          value: "crystal_palace"
        },
        {
          attribute_code: "customer_country"
          value: "US"
        },
        {
          attribute_code: "phone_no"
          value: "3214322234211"
        },
        {
          attribute_code: "referral_code"
          value: "ABCDE"
        }
      ]
    }
  ) {
    customer {
      firstname
      lastname
      email
      is_subscribed
    }
  }
}
```

---

## 📥 Input Parameters

| Name               | Type     | Required | Description                                  |
|--------------------|----------|----------|----------------------------------------------|
| `customerEmailReg` | String   | ✅       | The email address for the new customer.      |

---

## 📤 Custom Attributes (Optional)

| Attribute Code     | Value Example     | Purpose                                    |
|--------------------|------------------|--------------------------------------------|
| `customerclub`     | `"crystal_palace"`| Associates customer with a club.           |
| `customer_country` | `"US"`            | Country code for the customer.             |
| `phone_no`         | `"3214322234211"` | Customer's phone number.                   |
| `referral_code`    | `"ABCDE"`         | Code for referral tracking.                |

---

## 🛍 Become a Seller (Optional Block)

If included, this block registers the customer as a seller with the following fields:

| Field             | Example                 | Description                          |
|------------------|-------------------------|--------------------------------------|
| `store_url`       | `"https://somestore.url"`| Seller's store front URL             |
| `company_name`    | `"My new company"`       | Name of the company                  |
| `company_country` | `"GB"`                   | ISO country code                     |
| `company_phone`   | `"+407407292111"`        | Contact phone                        |
| `company_industry`| `SPORTS`                 | Industry type (must match enum)      |

---

## ✅ Successful Response

```json
{
  "data": {
    "createCustomerV2": {
      "customer": {
        "firstname": "Mihai",
        "lastname": "Coman",
        "email": "<EMAIL>",
        "is_subscribed": true
      }
    }
  }
}
```

---

## ⚠️ Notes

- Password policy must conform to Magento's security requirements.
- Fields like `company_industry` must match predefined enum values in the schema.
- Ensure that any `custom_attributes` codes exist in your Magento setup.

---
