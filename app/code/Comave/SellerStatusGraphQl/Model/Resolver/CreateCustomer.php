<?php

declare(strict_types=1);

namespace Comave\SellerStatusGraphQl\Model\Resolver;

use Comave\SellerStatusGraphQl\Service\CreateSeller;
use Magento\Customer\Api\Data\CustomerInterface;
use Magento\CustomerGraphQl\Model\Customer\CreateCustomerAccount;
use Magento\CustomerGraphQl\Model\Customer\ExtractCustomerData;
use Magento\CustomerGraphQl\Model\Resolver\CreateCustomer as CoreResolver;
use Magento\Directory\Model\CountryFactory;
use Magento\Directory\Model\ResourceModel\Country;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Exception\GraphQlInputException;
use Magento\Framework\GraphQl\Query\Resolver\ContextInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Magento\Newsletter\Model\Config;

/**
 * Create customer account resolver
 */
class CreateCustomer extends CoreResolver
{
    private const string BECOME_A_SELLER_INPUT = 'become_a_seller';

    /**
     * @param ExtractCustomerData $extractCustomerData
     * @param CreateCustomerAccount $createCustomerAccount
     * @param Config $newsLetterConfig
     * @param CountryFactory $countryFactory
     * @param Country $resourceModel
     * @param CreateSeller $createSellerService
     */
    public function __construct(
        ExtractCustomerData $extractCustomerData,
        CreateCustomerAccount $createCustomerAccount,
        Config $newsLetterConfig,
        private readonly CountryFactory $countryFactory,
        private readonly Country $resourceModel,
        private readonly CreateSeller $createSellerService
    ) {
        parent::__construct($extractCustomerData, $createCustomerAccount, $newsLetterConfig);
    }

    /**
     * @param Field $field
     * @param ContextInterface$context
     * @param ResolveInfo $info
     * @param array|null $value
     * @param array|null $args
     * @return array
     * @throws \Exception
     */
    public function resolve(Field $field, $context, ResolveInfo $info, array $value = null, array $args = null): array
    {
        if (!isset($args['input'][self::BECOME_A_SELLER_INPUT])) {
            return parent::resolve($field, $context, $info, $value, $args);
        }

        $this->validateSellerData($args['input'][self::BECOME_A_SELLER_INPUT]);
        $customerData = parent::resolve($field, $context, $info, $value, $args);

        if (!$customerData['customer']['model'] instanceof CustomerInterface) {
            return $customerData;
        }

        $this->createSellerService->execute(
            $customerData['customer']['model'],
            $args['input'][self::BECOME_A_SELLER_INPUT]
        );

        return $customerData;
    }

    /**
     * @param array $sellerInput
     * @return void
     * @throws GraphQlInputException
     */
    private function validateSellerData(array $sellerInput): void
    {
        if (isset($sellerInput['store_url'])) {
            $validUrl = filter_var($sellerInput['store_url'], FILTER_VALIDATE_URL);

            if ($validUrl === false) {
                throw new GraphQlInputException(
                    __('You must provide a proper store url to register as a seller')
                );
            }
        }

        $countryCode = $sellerInput['company_country'];
        $country = $this->countryFactory->create();

        try {
            $this->resourceModel->loadByCode($country, $countryCode);
        } catch (\Exception) {
            throw new GraphQlInputException(
                __('You have provided an invalid country code for your seller account')
            );
        }

        //@todo add twilio phone validation ? $sellerInput['company_phone']
    }
}
