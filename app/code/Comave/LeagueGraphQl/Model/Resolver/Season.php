<?php
declare(strict_types=1);

namespace Comave\LeagueGraphQl\Model\Resolver;

use Comave\League\Model\SeasonUiManager;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Exception\GraphQlInputException;
use Magento\Framework\GraphQl\Exception\GraphQlNoSuchEntityException;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;

/**
 * GraphQL resolver for season data
 *
 * Resolves individual season information by ID, providing complete season details
 * with proper GraphQL error handling and data transformation.
 */
class Season implements ResolverInterface
{
    /**
     * @param \Comave\League\Model\SeasonUiManager $seasonUiManager
     */
    public function __construct(
        private readonly SeasonUiManager $seasonUiManager
    ) {
    }

    /**
     * @inheritdoc
     */
    public function resolve(
        Field $field,
        $context,
        ResolveInfo $info,
        ?array $value = null,
        ?array $args = null
    ) {
        $this->validateArgs($args);

        try {
            $season = $this->seasonUiManager->get((int)$args['id']);

            return [
                'season_id' => $season->getId(),
                'name' => $season->getName(),
                'league_id' => $season->getLeagueId(),
                'max_clubs' => $season->getMaxClubs(),
                'start_date' => $season->getStartDate(),
                'end_date' => $season->getEndDate(),
                'is_current' => $season->getIsCurrent(),
                'created_at' => $season->getCreatedAt(),
                'updated_at' => $season->getUpdatedAt(),
                'model' => $season,
            ];
        } catch (NoSuchEntityException $e) {
            throw new GraphQlNoSuchEntityException(__('Season with ID "%1" does not exist.', $args['id']));
        }
    }

    /**
     * Validate GraphQL arguments
     *
     * @param array $args
     * @throws GraphQlInputException
     */
    private function validateArgs(array $args): void
    {
        if (!isset($args['id']) || !is_int($args['id']) || $args['id'] <= 0) {
            throw new GraphQlInputException(__('Season ID must be a positive integer'));
        }
    }
}
