<?php
declare(strict_types=1);

namespace Comave\LeagueGraphQl\Model\Resolver;

use Comave\League\Api\SeasonListRepositoryInterface;
use Comave\LeagueGraphQl\Model\Transformer\SeasonTransformer;
use Magento\Framework\Api\FilterBuilder;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\App\CacheInterface;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Exception\GraphQlInputException;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Magento\Framework\Serialize\SerializerInterface;

/**
 * GraphQL resolver for seasons data
 */
class Seasons implements ResolverInterface
{
    private const string CACHE_KEY = 'league_graphql_seasons_';
    private const string CACHE_TAG = 'LEAGUE_SEASONS';
    private const int CACHE_TTL = 7200;

    /**
     * @param \Comave\League\Api\SeasonListRepositoryInterface $seasonListRepository
     * @param \Magento\Framework\Api\SearchCriteriaBuilder $searchCriteriaBuilder
     * @param \Magento\Framework\Api\FilterBuilder $filterBuilder
     * @param \Magento\Framework\App\CacheInterface $cache
     * @param \Magento\Framework\Serialize\SerializerInterface $serializer
     * @param \Comave\LeagueGraphQl\Model\Transformer\SeasonTransformer $seasonTransformer
     */
    public function __construct(
        private readonly SeasonListRepositoryInterface $seasonListRepository,
        private readonly SearchCriteriaBuilder $searchCriteriaBuilder,
        private readonly FilterBuilder $filterBuilder,
        private readonly CacheInterface $cache,
        private readonly SerializerInterface $serializer,
        private readonly SeasonTransformer $seasonTransformer
    ) {
    }

    /**
     * @inheritdoc
     */
    public function resolve(
        Field $field,
        $context,
        ResolveInfo $info,
        ?array $value = null,
        ?array $args = null
    ) {
        $this->validateArgs($args);

        $cacheKey = $this->generateCacheKey($args);
        $cachedData = $this->cache->load($cacheKey);

        if ($cachedData) {
            return $this->serializer->unserialize($cachedData);
        }

        $searchCriteria = $this->buildSearchCriteria($args);
        $searchResult = $this->seasonListRepository->getList($searchCriteria);

        $transformationContext = $this->seasonTransformer->createContext(
            [],
            [
                'pageSize' => $args['pageSize'] ?? 20,
                'currentPage' => $args['currentPage'] ?? 1,
            ]
        );

        $seasonListDto = $this->seasonTransformer->transformSearchResults($searchResult, $transformationContext);
        $result = $seasonListDto->toArray();

        $this->cache->save(
            $this->serializer->serialize($result),
            $cacheKey,
            [self::CACHE_TAG],
            self::CACHE_TTL
        );

        return $result;
    }

    /**
     * Validate GraphQL arguments
     *
     * @param array $args
     * @throws GraphQlInputException
     */
    private function validateArgs(array $args): void
    {
        if (isset($args['filter'])) {
            $filter = $args['filter'];

            if (isset($filter['league_id']) && (!is_int($filter['league_id']) || $filter['league_id'] <= 0)) {
                throw new GraphQlInputException(__('league_id must be a positive integer'));
            }

            if (isset($filter['is_current']) && !in_array($filter['is_current'], [0, 1], true)) {
                throw new GraphQlInputException(__('is_current must be 0 or 1'));
            }

            if (isset($filter['name'])) {
                if (!is_string($filter['name']) || strlen(trim($filter['name'])) === 0) {
                    throw new GraphQlInputException(__('name filter must be a non-empty string'));
                }
                if (strlen($filter['name']) > 255) {
                    throw new GraphQlInputException(__('name filter must not exceed 255 characters'));
                }
            }
        }

        if (isset($args['pageSize'])) {
            if (!is_int($args['pageSize']) || $args['pageSize'] <= 0 || $args['pageSize'] > 100) {
                throw new GraphQlInputException(__('pageSize must be a positive integer between 1 and 100'));
            }
        }

        if (isset($args['currentPage'])) {
            if (!is_int($args['currentPage']) || $args['currentPage'] <= 0) {
                throw new GraphQlInputException(__('currentPage must be a positive integer'));
            }
        }
    }

    /**
     * Build search criteria from arguments
     *
     * @param array $args
     * @return \Magento\Framework\Api\SearchCriteriaInterface
     */
    private function buildSearchCriteria(array $args): \Magento\Framework\Api\SearchCriteriaInterface
    {

        if (isset($args['filter'])) {
            $filter = $args['filter'];

            if (isset($filter['league_id'])) {
                $leagueFilter = $this->filterBuilder
                    ->setField('league_id')
                    ->setValue($filter['league_id'])
                    ->setConditionType('eq')
                    ->create();
                $this->searchCriteriaBuilder->addFilters([$leagueFilter]);
            }

            if (isset($filter['is_current'])) {
                $currentFilter = $this->filterBuilder
                    ->setField('is_current')
                    ->setValue($filter['is_current'])
                    ->setConditionType('eq')
                    ->create();
                $this->searchCriteriaBuilder->addFilters([$currentFilter]);
            }

            if (isset($filter['name'])) {
                $nameFilter = $this->filterBuilder
                    ->setField('name')
                    ->setValue('%'.$filter['name'].'%')
                    ->setConditionType('like')
                    ->create();
                $this->searchCriteriaBuilder->addFilters([$nameFilter]);
            }
        }

        $pageSize = $args['pageSize'] ?? 20;
        $currentPage = $args['currentPage'] ?? 1;

        $this->searchCriteriaBuilder->setPageSize($pageSize);
        $this->searchCriteriaBuilder->setCurrentPage($currentPage);

        return $this->searchCriteriaBuilder->create();
    }

    /**
     * Generate cache key based on query parameters
     *
     * @param array $args
     * @return string
     */
    private function generateCacheKey(array $args): string
    {
        $keyData = [
            'filter' => $args['filter'] ?? [],
            'page_size' => $args['pageSize'] ?? 20,
            'current_page' => $args['currentPage'] ?? 1,
        ];

        return self::CACHE_KEY.md5($this->serializer->serialize($keyData));
    }
}
