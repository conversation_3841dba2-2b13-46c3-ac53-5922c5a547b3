<?php
declare(strict_types=1);

namespace Comave\LeagueGraphQl\Model\Resolver\Season;

use Comave\League\Model\LeagueUiManager;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Exception\GraphQlNoSuchEntityException;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;

class League implements ResolverInterface
{
    /**
     * @param \Comave\League\Model\LeagueUiManager $leagueUiManager
     */
    public function __construct(
        private readonly LeagueUiManager $leagueUiManager
    ) {
    }

    /**
     * @inheritdoc
     */
    public function resolve(
        Field $field,
        $context,
        ResolveInfo $info,
        ?array $value = null,
        ?array $args = null
    ) {
        if (!isset($value['league_id'])) {
            throw new GraphQlNoSuchEntityException(__('League ID is missing from season data'));
        }

        try {
            $league = $this->leagueUiManager->get((int)$value['league_id']);

            return [
                'league_id' => $league->getId(),
                'name' => $league->getName(),
                'code' => $league->getCode(),
                'description' => $league->getDescription(),
                'country_code' => $league->getCountryCode(),
                'logo' => $league->getLogo(),
                'status' => $league->getStatus(),
            ];
        } catch (NoSuchEntityException $e) {
            throw new GraphQlNoSuchEntityException(__('League with ID "%1" does not exist.', $value['league_id']));
        }
    }
}
