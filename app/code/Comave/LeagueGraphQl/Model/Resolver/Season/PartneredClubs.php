<?php
declare(strict_types=1);

namespace Comave\LeagueGraphQl\Model\Resolver\Season;

use Comave\League\Api\LeagueClubListRepositoryInterface;
use Magento\Framework\Api\FilterBuilder;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\Api\SearchCriteriaInterface;
use Magento\Framework\Api\SortOrderFactory;
use Magento\Framework\App\CacheInterface;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Exception\GraphQlInputException;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Magento\Framework\Serialize\SerializerInterface;

/**
 * GraphQL resolver for partnered clubs within a season
 *
 * Resolves league clubs that have partnered status for a specific season,
 * providing filtered, sorted, and paginated results with caching support.
 */
class PartneredClubs implements ResolverInterface
{
    private const string CACHE_KEY = 'league_graphql_partnered_clubs_';
    private const string CACHE_TAG = 'LEAGUE_CLUBS';
    private const int CACHE_TTL = 3600; // 1 hour

    /**
     * @param \Comave\League\Api\LeagueClubListRepositoryInterface $leagueClubListRepository
     * @param \Magento\Framework\Api\SearchCriteriaBuilder $searchCriteriaBuilder
     * @param \Magento\Framework\Api\FilterBuilder $filterBuilder
     * @param \Magento\Framework\Api\SortOrderFactory $sortOrderFactory
     * @param \Magento\Framework\App\CacheInterface $cache
     * @param \Magento\Framework\Serialize\SerializerInterface $serializer
     */
    public function __construct(
        private readonly LeagueClubListRepositoryInterface $leagueClubListRepository,
        private readonly SearchCriteriaBuilder $searchCriteriaBuilder,
        private readonly FilterBuilder $filterBuilder,
        private readonly SortOrderFactory $sortOrderFactory,
        private readonly CacheInterface $cache,
        private readonly SerializerInterface $serializer
    ) {
    }

    /**
     * @inheritdoc
     */
    public function resolve(
        Field $field,
        $context,
        ResolveInfo $info,
        ?array $value = null,
        ?array $args = null
    ) {
        if (!isset($value['season_id'])) {
            return [];
        }

        $this->validateArgs($args ?? [], (int)$value['season_id']);

        $cacheKey = $this->generateCacheKey((int)$value['season_id'], $args ?? []);
        $cachedData = $this->cache->load($cacheKey);

        if ($cachedData) {
            return $this->serializer->unserialize($cachedData);
        }

        $searchCriteria = $this->buildSearchCriteria((int)$value['season_id'], $args ?? []);
        $searchResult = $this->leagueClubListRepository->getList($searchCriteria);

        $leagueClubs = [];
        foreach ($searchResult->getItems() as $leagueClub) {
            $leagueClubs[] = [
                'league_club_id' => $leagueClub->getId(),
                'season_id' => $leagueClub->getSeasonId(),
                'club_id' => $leagueClub->getClubId(),
                'rank' => $leagueClub->getRank(),
                'points' => $leagueClub->getPoints(),
                'matches_played' => $leagueClub->getMatchesPlayed(),
                'wins' => $leagueClub->getWins(),
                'draws' => $leagueClub->getDraws(),
                'losses' => $leagueClub->getLosses(),
                'goals_for' => $leagueClub->getGoalsFor(),
                'goals_against' => $leagueClub->getGoalsAgainst(),
                'created_at' => $leagueClub->getCreatedAt(),
                'updated_at' => $leagueClub->getUpdatedAt(),
                'model' => $leagueClub,
            ];
        }

        $this->cache->save(
            $this->serializer->serialize($leagueClubs),
            $cacheKey,
            [self::CACHE_TAG],
            self::CACHE_TTL
        );

        return $leagueClubs;
    }

    /**
     * Build search criteria for partnered clubs in season
     *
     * @param int $seasonId
     * @return \Magento\Framework\Api\SearchCriteriaInterface
     */
    private function buildSearchCriteria(int $seasonId, array $args = []): SearchCriteriaInterface
    {
        $seasonFilter = $this->filterBuilder
            ->setField('season_id')
            ->setValue($seasonId)
            ->setConditionType('eq')
            ->create();
        $this->searchCriteriaBuilder->addFilters([$seasonFilter]);

        $partneredFilter = $this->filterBuilder
            ->setField('club.partnered')
            ->setValue(1)
            ->setConditionType('eq')
            ->create();
        $this->searchCriteriaBuilder->addFilters([$partneredFilter]);

        $sortBy = $args['sortBy'] ?? 'rank';
        $sortDirection = $args['sortDirection'] ?? 'ASC';

        if (in_array($sortBy, ['rank', 'points', 'goals_for', 'goals_against'])) {
            $sortOrder = $this->sortOrderFactory->create()
                ->setField($sortBy)
                ->setDirection($sortDirection);
            $this->searchCriteriaBuilder->addSortOrder($sortOrder);
        }

        $pageSize = $args['pageSize'] ?? 20;
        $currentPage = $args['currentPage'] ?? 1;

        $this->searchCriteriaBuilder->setPageSize($pageSize);
        $this->searchCriteriaBuilder->setCurrentPage($currentPage);

        return $this->searchCriteriaBuilder->create();
    }

    /**
     * Generate cache key based on season ID and query parameters
     *
     * @param int $seasonId
     * @param array $args
     * @return string
     */
    private function generateCacheKey(int $seasonId, array $args): string
    {
        $keyData = [
            'season_id' => $seasonId,
            'partnered' => 1,
            'sort_by' => $args['sortBy'] ?? 'rank',
            'sort_direction' => $args['sortDirection'] ?? 'ASC',
            'page_size' => $args['pageSize'] ?? 20,
            'current_page' => $args['currentPage'] ?? 1,
        ];

        return self::CACHE_KEY.md5($this->serializer->serialize($keyData));
    }

    /**
     * Validate GraphQL arguments
     *
     * @param array $args
     * @param int $seasonId
     * @throws GraphQlInputException
     */
    private function validateArgs(array $args, int $seasonId): void
    {
        if ($seasonId <= 0) {
            throw new GraphQlInputException(__('Season ID must be a positive integer'));
        }

        if (isset($args['sortBy'])) {
            $allowedSortFields = [
                'rank',
                'points',
                'goals_for',
                'goals_against',
                'wins',
                'draws',
                'losses',
                'matches_played',
            ];
            if (!in_array($args['sortBy'], $allowedSortFields, true)) {
                throw new GraphQlInputException(
                    __('Invalid sortBy field. Allowed values: %1', implode(', ', $allowedSortFields))
                );
            }
        }

        if (isset($args['sortDirection'])) {
            $allowedDirections = ['ASC', 'DESC'];
            if (!in_array(strtoupper($args['sortDirection']), $allowedDirections, true)) {
                throw new GraphQlInputException(__('Invalid sortDirection. Allowed values: ASC, DESC'));
            }
        }

        if (isset($args['pageSize'])) {
            if (!is_int($args['pageSize']) || $args['pageSize'] <= 0 || $args['pageSize'] > 100) {
                throw new GraphQlInputException(__('pageSize must be a positive integer between 1 and 100'));
            }
        }

        if (isset($args['currentPage'])) {
            if (!is_int($args['currentPage']) || $args['currentPage'] <= 0) {
                throw new GraphQlInputException(__('currentPage must be a positive integer'));
            }
        }
    }
}
