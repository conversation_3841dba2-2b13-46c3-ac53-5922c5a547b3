<?php
declare(strict_types=1);

namespace Comave\LeagueGraphQl\Model\Resolver\LeagueClub;

use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;

class GoalDifference implements ResolverInterface
{
    /**
     * @inheritdoc
     */
    public function resolve(
        Field $field,
        $context,
        ResolveInfo $info,
        ?array $value = null,
        ?array $args = null
    ) {
        $goalsFor = $value['goals_for'] ?? 0;
        $goalsAgainst = $value['goals_against'] ?? 0;

        return (int)$goalsFor - (int)$goalsAgainst;
    }
}
