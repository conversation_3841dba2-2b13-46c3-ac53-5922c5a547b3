<?php
declare(strict_types=1);

namespace Comave\LeagueGraphQl\Model\Resolver\LeagueClub;

use Comave\Club\Api\ClubRepositoryInterface;
use Comave\ClubGraphQl\Model\ClubDto;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Exception\GraphQlNoSuchEntityException;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;

/**
 * GraphQL resolver for club data within league club context
 *
 * Resolves club information associated with league clubs, fetching complete club details
 * and transforming them into GraphQL-compatible format with error handling.
 */
class Club implements ResolverInterface
{
    /**
     * @param \Comave\Club\Api\ClubRepositoryInterface $clubRepository
     * @param \Comave\ClubGraphQl\Model\ClubDto $clubDto
     */
    public function __construct(
        private readonly ClubRepositoryInterface $clubRepository,
        private readonly ClubDto $clubDto
    ) {
    }

    /**
     * @inheritdoc
     */
    public function resolve(
        Field $field,
        $context,
        ResolveInfo $info,
        ?array $value = null,
        ?array $args = null
    ) {
        if (!isset($value['club_id'])) {
            throw new GraphQlNoSuchEntityException(__('Club ID is missing from league club data'));
        }

        try {
            $club = $this->clubRepository->get((int)$value['club_id']);

            $this->clubDto->setClub($club);
            $clubData = $this->clubDto->toArray();

            return [
                'club_id' => $clubData['club_id'],
                'name' => $clubData['name'],
                'description' => $clubData['description'],
                'logo' => $clubData['clogo'],
                'url_key' => $clubData['url_key'],
                'partnered' => $clubData['partnered'],
                'orgid' => $club->getData('orgid'),
                'uniqueid' => $clubData['uniqueid'],
                'website' => null, // Field doesn't exist in Club entity or database
                'status' => $clubData['status'],
                'subtitle' => $clubData['subtitle'] ?? null,
                'image' => $clubData['image'] ?? null,
                'position' => $clubData['position'] ?? null,
                'club_banner' => $clubData['club_banner'] ?? null,
                'club_prefix' => $clubData['club_prefix'] ?? null,
                'created_at' => $club->getData('creation_time'),
                'updated_at' => $club->getData('update_time'),
            ];
        } catch (NoSuchEntityException $e) {
            throw new GraphQlNoSuchEntityException(__('Club with ID "%1" does not exist.', $value['club_id']));
        }
    }
}