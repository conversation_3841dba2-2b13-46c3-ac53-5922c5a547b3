<?php
declare(strict_types=1);

namespace Comave\LeagueGraphQl\Model\Transformer;

use Comave\LeagueGraphQl\Model\Dto\AbstractDto;

/**
 * Abstract base class for all entity-to-DTO transformers
 * Provides common transformation utilities and patterns
 */
abstract class AbstractTransformer
{
    /**
     * Transform a single entity to DTO
     *
     * @param object $entity
     * @param array $context Additional context for transformation
     * @return AbstractDto
     */
    abstract public function transform(object $entity, array $context = []): AbstractDto;

    /**
     * Transform collection of entities to array of DTOs
     *
     * @param iterable $entities
     * @param array $context Additional context for transformation
     * @return array
     */
    public function transformCollection(iterable $entities, array $context = []): array
    {
        $result = [];

        foreach ($entities as $entity) {
            $result[] = $this->transform($entity, $context);
        }

        return $result;
    }

    /**
     * Format date string for GraphQL output
     *
     * @param string|null $date
     * @param string $format
     * @return string|null
     */
    protected function formatDate(?string $date, string $format = 'Y-m-d H:i:s'): ?string
    {
        if (empty($date)) {
            return null;
        }

        try {
            $dateTime = new \DateTime($date);

            return $dateTime->format($format);
        } catch (\Exception $e) {
            return $date;
        }
    }

    /**
     * Safely get entity property value
     *
     * @param object $entity
     * @param string $methodName
     * @param mixed $default
     * @return mixed
     */
    protected function getEntityValue(object $entity, string $methodName, $default = null)
    {
        if (method_exists($entity, $methodName)) {
            try {
                return $entity->$methodName();
            } catch (\Exception $e) {
                return $default;
            }
        }

        return $default;
    }

    /**
     * Convert entity to array using basic getter methods
     * Useful for simple transformations
     *
     * @param object $entity
     * @param array $fieldMapping Field name mapping (entityMethod => dtoField)
     * @return array
     */
    protected function extractBasicFields(object $entity, array $fieldMapping): array
    {
        $result = [];

        foreach ($fieldMapping as $method => $field) {
            $result[$field] = $this->getEntityValue($entity, $method);
        }

        return $result;
    }

    /**
     * Check if a specific field should be included based on context
     *
     * @param string $fieldName
     * @param array $context
     * @return bool
     */
    protected function shouldIncludeField(string $fieldName, array $context): bool
    {
        if (empty($context['fields'])) {
            return true;
        }

        return in_array($fieldName, $context['fields'], true);
    }

    /**
     * Extract pagination context from GraphQL arguments
     *
     * @param array $args
     * @return array
     */
    protected function extractPaginationContext(array $args): array
    {
        return [
            'page_size' => $args['pageSize'] ?? 20,
            'current_page' => $args['currentPage'] ?? 1,
        ];
    }

    /**
     * Extract filter context from GraphQL arguments
     *
     * @param array $args
     * @return array
     */
    protected function extractFilterContext(array $args): array
    {
        return $args['filter'] ?? [];
    }
}
