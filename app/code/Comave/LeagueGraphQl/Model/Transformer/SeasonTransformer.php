<?php
declare(strict_types=1);

namespace Comave\LeagueGraphQl\Model\Transformer;

use Comave\League\Api\Data\SeasonInterface;
use Comave\LeagueGraphQl\Model\Dto\AbstractDto;
use Comave\LeagueGraphQl\Model\Dto\SeasonDto;
use Comave\LeagueGraphQl\Model\Dto\SeasonListDto;
use Magento\Framework\Api\SearchResultsInterface;

/**
 * Season Entity to DTO Transformer
 * Converts Season entities to SeasonDto objects for GraphQL responses
 */
class SeasonTransformer extends AbstractTransformer
{
    /**
     * @param \Comave\LeagueGraphQl\Model\Transformer\LeagueTransformer $leagueTransformer
     */
    public function __construct(
        private readonly LeagueTransformer $leagueTransformer
    ) {
    }

    /**
     * Transform Season entity to SeasonDto
     *
     * @param SeasonInterface $entity
     * @param array $context
     * @return SeasonDto
     */
    public function transform(object $entity, array $context = []): AbstractDto
    {
        if (!$entity instanceof SeasonInterface) {
            throw new \InvalidArgumentException('Entity must be instance of SeasonInterface');
        }

        $seasonData = [
            'seasonId' => (int)$entity->getId(),
            'name' => (string)$entity->getName(),
            'leagueId' => (int)$entity->getLeagueId(),
            'maxClubs' => $entity->getMaxClubs() ? (int)$entity->getMaxClubs() : null,
            'startDate' => $this->formatDate($entity->getStartDate()),
            'endDate' => $this->formatDate($entity->getEndDate()),
            'isCurrent' => $entity->getIsCurrent() ? (int)$entity->getIsCurrent() : null,
            'createdAt' => $this->formatDate($entity->getCreatedAt()),
            'updatedAt' => $this->formatDate($entity->getUpdatedAt()),
        ];

        if ($this->shouldIncludeField('league', $context) && !empty($context['league_data'][$entity->getLeagueId()])) {
            $leagueEntity = $context['league_data'][$entity->getLeagueId()];
            $seasonData['league'] = $this->leagueTransformer->transform($leagueEntity, $context);
        }

        if ($this->shouldIncludeField('clubs', $context) && !empty($context['clubs_data'][$entity->getId()])) {
            $seasonData['clubs'] = $context['clubs_data'][$entity->getId()];
        }

        if ($this->shouldIncludeField(
                'partnered_clubs',
                $context
            ) && !empty($context['partnered_clubs_data'][$entity->getId()])) {
            $seasonData['partneredClubs'] = $context['partnered_clubs_data'][$entity->getId()];
        }

        if ($this->shouldIncludeField(
                'non_partnered_clubs',
                $context
            ) && !empty($context['non_partnered_clubs_data'][$entity->getId()])) {
            $seasonData['nonPartneredClubs'] = $context['non_partnered_clubs_data'][$entity->getId()];
        }

        return new SeasonDto(...$seasonData);
    }

    /**
     * Transform search results to SeasonListDto
     *
     * @param SearchResultsInterface $searchResults
     * @param array $context
     * @return SeasonListDto
     */
    public function transformSearchResults(SearchResultsInterface $searchResults, array $context = []): SeasonListDto
    {
        $seasonDtos = [];

        foreach ($searchResults->getItems() as $season) {
            $seasonDtos[] = $this->transform($season, $context);
        }

        $paginationContext = $this->extractPaginationContext($context);

        return SeasonListDto::fromSearchResults(
            $searchResults,
            $seasonDtos,
            $paginationContext
        );
    }

    /**
     * Transform Season entity to minimal DTO (for nested references)
     *
     * @param SeasonInterface $entity
     * @return SeasonDto
     */
    public function transformMinimal(SeasonInterface $entity): SeasonDto
    {
        return SeasonDto::minimal(
            (int)$entity->getId(),
            (string)$entity->getName(),
            (int)$entity->getLeagueId()
        );
    }

    /**
     * Prepare context for bulk transformation with league data
     *
     * @param array $seasons
     * @param array $leagueData
     * @return array
     */
    public function prepareContextWithLeagues(array $seasons, array $leagueData = []): array
    {
        $context = [
            'league_data' => [],
        ];

        foreach ($leagueData as $league) {
            if (method_exists($league, 'getId')) {
                $context['league_data'][$league->getId()] = $league;
            }
        }

        return $context;
    }

    /**
     * Create context for transformation with field selection
     *
     * @param array $requestedFields GraphQL field selection
     * @param array $additionalContext
     * @return array
     */
    public function createContext(array $requestedFields = [], array $additionalContext = []): array
    {
        return array_merge([
            'fields' => $requestedFields,
        ], $additionalContext);
    }
}
