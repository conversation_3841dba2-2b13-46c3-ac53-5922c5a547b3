<?php
declare(strict_types=1);

namespace Comave\LeagueGraphQl\Model\Transformer;

use Comave\League\Api\Data\LeagueInterface;
use Comave\LeagueGraphQl\Model\Dto\AbstractDto;
use Comave\LeagueGraphQl\Model\Dto\LeagueDto;

/**
 * League Entity to DTO Transformer
 * Converts League entities to LeagueDto objects for GraphQL responses
 */
class LeagueTransformer extends AbstractTransformer
{
    /**
     * Transform League entity to LeagueDto
     *
     * @param LeagueInterface $entity
     * @param array $context
     * @return LeagueDto
     */
    public function transform(object $entity, array $context = []): AbstractDto
    {
        if (!$entity instanceof LeagueInterface) {
            throw new \InvalidArgumentException('Entity must be instance of LeagueInterface');
        }

        return new LeagueDto(
            leagueId: (int)$entity->getId(),
            name: (string)$entity->getName(),
            code: $entity->getCode(),
            description: $entity->getDescription(),
            countryCode: $entity->getCountryCode(),
            logo: $entity->getLogo(),
            status: $entity->getStatus() ? (int)$entity->getStatus() : null
        );
    }

    /**
     * Transform League entity to minimal DTO (for nested references)
     *
     * @param LeagueInterface $entity
     * @return LeagueDto
     */
    public function transformMinimal(LeagueInterface $entity): LeagueDto
    {
        return LeagueDto::minimal(
            (int)$entity->getId(),
            (string)$entity->getName()
        );
    }
}