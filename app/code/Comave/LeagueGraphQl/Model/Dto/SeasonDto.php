<?php
declare(strict_types=1);

namespace Comave\LeagueGraphQl\Model\Dto;

/**
 * Season Data Transfer Object
 * Represents a season entity optimized for GraphQL responses
 */
class SeasonDto extends AbstractDto
{
    /**
     * Constructor for SeasonDto
     * 
     * Creates a season data transfer object with comprehensive season information
     * including league associations, club relationships, and timing data.
     * 
     * @param int $seasonId Unique identifier for the season
     * @param string $name Season name or title
     * @param int $leagueId ID of the league this season belongs to
     * @param int|null $maxClubs Maximum number of clubs allowed in this season
     * @param string|null $startDate Season start date (ISO format)
     * @param string|null $endDate Season end date (ISO format)
     * @param int|null $isCurrent Flag indicating if this is the current active season (1=current, 0=not current)
     * @param string|null $createdAt Timestamp when season was created
     * @param string|null $updatedAt Timestamp when season was last updated
     * @param LeagueDto|null $league Complete league object this season belongs to
     * @param array|null $clubs Array of all clubs participating in this season
     * @param array|null $partneredClubs Array of partnered clubs in this season
     * @param array|null $nonPartneredClubs Array of non-partnered clubs in this season
     */
    public function __construct(
        public readonly int $seasonId,
        public readonly string $name,
        public readonly int $leagueId,
        public readonly ?int $maxClubs = null,
        public readonly ?string $startDate = null,
        public readonly ?string $endDate = null,
        public readonly ?int $isCurrent = null,
        public readonly ?string $createdAt = null,
        public readonly ?string $updatedAt = null,
        public readonly ?LeagueDto $league = null,
        public readonly ?array $clubs = null,
        public readonly ?array $partneredClubs = null,
        public readonly ?array $nonPartneredClubs = null
    ) {
    }

    /**
     * Create a minimal Season DTO with only basic fields
     * 
     * Factory method to create a lightweight SeasonDto instance containing only
     * the essential fields (ID, name, league ID). All optional fields are set to null.
     * Useful for scenarios where only basic season information is needed.
     *
     * @param int $seasonId Unique identifier for the season
     * @param string $name Season name or title
     * @param int $leagueId ID of the league this season belongs to
     * @return self New SeasonDto instance with minimal data
     */
    public static function minimal(int $seasonId, string $name, int $leagueId): self
    {
        return new self(
            seasonId: $seasonId,
            name: $name,
            leagueId: $leagueId
        );
    }

    /**
     * Check if this season is marked as current
     *
     * @return bool
     */
    public function isCurrentSeason(): bool
    {
        return $this->isCurrent === 1;
    }

    /**
     * Get season name with league name if available
     *
     * @return string
     */
    public function getDisplayName(): string
    {
        if ($this->league !== null) {
            return $this->league->name.' - '.$this->name;
        }

        return $this->name;
    }

    /**
     * Get the number of clubs in this season
     *
     * @return int
     */
    public function getClubCount(): int
    {
        if ($this->clubs !== null) {
            return count($this->clubs);
        }

        return $this->maxClubs ?? 0;
    }

    /**
     * Get the number of partnered clubs in this season
     *
     * @return int
     */
    public function getPartneredClubCount(): int
    {
        if ($this->partneredClubs !== null) {
            return count($this->partneredClubs);
        }

        return 0;
    }

    /**
     * Override toArray to handle special field naming for GraphQL
     *
     * @return array
     */
    public function toArray(): array
    {
        $result = [
            'season_id' => $this->seasonId,
            'name' => $this->name,
            'league_id' => $this->leagueId,
            'max_clubs' => $this->maxClubs,
            'start_date' => $this->startDate,
            'end_date' => $this->endDate,
            'is_current' => $this->isCurrent,
            'created_at' => $this->createdAt,
            'updated_at' => $this->updatedAt,
        ];

        // Include nested objects only if they exist
        if ($this->league !== null) {
            $result['league'] = $this->league->toArray();
        }

        if ($this->clubs !== null) {
            $result['clubs'] = array_map(
                fn($club) => $club instanceof AbstractDto ? $club->toArray() : $club,
                $this->clubs
            );
        }

        if ($this->partneredClubs !== null) {
            $result['partnered_clubs'] = array_map(
                fn($club) => $club instanceof AbstractDto ? $club->toArray() : $club,
                $this->partneredClubs
            );
        }

        if ($this->nonPartneredClubs !== null) {
            $result['non_partnered_clubs'] = array_map(
                fn($club) => $club instanceof AbstractDto ? $club->toArray() : $club,
                $this->nonPartneredClubs
            );
        }

        return $result;
    }
}
