<?php
declare(strict_types=1);

namespace Comave\LeagueGraphQl\Model\Dto;

/**
 * League Data Transfer Object
 * Represents a league entity optimized for GraphQL responses
 */
class LeagueDto extends AbstractDto
{
    /**
     * @param int $leagueId
     * @param string $name
     * @param string|null $code
     * @param string|null $description
     * @param string|null $countryCode
     * @param string|null $logo
     * @param int|null $status
     */
    public function __construct(
        public readonly int $leagueId,
        public readonly string $name,
        public readonly ?string $code = null,
        public readonly ?string $description = null,
        public readonly ?string $countryCode = null,
        public readonly ?string $logo = null,
        public readonly ?int $status = null
    ) {
    }

    /**
     * Create a minimal League DTO with only basic fields
     *
     * @param int $leagueId
     * @param string $name
     * @return self
     */
    public static function minimal(int $leagueId, string $name): self
    {
        return new self(
            leagueId: $leagueId,
            name: $name
        );
    }

    /**
     * Check if league is active
     *
     * @return bool
     */
    public function isActive(): bool
    {
        return $this->status === 1;
    }

    /**
     * Get display name with country code if available
     *
     * @return string
     */
    public function getDisplayName(): string
    {
        if ($this->countryCode !== null) {
            return sprintf('%s (%s)', $this->name, $this->countryCode);
        }

        return $this->name;
    }

    /**
     * Override toArray to handle special field naming for GraphQL
     *
     * @return array
     */
    public function toArray(): array
    {
        return [
            'league_id' => $this->leagueId,
            'name' => $this->name,
            'code' => $this->code,
            'description' => $this->description,
            'country_code' => $this->countryCode,
            'logo' => $this->logo,
            'status' => $this->status,
        ];
    }
}
