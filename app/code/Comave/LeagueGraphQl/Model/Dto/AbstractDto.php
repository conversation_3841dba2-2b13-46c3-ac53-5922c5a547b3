<?php
declare(strict_types=1);

namespace Comave\LeagueGraphQl\Model\Dto;

/**
 * Abstract base class for all DTO objects
 * Provides common functionality and ensures consistent behavior
 */
abstract class AbstractDto
{
    /**
     * Convert DTO to array representation
     * Used for GraphQL response serialization
     *
     * @return array
     */
    public function toArray(): array
    {
        $result = [];
        $reflection = new \ReflectionClass($this);

        foreach ($reflection->getProperties() as $property) {
            if ($property->isPublic()) {
                $value = $property->getValue($this);
                $key = $this->convertPropertyName($property->getName());

                if ($value instanceof AbstractDto) {
                    $result[$key] = $value->toArray();
                } elseif (is_array($value)) {
                    $result[$key] = $this->convertArrayToOutput($value);
                } else {
                    $result[$key] = $value;
                }
            }
        }

        return $result;
    }

    /**
     * Convert property name from camelCase to snake_case for GraphQL compatibility
     *
     * @param string $propertyName
     * @return string
     */
    protected function convertPropertyName(string $propertyName): string
    {
        return strtolower(preg_replace('/(?<!^)[A-Z]/', '_$0', $propertyName));
    }

    /**
     * Convert array values to appropriate output format
     *
     * @param array $array
     * @return array
     */
    protected function convertArrayToOutput(array $array): array
    {
        $result = [];

        foreach ($array as $key => $value) {
            if ($value instanceof AbstractDto) {
                $result[$key] = $value->toArray();
            } elseif (is_array($value)) {
                $result[$key] = $this->convertArrayToOutput($value);
            } else {
                $result[$key] = $value;
            }
        }

        return $result;
    }

    /**
     * Convert DTO to JSON string
     *
     * @return string
     */
    public function toJson(): string
    {
        return json_encode($this->toArray(), JSON_THROW_ON_ERROR);
    }

    /**
     * Magic method to prevent modification of DTO properties
     *
     * @param string $name
     * @param mixed $value
     * @throws \BadMethodCallException
     */
    public function __set(string $name, $value): void
    {
        throw new \BadMethodCallException('DTO objects are immutable');
    }
}
