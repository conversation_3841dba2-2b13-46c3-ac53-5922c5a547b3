<?php
declare(strict_types=1);

namespace Comave\LeagueGraphQl\Model\Dto;

/**
 * Season List Data Transfer Object
 * Represents a paginated collection of seasons for GraphQL responses
 */
class SeasonListDto extends AbstractDto
{
    /**
     * @param array $items
     * @param int $totalCount
     * @param int|null $pageSize
     * @param int|null $currentPage
     */
    public function __construct(
        public readonly array $items,
        public readonly int $totalCount,
        public readonly ?int $pageSize = null,
        public readonly ?int $currentPage = null
    ) {
    }

    /**
     * Create from search result interface
     *
     * @param \Magento\Framework\Api\SearchResultsInterface $searchResults
     * @param array $seasonDtos Array of SeasonDto objects
     * @param array $context Additional context (page info, etc.)
     * @return self
     */
    public static function fromSearchResults(
        \Magento\Framework\Api\SearchResultsInterface $searchResults,
        array $seasonDtos,
        array $context = []
    ): self {
        return new self(
            items: $seasonDtos,
            totalCount: (int)$searchResults->getTotalCount(),
            pageSize: $context['pageSize'] ?? null,
            currentPage: $context['currentPage'] ?? null
        );
    }

    /**
     * Get the number of items in current page
     *
     * @return int
     */
    public function getItemCount(): int
    {
        return count($this->items);
    }

    /**
     * Check if there are more pages
     *
     * @return bool
     */
    public function hasMorePages(): bool
    {
        if ($this->pageSize === null || $this->currentPage === null) {
            return false;
        }

        $totalPages = (int)ceil($this->totalCount / $this->pageSize);

        return $this->currentPage < $totalPages;
    }

    /**
     * Get total number of pages
     *
     * @return int|null
     */
    public function getTotalPages(): ?int
    {
        if ($this->pageSize === null || $this->pageSize === 0) {
            return null;
        }

        return (int)ceil($this->totalCount / $this->pageSize);
    }

    /**
     * Check if list is empty
     *
     * @return bool
     */
    public function isEmpty(): bool
    {
        return empty($this->items);
    }

    /**
     * Override toArray to handle items serialization
     *
     * @return array
     */
    public function toArray(): array
    {
        return [
            'items' => array_map(
                fn($item) => $item instanceof AbstractDto ? $item->toArray() : $item,
                $this->items
            ),
            'total_count' => $this->totalCount,
        ];
    }
}
