<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    
    <!-- Inject ClubGraphQL DTO into LeagueGraphQL Club Resolver -->
    <type name="Comave\LeagueGraphQl\Model\Resolver\LeagueClub\Club">
        <arguments>
            <argument name="clubDto" xsi:type="object">Comave\ClubGraphQl\Model\ClubDto</argument>
        </arguments>
    </type>
    
</config>