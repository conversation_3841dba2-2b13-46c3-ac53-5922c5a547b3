type Query {
    getSeasons(filter: SeasonFilterInput, pageSize: Int = 20, currentPage: Int = 1): SeasonList @resolver(class: "Comave\\LeagueGraphQl\\Model\\Resolver\\Seasons") @doc(description: "Get list of seasons with optional filtering and pagination")
    getSeason(id: Int!): Season @resolver(class: "Comave\\LeagueGraphQl\\Model\\Resolver\\Season") @doc(description: "Get a specific season by ID")
    getLeagueClubs(season_id: Int!, partnered: Int, pageSize: Int = 20, currentPage: Int = 1): LeagueClubList @resolver(class: "Comave\\LeagueGraphQl\\Model\\Resolver\\LeagueClubs") @doc(description: "Get clubs in a league season with optional partnership filtering and pagination")
}

type SeasonList @doc(description: "Contains an array of seasons") {
    items: [Season] @doc(description: "An array of seasons")
    total_count: Int @doc(description: "The total number of seasons")
}

type Season @doc(description: "Season information") {
    season_id: Int! @doc(description: "Season ID")
    name: String! @doc(description: "Season name")
    league: League! @resolver(class: "Comave\\LeagueGraphQl\\Model\\Resolver\\Season\\League") @doc(description: "League information")
    max_clubs: Int @doc(description: "Maximum number of clubs in season")
    start_date: String @doc(description: "Season start date")
    end_date: String @doc(description: "Season end date")
    is_current: Int @doc(description: "Whether this is the current season")
    clubs: [LeagueClub] @resolver(class: "Comave\\LeagueGraphQl\\Model\\Resolver\\Season\\Clubs") @doc(description: "All clubs in the season")
    partnered_clubs: [LeagueClub] @resolver(class: "Comave\\LeagueGraphQl\\Model\\Resolver\\Season\\PartneredClubs") @doc(description: "Partnered clubs in the season")
    non_partnered_clubs: [LeagueClub] @resolver(class: "Comave\\LeagueGraphQl\\Model\\Resolver\\Season\\NonPartneredClubs") @doc(description: "Non-partnered clubs in the season")
    created_at: String @doc(description: "Season creation timestamp")
    updated_at: String @doc(description: "Season last update timestamp")
}

type League @doc(description: "League reference information") {
    league_id: Int! @doc(description: "League ID")
    name: String! @doc(description: "League name")
    code: String @doc(description: "League code")
    description: String @doc(description: "League description")
    country_code: String @doc(description: "Country code")
    logo: String @doc(description: "League logo URL")
    status: Int @doc(description: "League status")
}

type LeagueClubList @doc(description: "Contains an array of league clubs") {
    items: [LeagueClub] @doc(description: "An array of league clubs")
    total_count: Int @doc(description: "The total number of league clubs")
}

type LeagueClub @doc(description: "League club with statistics") {
    league_club_id: Int! @doc(description: "League club ID")
    club: Club! @resolver(class: "Comave\\LeagueGraphQl\\Model\\Resolver\\LeagueClub\\Club") @doc(description: "Club information")
    rank: Int @doc(description: "Club rank in the league")
    points: Int @doc(description: "Club points")
    matches_played: Int @doc(description: "Number of matches played")
    wins: Int @doc(description: "Number of wins")
    draws: Int @doc(description: "Number of draws")
    losses: Int @doc(description: "Number of losses")
    goals_for: Int @doc(description: "Goals scored")
    goals_against: Int @doc(description: "Goals conceded")
    goal_difference: Int @resolver(class: "Comave\\LeagueGraphQl\\Model\\Resolver\\LeagueClub\\GoalDifference") @doc(description: "Goal difference (goals_for - goals_against)")
    created_at: String @doc(description: "Record creation timestamp")
    updated_at: String @doc(description: "Record last update timestamp")
}

type Club @doc(description: "Club information") {
    club_id: Int! @doc(description: "Club ID")
    name: String! @doc(description: "Club name")
    description: String @doc(description: "Club description")
    logo: String @doc(description: "Club logo URL")
    url_key: String @doc(description: "Club URL key")
    partnered: Int @doc(description: "Whether club is partnered (1 = yes, 0 = no)")
    orgid: String @doc(description: "Organization ID")
    uniqueid: String @doc(description: "Unique identifier")
    website: String @doc(description: "Club website URL (deprecated - always null)")
    status: Int @doc(description: "Club status")
    subtitle: String @doc(description: "Club subtitle")
    image: String @doc(description: "Club image URL")
    position: Int @doc(description: "Club position/ranking")
    club_banner: String @doc(description: "Club banner image URL")
    club_prefix: String @doc(description: "Club prefix code")
    created_at: String @doc(description: "Club creation timestamp")
    updated_at: String @doc(description: "Club last update timestamp")
}

input SeasonFilterInput @doc(description: "Season filter input") {
    league_id: Int @doc(description: "Filter by league ID")
    is_current: Int @doc(description: "Filter by current season status")
    name: String @doc(description: "Filter by season name")
}