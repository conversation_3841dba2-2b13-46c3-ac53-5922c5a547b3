<?php

declare(strict_types=1);

namespace Comave\SellerStatusOnboarding\Controller\Seller;

use Comave\SellerStatusOnboarding\Model\OnboardingSaveHandler;
use Magento\Framework\App\Action\Action;
use Magento\Framework\App\Action\Context;
use Magento\Framework\App\Action\HttpPostActionInterface;
use Magento\Framework\Controller\Result\Json;
use Magento\Framework\Controller\ResultFactory;
use Psr\Log\LoggerInterface;

class Save extends Action implements HttpPostActionInterface
{
    /**
     * @param OnboardingSaveHandler $onboardingSaveHandler
     * @param LoggerInterface $logger
     * @param Context $context
     */
    public function __construct(
        private readonly OnboardingSaveHandler $onboardingSaveHandler,
        private readonly LoggerInterface $logger,
        Context $context
    ) {
        parent::__construct($context);
    }

    /**
     * @return Json
     */
    public function execute(): Json
    {
        $result = $this->resultFactory->create(ResultFactory::TYPE_JSON);

        if (!$this->getRequest()->isAjax()) {
            return $result->setData([
                'success' => true,
                'message' => __('Invalid request')
            ]);
        }

        try {
            $this->onboardingSaveHandler->save($this->getRequest());

            return $result->setData([
                'success' => true,
                'message' => __('Successfully saved your information')
            ]);
        } catch (\Exception $e) {
            $this->logger->error(
                'Unable to save onboarding data',
                [
                    'message' => $e->getMessage(),
                    'trace' => $e->getTrace()
                ]
            );

            return $result->setData([
                'success' => false,
                'message' => __('There was a problem processing your request')
            ]);
        }
    }
}
