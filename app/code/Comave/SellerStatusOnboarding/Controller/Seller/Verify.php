<?php

declare(strict_types=1);

namespace Comave\SellerStatusOnboarding\Controller\Seller;

use Comave\SellerPayouts\Controller\Seller\Verify as CoreController;
use Magento\Framework\Controller\Result\Redirect;
use Magento\Framework\Controller\ResultFactory;

class Verify extends CoreController
{
    /**
     * @return Redirect
     */
    public function execute(): Redirect
    {
        parent::execute();
        $resultRedirect = $this->resultFactory->create(ResultFactory::TYPE_REDIRECT);

        return $resultRedirect->setPath('marketplace/account/dashboard');
    }
}
