<?php

declare(strict_types=1);

namespace Comave\SellerStatusOnboarding\Controller\Seller;

use Comave\SellerPayouts\Controller\Seller\Payouts as CoreController;
use Magento\Framework\Controller\Result\Redirect;
use Magento\Framework\Controller\ResultFactory;

class Payouts extends CoreController
{
    /**
     * @return Redirect
     */
    public function execute(): Redirect
    {
        parent::execute();
        $resultRedirect = $this->resultFactory->create(ResultFactory::TYPE_REDIRECT);

        return $resultRedirect->setPath('marketplace/account/dashboard');
    }
}
