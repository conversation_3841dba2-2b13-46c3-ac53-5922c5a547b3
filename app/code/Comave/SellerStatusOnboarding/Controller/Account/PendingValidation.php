<?php

declare(strict_types=1);

namespace Comave\SellerStatusOnboarding\Controller\Account;

use Comave\SellerStatus\Model\Command\SellerCompanyProvider;
use Magento\Company\Api\CompanyUserRoleManagementInterface;
use Magento\Customer\Controller\AccountInterface;
use Magento\Customer\Helper\Session\CurrentCustomer;
use Magento\Framework\App\Action\Action;
use Magento\Framework\App\Action\Context;
use Magento\Framework\App\Action\HttpGetActionInterface;
use Magento\Framework\Controller\ResultFactory;
use Magento\Framework\View\Result\Page;

class PendingValidation extends Action implements HttpGetActionInterface, AccountInterface
{
    /**
     * @param Context $context
     * @param CurrentCustomer $currentCustomer
     * @param SellerCompanyProvider $sellerCompanyProvider
     * @param CompanyUserRoleManagementInterface $companyUserRoleManagement
     */
    public function __construct(
        Context $context,
        private readonly CurrentCustomer $currentCustomer,
        private readonly SellerCompanyProvider $sellerCompanyProvider,
        private readonly CompanyUserRoleManagementInterface $companyUserRoleManagement
    ) {
        parent::__construct($context);
    }

    /**
     * @return Page
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function execute(): Page
    {
        $userRoles = $this->companyUserRoleManagement->getRolesForCompanyUser(
            (int) $this->currentCustomer->getCustomerId(),
            (int) $this->sellerCompanyProvider->get()->getId()
        );
        $currentRole = current($userRoles ?? []);

        if (!empty($currentRole) && $currentRole->getRoleName() !== \Comave\SellerStatus\Model\RoleValidator\Onboarding::ROLE_NAME) {
            $this->_redirect('marketplace/account/dashboard');
        }

        $this->messageManager->getMessages(true);
        $page = $this->resultFactory->create(ResultFactory::TYPE_PAGE);
        $page->getConfig()->getTitle()->set('Pending onboarding validation');

        return $page;
    }
}
