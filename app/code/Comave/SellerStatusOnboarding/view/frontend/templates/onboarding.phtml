<?php
/** @var \Magento\Framework\View\Element\Template $block */
$onboardingVm = $block->getData('onboardingViewModel');
$jsonConfig = '{}';

if ($onboardingVm instanceof \Comave\SellerStatusOnboarding\ViewModel\OnboardingData) {
    $jsonConfig = $onboardingVm->getOnboardingSellerData();
}
?>
<div id="onboarding-container" data-bind="scope: 'onboarding'">
    <!-- ko template: getTemplate() --><!-- /ko -->
</div>
<script type="text/javascript">
    window.onboardingConfig = <?= /** @noEscape */ $jsonConfig; ?>
</script>
<script type="text/x-magento-init">
    {
        "#onboarding-container": {
            "Magento_Ui/js/core/app": {
                "components": {
                    "onboarding": {
                        "component": "Comave_SellerStatusOnboarding/js/view/steps"
                    },
                    "onboarding_1": {
                        "component": "Comave_SellerStatusOnboarding/js/view/onboarding_1"
                    },
                    "onboarding_2": {
                        "component": "Comave_SellerStatusOnboarding/js/view/onboarding_2"
                    },
                    "onboarding_3": {
                        "component": "Comave_SellerStatusOnboarding/js/view/onboarding_3"
                    }
                }
            }
        }
    }
</script>
<style>
    #onboarding-container {
        display: flex;
        justify-content: center;
        padding-bottom: 25px;
    }
</style>
