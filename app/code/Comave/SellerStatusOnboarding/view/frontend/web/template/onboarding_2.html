<div class="stripe-connect-container">
    <div class="content" data-bind="html: onboardingData().step_data.html"></div>
    <div class="action-buttons">
        <button class="action primary" type='button' data-bind="i18n: 'Back', click: navigatePrev"></button>
        <button class="action primary" type='button' data-bind="click: navigateNext, i18n: 'Next'"></button>
    </div>
</div>

<style>
    .stripe-connect-container {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
    }
    .stripe-connect-container .content {
        border: 3px solid;
        padding: 25px;
        margin: 25px;
        border-radius: 20px;
    }

    .stripe-connect-container #stripe_icon {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 18px;
    }

    .stripe-connect-container #stripe-connect-form-seller {
        display: flex;
        flex-direction: column;
        gap: 30px;
    }

    .stripe-connect-container .stripe_create_button, .stripe-connect-container .stripe_verify_button {
        display: flex;
        justify-content: center;
    }

    .stripe-connect-container .stripe_create_button button, .stripe-connect-container .stripe_verify_button button{
        background-color: #2c379c;
        color: white;
    }
</style>
