<div class="shipping-return-container">
    <form data-bind="attr: {id: 'onboarding_3'}" data-mage-init='{"validation":{}}'>
        <!-- ko foreach: { data: onboardingData, as: 'step' } -->
            <div class="fieldset" data-mage-init='{"collapsible": {}}'  data-role="collapsible" data-collapsible="true" data-collapsible-open="true">
                <legend class="legend">
                    <span data-bind="text: $parent.getText(step.step_name)"></span>
                </legend>
                <!-- ko foreach: { data: Object.keys(step.step_data), as: 'dataKey' } -->
                    <!-- ko ifnot: _.contains($parents[1].nonTypicalInputs, dataKey) -->
                        <div class="field required">
                            <label data-bind="attr: {class: 'label', for: dataKey}">
                                <span data-bind="text: dataKey.replaceAll('_', ' ').toUpperCase()"></span></label>
                            <div class="control">
                                <input data-bind="{attr: {name: dataKey, id: dataKey, value: step.step_data[dataKey] || ''}}"
                                       type="text"
                                       class="input-text required-entry" />
                            </div>
                        </div>
                    <!-- /ko -->
                    <!-- ko if: dataKey === 'countries' -->
                    <div class="field required">
                        <label data-bind="attr: {class: 'label', for: 'countries'}">
                            <span data-bind="i18n: 'AVAILABLE COUNTRIES'"></span>
                        </label>
                        <div class="control">
                            <select id="countries"
                                    name="countries[]"
                                    multiple
                                    class="select required-entry">
                            </select>
                        </div>
                    </div>
                    <!-- /ko -->
                    <!-- ko if: dataKey === 'country' -->
                    <div class="field required">
                        <label data-bind="attr: {class: 'label', for: dataKey}">
                            <span data-bind="i18n: dataKey.toUpperCase()"></span>
                        </label>
                        <div class="control">
                            <select data-bind="options: $parents[1].selectOptions,
                                           optionsText: 'label',
                                           optionsValue: 'value',
                                           optionsCaption: 'Please select',
                                           value: $parents[1].selectedCountry,
                                           attr: {id: dataKey, name: dataKey, class: 'required-entry select'}">
                            </select>
                        </div>
                    </div>
                    <!-- /ko -->
                    <!-- ko if: dataKey === 'region' -->
                    <div class="field required">
                        <label data-bind="attr: {class: 'label', for: dataKey}">
                            <span data-bind="i18n: dataKey.toUpperCase()"></span>
                        </label>
                        <div class="control">
                            <select name="region_id" data-bind="options: $parents[1].selectRegionOptions,
                                           optionsText: 'label',
                                           optionsValue: 'value',
                                           optionsCaption: 'Please select',
                                           value: $parents[1].selectedRegion,
                                           visible: $parents[1].showRegionTextInput() === false,
                                           required: $parents[1].showRegionTextInput() === false,
                                           disabled: $parents[1].showRegionTextInput() === true,
                                           attr: {id: dataKey}">
                            </select>
                            <input data-bind="visible: $parents[1].showRegionTextInput() === true,
                                           required: $parents[1].showRegionTextInput() === true,
                                           value: $parents[1].regionTextValue()" type="text" class="input-text" name="region"/>
                        </div>
                    </div>
                    <!-- /ko -->
                    <!-- ko if: dataKey === 'total_lead_time' -->
                        <div class="fieldset lead-container">
                            <div class="field required">
                                <label data-bind="attr: {class: 'label', for: 'packing_time'}">
                                    <span data-bind="i18n: 'Packing Time'"></span>
                                </label>
                                <div class="control">
                                    <input id="packing_time" data-bind="value: $parents[1].packingTime" type="number" value="0" step="1" name="packing_time" class="input-text required-entry"/>
                                </div>
                            </div>
                            <div class="field required">
                                <label data-bind="attr: {class: 'label', for: 'delivery_time'}">
                                    <span data-bind="i18n: 'Delivery Time'"></span>
                                </label>
                                <div class="control">
                                    <input id='delivery_time' data-bind="value: $parents[1].deliveryTime" type="number" value="0" step="1" name="delivery_time" class="input-text required-entry"/>
                                </div>
                            </div>
                            <div class="field required">
                                <label data-bind="attr: {class: 'label', for: 'total_lead_time'}">
                                    <span data-bind="i18n: 'Total Lead Time'"></span>
                                </label>
                                <div class="control">
                                    <input data-bind="value: $parents[1].leadTime" id='total_lead_time' type="number" readonly step="1" name="total_lead_time" class="input-text required-entry"/>
                                </div>
                            </div>
                        </div>
                    <!-- /ko -->
                    <!-- ko if: dataKey === 'service_options' -->
                    <div class="field required">
                        <label data-bind="attr: {class: 'label', for: 'services'}">
                            <span data-bind="i18n: 'Available services'"></span>
                        </label>
                        <div class="control">
                            <select id="services"
                                    name="service"
                                    data-bind="value: $parents[1].selectedService"
                                    class="select required-entry">
                                <!-- ko foreach: { data: step.step_data[dataKey], as: 'service' } -->
                                    <option data-bind="text: service.label, value: service.value"></option>
                                <!-- /ko -->
                            </select>
                        </div>
                    </div>
                    <!-- /ko -->
                <!-- /ko -->
            </div>
        <!-- /ko -->
        <button class="action primary" type='button' data-bind='click: saveStep, i18n: "Complete onboarding"'></button>
    </form>
    <button class="action primary" type='button' data-bind="i18n: 'Back', click: navigatePrev"></button>
</div>

<style>
    #onboarding_3 {
        padding-bottom: 10px;
    }
    .lead-container {
        display: flex;
        gap: 2rem;
    }
</style>
