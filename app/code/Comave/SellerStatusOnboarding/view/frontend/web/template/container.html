<div class="step-navigator">
    <ul class="opc-progress-bar">
        <!-- ko foreach: { data: steps().sort(sortItems), as: 'item' } -->
        <li class="opc-progress-bar-item" data-bind="css: item.isVisible() ? '_active' : ($parent.isComplete(item.code) ? '_complete' : '')">
            <span data-bind="i18n: item.title, click: $parent.navigate"></span>
        </li>
        <!-- /ko -->
    </ul>
    <!-- ko foreach: { data: steps().sort(sortItems), as: 'item' } -->
        <!-- ko if: isVisible -->
            <div data-bind="scope: item.code">
                <!-- ko template: getTemplate() --><!-- /ko -->
            </div>
        <!-- /ko -->
    <!-- /ko -->
</div>
