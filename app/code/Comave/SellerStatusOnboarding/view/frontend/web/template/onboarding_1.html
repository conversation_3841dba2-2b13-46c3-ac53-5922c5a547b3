<div class="business_details" id="business_details">
    <form data-bind="attr: {id: 'onboarding_1'}" data-mage-init='{"validation":{}}'>
        <!-- ko foreach: { data: onboardingData, as: 'step' } -->
            <div class="fieldset" data-mage-init='{"collapsible": {}}'  data-role="collapsible" data-collapsible="true" data-collapsible-open="true">
                <legend class="legend">
                    <span data-bind="text: $parent.getText(step.step_name)"></span>
                </legend>
                <!-- ko foreach: { data: Object.keys(step.step_data), as: 'dataKey' } -->
                    <!-- ko ifnot: _.contains($parents[1].nonTypicalInputs, dataKey) -->
                    <div class="field required">
                        <label data-bind="attr: {class: 'label', for: dataKey}">
                            <span data-bind="text: dataKey.replaceAll('_', ' ').toUpperCase()"></span></label>
                        <div class="control">
                            <input data-bind="{attr: {name: dataKey, id: dataKey, value: step.step_data[dataKey] || ''}}"
                                   type="text"
                                   class="input-text required-entry" />
                        </div>
                    </div>
                    <!-- /ko -->
                    <!-- ko if: dataKey === 'wtm_type' -->
                        <div class="field">
                            <label data-bind="attr: {class: 'label', for: dataKey}">
                                <span data-bind="i18n: 'How did you hear about comave?'"></span>
                            </label>
                            <div class="control">
                                <select name="wtm_type" id="wtm_type" class="select required-entry" data-bind="{
                                        value: step.step_data[dataKey]
                                    }">
                                    <option value="0" data-bind="i18n: 'Sales Representative Team'"></option>
                                    <option value="1" data-bind="i18n: 'Publicity/Marketing'"></option>
                                    <option value="2" data-bind="i18n: 'Referral'"></option>
                                    <option value="3" data-bind="i18n: 'Other'"></option>
                                </select>
                            </div>
                        </div>
                    <!-- /ko -->
                    <!-- ko if: dataKey === 'country' -->
                        <div class="field">
                            <label data-bind="attr: {class: 'label', for: 'country'}">
                                <span data-bind="i18n: 'Country'"></span>
                            </label>
                            <div class="control">
                                <select id="country"
                                        name="country"
                                        class="select required-entry"
                                        data-bind="options: $parents[1].selectOptions,
                                           optionsText: 'label',
                                           optionsValue: 'value',
                                           value: $parents[1].selectedOption,
                                           optionsCaption: 'Please select'">
                                </select>
                            </div>
                        </div>
                    <!-- /ko -->
                    <!-- ko if: dataKey === 'region' -->
                    <div class="field required">
                        <label data-bind="attr: {class: 'label', for: dataKey}">
                            <span data-bind="i18n: dataKey.toUpperCase()"></span>
                        </label>
                        <div class="control">
                            <select name="region_id" data-bind="options: $parents[1].selectRegionOptions,
                                               optionsText: 'label',
                                               optionsValue: 'value',
                                               optionsCaption: 'Please select',
                                               value: $parents[1].selectedRegion,
                                               visible: $parents[1].showRegionTextInput() === false,
                                               required: $parents[1].showRegionTextInput() === false,
                                               disabled: $parents[1].showRegionTextInput() === true,
                                               attr: {id: dataKey}">
                            </select>
                            <input data-bind="visible: $parents[1].showRegionTextInput() === true,
                                               required: $parents[1].showRegionTextInput() === true,
                                               value: $parents[1].regionTextValue()" type="text" class="input-text" name="region"/>
                        </div>
                    </div>
                    <!-- /ko -->
                    <!-- ko if: dataKey === 'html' -->
                        <div class="field">
                            <div class="control flex-wrap-group">
                                <input class="required-entry checkbox" type="checkbox" name="term_accepted" value="1" data-bind="{attr: {checked: step.step_data['term_accepted']}}"/>
                                <label data-bind="attr: {class: 'label', for: 'term_accepted'}">
                                    <span data-bind="i18n: 'Please read and accept the following terms and conditions.'"></span>
                                    <a href="javascript:void(0);" data-bind="click: $parents[1].initTermsModal, i18n: 'View'"></a>
                                </label>
                            </div>
                            <div class="terms_modal" style="display: none;">
                                <div class="modal-inner-content">
                                    <div data-bind="html: step.step_data[dataKey]"></div>
                                </div>
                            </div>
                        </div>
                    <!-- /ko -->
                <!-- /ko -->
            </div>
        <!-- /ko -->
        <button class="action primary" type='button' data-bind='click: saveStep'>Next</button>
    </form>
    <style>
        .flex-wrap-group {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            gap: 10px; /* optional spacing */
        }

        .flex-wrap-group > * {
            flex: 0 0 auto;
        }

        .flex-wrap-group > *:nth-child(3) {
            flex-basis: 100%;
        }
    </style>
</div>
