define([
    'jquery',
    'underscore',
    'ko',
], function ($, _, ko) {
    'use strict';

    return {
        countries: ko.observableArray([]),
        regions: ko.observableArray([]),

        initCountries: function (
            stepCode,
            onboardingData = [],
            countryListOptions = ko.observableArray([]),
            selectedCountry = ko.observable(null)
        ){
            const self = this;

            if (_.isEmpty(this.countries())) {
                $('body').trigger('processStart');
                $.ajax({
                    url: '/rest/V1/directory/countries',
                    method: 'GET',
                    contentType: 'application/json',
                    success: function (data) {
                        const formattedCountries = _.map(data, function (country) {
                            return {
                                value: country.id,
                                label: country.full_name_english || country.full_name_locale || country.id
                            };
                        });

                        countryListOptions(formattedCountries);
                        self.countries(formattedCountries);
                        self.setInitialCountryOption(onboardingData, selectedCountry);

                        $('body').trigger('processStop');
                    },
                    error: function (xhr) {
                        console.error('Error fetching countries', xhr);
                        $('body').trigger('processStop');
                    }
                });
            }

            countryListOptions(self.countries());
            this.setInitialCountryOption(onboardingData, selectedCountry);
        },

        setInitialCountryOption: function (onboardingData = [], selectedCountry = ko.observable(null)) {
            const originStep = _.find(onboardingData, function(step) {
                    return _.has(step.step_data, 'country');
                }),
                country = originStep ? originStep.step_data.country : null;

            if (!_.isEmpty(country)) {
                selectedCountry(country);
            }
        },

        getRegions: function (
            onboardingData = [],
            countryId,
            selectRegionOptions = ko.observableArray([]),
            selectedRegion = ko.observable(null),
            regionTextValue = ko.observable(null),
            showRegionTextInput = ko.observable(false)
        ) {
            showRegionTextInput.subscribe(function (showInput) {
                if (showInput === true) {
                    regionTextValue(null);
                }
            });

            if (typeof countryId === 'undefined') {
                return;
            }

            const self = this;

            if (this.regions()[countryId] && !showRegionTextInput()) {
                selectRegionOptions(this.regions()[countryId]);
                self.setInitialRegionValue(onboardingData, selectedRegion, regionTextValue);
            } else {
                $('body').trigger('processStart');
                $.ajax({
                    url: '/rest/V1/directory/countries/' + countryId,
                    method: 'GET',
                    contentType: 'application/json',
                    success: function (data) {
                        $('body').trigger('processStop');
                        showRegionTextInput(typeof data?.available_regions === 'undefined');

                        if (typeof data.available_regions === 'undefined') {
                            return false;
                        }

                        const formattedRegions = _.map(data.available_regions, function (region) {
                            return {
                                value: region.id,
                                label: region.name
                            };
                        });

                        const existingRegions = self.regions();
                        existingRegions[countryId] = formattedRegions;
                        self.regions(existingRegions);
                        selectRegionOptions(formattedRegions);
                        self.setInitialRegionValue(onboardingData, selectedRegion, regionTextValue);
                    },
                    error: function (xhr) {
                        console.error('Error fetching regions', xhr);
                        $('body').trigger('processStop');
                    }
                });
            }
        },

        setInitialRegionValue: function (
            onboardingData = [],
            selectedRegion = ko.observable(null),
            regionTextValue = ko.observable(null)
        ) {
            const originStep = _.find(onboardingData, function(step) {
                    return _.has(step.step_data, 'region_id');
                }),
                regionId = originStep ? originStep.step_data.region_id : null,
                region = originStep ? originStep.step_data.region : null

            if (!_.isEmpty(regionId)) {
                selectedRegion(regionId);
            }

            if (!_.isEmpty(region)) {
                regionTextValue(region);
            }
        }
    }
});
