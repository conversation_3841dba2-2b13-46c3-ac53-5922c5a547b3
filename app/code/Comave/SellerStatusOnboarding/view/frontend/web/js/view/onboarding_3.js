define([
    'uiComponent',
    'Comave_SellerStatusOnboarding/js/model/completion',
    'Comave_SellerStatusOnboarding/js/model/countries-provider',
    'Magento_Checkout/js/model/step-navigator',
    'mage/translate',
    'underscore',
    'jquery',
    'ko',
    'mage/url',
    'https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js'
], function (Component, stepsCompletion, countryProvider, stepNavigator, $t, _, $, ko, urlBuilder) {
    'use strict';

    return Component.extend({
        defaults: {
            template: 'Comave_SellerStatusOnboarding/onboarding_3',
            onboardingData: ko.observableArray([]),
            nonTypicalInputs: [
                'country',
                'packing_time',
                'delivery_time',
                'selected_service',
                'countries',
                'entity_id',
                'service_options',
                'total_lead_time',
                'region_id',
                'region',
                'has_shipping_methods'
            ],
            selectedCountry: ko.observable(null),
            countriesInitiated: ko.observable(false),
            selectOptions: ko.observableArray([]),
            showRegionTextInput: ko.observable(false),
            selectRegionOptions: ko.observableArray([]),
            packingTime: ko.observable(0),
            selectedRegion: ko.observable(null),
            regionTextValue: ko.observable(null),
            selectedService: ko.observable(null),
            deliveryTime: ko.observable(0),
            leadTime: ko.observable(0),
        },

        initialize: function () {
            this._super();
            const self = this;

            const rawData = window.onboardingConfig['onboarding_3'] || {};
            const stepArray = Object.entries(rawData)
                .filter(([key]) => key !== 'is_complete')
                .map(([key, value]) => value);

            this.onboardingData(stepArray);

            _.each(stepNavigator.steps(), function (step) {
                if (step.code === 'onboarding_3') {
                    step.isVisible.subscribe(function (isVisible) {
                        self.packingTime(rawData['shipping_method']['step_data']['packing_time'] || 0);
                        self.selectedService(rawData['shipping_method']['step_data']['selected_service'] || 0);
                        self.deliveryTime(rawData['shipping_method']['step_data']['delivery_time'] || 0);
                        if (isVisible === true && !self.countriesInitiated()) {
                            self.initCountries();
                            self.countriesInitiated(true);
                        }
                    });
                }
            });

            this.selectOptions.subscribe(function (countryList) {
                setTimeout(function () {
                    if (!_.isEmpty(countryList)) {
                        const formattedCountries = _.map(countryList, function (country) {
                            return {
                                id: JSON.stringify({id: country.value, text: country.label}),
                                text: country.label
                            };
                        });

                        const $select = $('select#countries');
                        $select.empty();

                        // Preload selected options
                        $select.select2({
                            placeholder: $t("Select all available countries"),
                            minimumInputLength: 3,
                            allowClear: true,
                            data: formattedCountries,
                            cache: true
                        });

                        const returnAddressData = _.first(self.onboardingData()),
                            shippingMethodData = _.last(self.onboardingData());
                        const countryId = returnAddressData.step_data.country || false;

                        if (countryId !== false) {
                            self.selectedCountry(countryId);
                        }

                        if (!_.isEmpty(shippingMethodData.step_data.countries)) {
                            const decodedCountries = JSON.parse(shippingMethodData.step_data.countries);

                            decodedCountries.forEach(item => {
                                const option = new Option(item.text, item.id, true, true);
                                $select.append(option);
                            });
                        }
                    }
                }, 500);
            });

            this.packingTime.subscribe(function (value) {
                self.leadTime(parseInt(value) + parseInt(self.deliveryTime()));
            });

            this.deliveryTime.subscribe(function (value) {
                self.leadTime(parseInt(value) + parseInt(self.packingTime()));
            });

            this.selectedCountry.subscribe(function (countryId) {
                if (typeof countryId !== 'undefined') {
                    countryProvider.getRegions(
                        self.onboardingData(),
                        countryId,
                        self.selectRegionOptions,
                        self.selectedRegion,
                        self.regionTextValue,
                        self.showRegionTextInput
                    );
                }
            });

            this.selectedRegion.subscribe(function (regionId) {
                const result = _.findWhere(self.selectRegionOptions(), { value: regionId });
                self.regionTextValue(result?.label || null);
            });

            return this;
        },

        getRegions: function (countryId) {
            countryProvider.getRegions(
                this.onboardingData(),
                countryId,
                this.selectRegionOptions,
                this.selectedRegion,
                this.regionTextValue,
                this.showRegionTextInput
            )
        },

        navigatePrev: function () {
            let activeItemIndex = stepNavigator.getActiveItemIndex(),
                prevStep = stepNavigator.steps()[activeItemIndex - 1];
            stepNavigator.steps()[activeItemIndex].isVisible(false);
            prevStep && prevStep.isVisible(true);
        },

        initCountries: function () {
            countryProvider.initCountries(
                'onboarding_3',
                this.onboardingData(),
                this.selectOptions,
                this.selectedOption,
            );
        },

        getText: function (title) {
            return title.replaceAll('_', ' ').toUpperCase();
        },

        saveStep: function () {
            const form = $('#onboarding_3');

            if (form.validation() && !form.validation('isValid')) {
                return false;
            }

            let data = form.serializeArray();
            data.push({ name: 'form_key', value: $('input[name="form_key"]').val() });
            data.push({ name: 'step', value: 'onboarding_3' });

            const self = this;
            $('body').trigger('processStart');

            $.ajax({
                url: urlBuilder.build('onboarding/seller/save'),
                method: 'POST',
                data: $.param(data),
                success: function (data) {
                    if (data.success === false) {
                        alert(data.message);
                    } else {
                        stepsCompletion.setStepComplete('onboarding_3');

                        if (stepsCompletion.isAllCompleted() === true) {
                            window.location = urlBuilder.build('onboarding/account/pendingValidation');
                        } else {
                            stepNavigator.setHash(stepsCompletion.getFirstUncompletedStep());
                            stepNavigator.handleHash();
                        }
                    }

                    $('body').trigger('processStop');
                },
                error: function (xhr) {
                    $('body').trigger('processStop');
                    alert('Error saving data');
                }
            });
        },
    });
});
