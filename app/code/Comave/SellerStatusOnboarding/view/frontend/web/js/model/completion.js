define([
    'ko',
    'underscore'
], function (ko, _) {
    'use strict';

    return {
        completedSteps: ko.observableArray([]),
        uncompletedStep: ko.observable(null),

        isStepComplete: function (stepCode) {
            return this.completedSteps()[stepCode] || false;
        },

        setStepComplete: function (stepCode) {
            const steps = this.completedSteps();
            steps[stepCode] = true;
            this.completedSteps(steps);
        },

        isAllCompleted() {
            const self = this;

            _.each(window.onboardingConfig, function (stepData, stepCode) {
                if (!self.isStepComplete(stepCode) && !self.uncompletedStep()) {
                    self.uncompletedStep(stepCode);
                }
            });

            return _.isEmpty(this.uncompletedStep());
        },

        getFirstUncompletedStep: function () {
            return this.uncompletedStep();
        }
    }
});
