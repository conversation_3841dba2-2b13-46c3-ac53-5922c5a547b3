define([
    'uiComponent',
    'Comave_SellerStatusOnboarding/js/model/completion',
    'Magento_Checkout/js/model/step-navigator',
    'ko',
    'mage/url',
    'underscore',
    'mage/translate'
], function (Component, stepCompletion, stepNavigator, ko, urlBuilder, _, $t) {
    'use strict';

    return Component.extend({
        defaults: {
            template: 'Comave_SellerStatusOnboarding/container'
        },
        initialize: function () {
            this._super();

            _.each(window.onboardingConfig, function(stepData, stepCode) {
                if (stepData.is_complete === true) {
                    stepCompletion.setStepComplete(stepCode);
                }
            });


            if (stepCompletion.isAllCompleted()) {
                window.location = urlBuilder.build('onboarding/account/pendingValidation');

                return false;
            }

            _.extend(this, stepNavigator);

            // Define 4 steps
            const steps = [
                {
                    code: 'onboarding_1',
                    title: $t('Business Details'),
                    isVisible: ko.observable(
                        window.onboardingConfig['onboarding_1']['is_complete']
                    ),
                    sortOrder: 10
                },
                {
                    code: 'onboarding_2',
                    title: $t('Stripe Connect'),
                    isVisible: ko.observable(
                        window.onboardingConfig['onboarding_2']['is_complete']
                    ),
                    sortOrder: 20
                },
                {
                    code: 'onboarding_3',
                    title: $t('Shipping Methods & Return Address'),
                    isVisible: ko.observable(
                        window.onboardingConfig['onboarding_3']['is_complete']
                    ),
                    sortOrder: 30
                },
            ];

            const self = this;

            steps.forEach(function (step, index) {
                stepNavigator.registerStep(
                    step.code,
                    null,
                    $t(step.title),
                    step.isVisible,
                    _.bind(self.navigate, self),
                    step.sortOrder
                );
            });

            const getUnfinishedStep = stepCompletion.getFirstUncompletedStep();

            if (getUnfinishedStep) {
                stepNavigator.setHash(getUnfinishedStep);
                stepNavigator.handleHash();
            }

            return this;
        },

        isComplete: function (step) {
            return stepCompletion.isStepComplete(step);
        },

        navigate: function (step) {
            let activeItemIndex = stepNavigator.getActiveItemIndex();
            stepNavigator.steps()[activeItemIndex].isVisible(false);
            step && step.isVisible(true)
        },
    });
});
