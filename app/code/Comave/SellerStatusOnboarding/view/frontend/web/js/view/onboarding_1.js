define([
    'Comave_SellerStatusOnboarding/js/model/completion',
    'Comave_SellerStatusOnboarding/js/model/countries-provider',
    'underscore',
    'ko',
    'mage/url',
    'jquery',
    'uiComponent',
    'Magento_Ui/js/modal/modal',
    'Magento_Checkout/js/model/step-navigator',
    'mage/utils/misc',
    'mage/validation'
], function (stepCompletion, countryProvider, _, ko, urlBuilder, $, Component, modal, stepNavigator, utils) {
    'use strict';

    return Component.extend({
        defaults: {
            template: 'Comave_SellerStatusOnboarding/onboarding_1',
            onboardingData: ko.observableArray([]),
            nonTypicalInputs: [
                'wtm_type',
                'region',
                'region_id',
                'term_accepted',
                'html',
                'country'
            ],
            selectedOption: ko.observable(''),
            selectOptions: ko.observableArray([]),
            countriesInitiated: ko.observable(false),
            selectRegionOptions: ko.observableArray([]),
            selectedRegion: ko.observable(null),
            showRegionTextInput: ko.observable(false),
            regionTextValue: ko.observable(null),
        },

        initialize: function () {
            this._super();

            const self = this;
            const rawData = window.onboardingConfig['onboarding_1'] || {};
            const stepArray = Object.entries(rawData)
                .filter(([key]) => key !== 'is_complete')
                .map(([key, value]) => value);


            this.onboardingData(stepArray);

            _.each(stepNavigator.steps(), function (step) {
                if (step.code === 'onboarding_1' && !self.countriesInitiated()) {
                    if (step.isVisible()) {
                        self.initCountries();
                        self.countriesInitiated(true);
                    }

                    step.isVisible.subscribe(function (isVisible) {
                        if (isVisible && !self.countriesInitiated()) {
                            self.initCountries();
                            self.countriesInitiated(true);
                        }
                    });
                }
            });

            if (rawData['is_complete']) {
                this.goToNextStep();
            }

            this.selectedOption.subscribe(function (countryId) {
                if (typeof countryId !== 'undefined') {
                    countryProvider.getRegions(
                        stepArray,
                        countryId,
                        self.selectRegionOptions,
                        self.selectedRegion,
                        self.regionTextValue,
                        self.showRegionTextInput
                    );
                }
            });

            this.selectedRegion.subscribe(function (regionId) {
                const result = _.findWhere(self.selectRegionOptions(), { value: regionId });
                self.regionTextValue(result?.label || null);
            });

            return this;
        },

        saveStep: function () {
            const form = $('#onboarding_1');

            if (form.validation() && !form.validation('isValid')) {
                return false;
            }

            let data = form.serializeArray();
            data.push({ name: 'form_key', value: $('input[name="form_key"]').val() });
            data.push({ name: 'step', value: 'onboarding_1' });

            const self = this;
            $('body').trigger('processStart');

            $.ajax({
                url: urlBuilder.build('onboarding/seller/save'),
                method: 'POST',
                data: $.param(data),
                success: function (data) {
                    $('body').trigger('processStop');
                    if (data.success === false) {
                        alert(data.message);
                    } else {
                        stepCompletion.setStepComplete('onboarding_1');
                        self.goToNextStep();
                    }
                },
                error: function (xhr) {
                    $('body').trigger('processStop');
                    alert('Error saving data');
                }
            });
        },

        goToNextStep: function () {
            let activeItemIndex = stepNavigator.getActiveItemIndex();
            stepNavigator.steps()[activeItemIndex].isVisible(false);
            stepNavigator.next();
        },

        getText: function (title) {
            return title.replaceAll('_', ' ').toUpperCase();
        },

        initCountries: function () {
            countryProvider.initCountries(
                'onboarding_1',
                this.onboardingData(),
                this.selectOptions,
                this.selectedOption
            );
        },

        initTermsModal: function () {
            var modalSelector = $('.terms_modal');

            if (!modalSelector.hasClass('modal-initialized')) {
                modal({
                    type: 'popup',
                    responsive: true,
                    innerScroll: true,
                    title: $.mage.__('Terms & Conditions'),
                    buttons: [{
                        text: $.mage.__('Close'),
                        class: 'action secondary',
                        click: function () {
                            this.closeModal();
                        }
                    }]
                }, modalSelector);

                modalSelector.addClass('modal-initialized');
            }

            modalSelector.modal('openModal');
        }
    });
});
