define([
    'uiComponent',
    'Magento_Checkout/js/model/step-navigator',
    'underscore',
    'ko'
], function (Component, stepNavigator, _, ko) {
    'use strict';

    return Component.extend({
        defaults: {
            template: 'Comave_SellerStatusOnboarding/onboarding_2',
            onboardingData: ko.observableArray([]),
        },

        initialize: function () {
            this._super();

            const rawData = window.onboardingConfig['onboarding_2'] || {};
            const stepArray = Object.entries(rawData)
                .filter(([key]) => key !== 'is_complete')
                .map(([key, value]) => value);

            this.onboardingData(_.first(stepArray));

            if (rawData['is_complete']) {
                this.goToNextStep();
            }

            return this;
        },

        goToNextStep: function () {
            stepNavigator.next();
        },

        navigatePrev: function () {
            let activeItemIndex = stepNavigator.getActiveItemIndex(),
                prevStep = stepNavigator.steps()[activeItemIndex - 1];
            stepNavigator.steps()[activeItemIndex].isVisible(false);
            prevStep && prevStep.isVisible(true);
        },

        navigateNext: function () {
            stepNavigator.next();
        }
    });
});
