<?php

declare(strict_types=1);

namespace Comave\SellerStatusOnboarding\Api;

use Webkul\Marketplace\Model\Seller;

interface OnboardingStepInterface
{
    public const string IS_COMPLETE = 'is_complete';
    public const string STEP_DATA = 'step_data';
    public const string STEP_NAME = 'step_name';
    /**
     * @param Seller $seller
     * @return bool
     */
    public function isComplete(Seller $seller): bool;

    /**
     * @param Seller $seller
     * @return array
     */
    public function getStepData(Seller $seller): array;

    /**
     * @return string
     */
    public function getStepName(): string;

    /**
     * @return string
     */
    public function belongsTo(): string;
}
