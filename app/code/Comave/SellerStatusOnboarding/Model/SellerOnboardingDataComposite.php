<?php

declare(strict_types=1);

namespace Comave\SellerStatusOnboarding\Model;

use Comave\SellerStatusOnboarding\Api\OnboardingStepInterface;
use Webkul\Marketplace\Model\Seller;

class SellerOnboardingDataComposite
{
    private ?array $results = null;

    /**
     * @param OnboardingStepInterface[] $onboardingSteps
     */
    public function __construct(private readonly array $onboardingSteps)
    {
    }

    /**
     * @param Seller $seller
     * @return array
     */
    public function getStepData(Seller $seller): array
    {
        if (!empty($this->results)) {
            return $this->results;
        }

        $result = [];

        foreach ($this->onboardingSteps as $onboardingStep) {
            if (!isset($result[$onboardingStep->belongsTo()])) {
                $result[$onboardingStep->belongsTo()] = [
                    OnboardingStepInterface::IS_COMPLETE => true,
                ];
            }

            $result[$onboardingStep->belongsTo()][OnboardingStepInterface::IS_COMPLETE] =
                $result[$onboardingStep->belongsTo()][OnboardingStepInterface::IS_COMPLETE] &&
                $onboardingStep->isComplete($seller);

            $result[$onboardingStep->belongsTo()][$onboardingStep->getStepName()] = [
                OnboardingStepInterface::STEP_DATA => $onboardingStep->getStepData($seller),
                OnboardingStepInterface::STEP_NAME => $onboardingStep->getStepName(),
            ];
        }

        $this->results = $result;
        return $this->results;
    }
}
