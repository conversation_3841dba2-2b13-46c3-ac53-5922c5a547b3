<?php

declare(strict_types=1);

namespace Comave\SellerStatusOnboarding\Model;

use Coditron\CustomShippingRate\Model\ResourceModel\ShipTableRates\CollectionFactory as ShipTableRatesFactory;
use Coditron\CustomShippingRate\Model\ResourceModel\ShipTableRates as RateResourceModel;
use Coditron\CustomShippingRate\Model\ShipTableRates;
use Magento\Customer\Api\AddressRepositoryInterface;
use Magento\Customer\Api\Data\AddressInterface;
use Magento\Customer\Api\Data\AddressInterfaceFactory;
use Magento\Customer\Api\Data\RegionInterfaceFactory;
use Magento\Customer\Model\ResourceModel\Customer as CustomerResourceModel;
use Magento\Customer\Model\CustomerFactory;
use Magento\Customer\Helper\Session\CurrentCustomer;
use Magento\Framework\Api\DataObjectHelper;
use Magento\Framework\App\RequestInterface;
use Webkul\Marketplace\Model\SellerFactory;
use Webkul\Marketplace\Model\ResourceModel\Seller as ResourceModel;
use Webkul\MarketplaceBaseShipping\Model\ResourceModel\ShippingSetting;
use Webkul\MarketplaceBaseShipping\Model\ShippingSettingFactory;

class OnboardingSaveHandler
{
    /**
     * @param AddressInterfaceFactory $addressFactory
     * @param RegionInterfaceFactory $regionFactory
     * @param ShipTableRatesFactory $rateFactory
     * @param CustomerResourceModel $customerResourceModel
     * @param DataObjectHelper $objectHelper
     * @param RateResourceModel $rateResource
     * @param AddressRepositoryInterface $addressRepository
     * @param CustomerFactory $customerFactory
     * @param ShippingSettingFactory $shippingSettingFactory
     * @param ShippingSetting $shippingSettingResource
     * @param SellerFactory $modelFactory
     * @param ResourceModel $resourceModel
     * @param CurrentCustomer $currentCustomer
     */
    public function __construct(
        private readonly AddressInterfaceFactory $addressFactory,
        private readonly RegionInterfaceFactory $regionFactory,
        private readonly ShipTableRatesFactory $rateFactory,
        private readonly CustomerResourceModel $customerResourceModel,
        private readonly DataObjectHelper $objectHelper,
        private readonly RateResourceModel $rateResource,
        private readonly AddressRepositoryInterface $addressRepository,
        private readonly CustomerFactory $customerFactory,
        private readonly ShippingSettingFactory $shippingSettingFactory,
        private readonly ShippingSetting $shippingSettingResource,
        private readonly SellerFactory $modelFactory,
        private readonly ResourceModel $resourceModel,
        private readonly CurrentCustomer $currentCustomer,
    ) {
    }

    public function save(RequestInterface $request): void
    {
        $seller = $this->modelFactory->create();
        $this->resourceModel->load(
            $seller,
            $this->currentCustomer->getCustomerId(),
            'seller_id'
        );
        $customer = $this->customerFactory->create();
        $this->customerResourceModel->load($customer, $this->currentCustomer->getCustomerId());

        $saveData = $this->translateFields($request->getParams(), $request->getParam('step'));

        if ($request->getParam('step') === 'onboarding_1') {
            $customer->setTaxvat($request->getParam('vat_number'));
            $this->customerResourceModel->save($customer);
            $otherInfo = json_decode($seller->getOthersInfo() ?: '{}', true);
            $otherInfo = array_merge(
                $otherInfo,
                $saveData['seller']['others_info'],
            );
            $saveData['seller']['others_info'] = json_encode($otherInfo);
            $seller->addData($saveData['seller']);
            $this->resourceModel->save($seller);

            $shippingSetting = $this->shippingSettingFactory->create();
            $this->shippingSettingResource->load(
                $shippingSetting,
                $this->currentCustomer->getCustomerId(),
                'seller_id'
            );
            $shippingSetting->addData($saveData['shipping_settings']);
            $this->shippingSettingResource->save($shippingSetting);
        } else {
            $address = !empty($customer->getData('default_return')) ?
                $this->addressRepository->getById((int) $customer->getData('default_return')) :
                $this->addressFactory->create();

            $this->objectHelper->populateWithArray(
                $address,
                $saveData['return_address'],
                AddressInterface::class
            );
            /** @var AddressInterface $address */
            $this->addressRepository->save($address);
            $customer->setData('default_return', $address->getId());
            $this->customerResourceModel->saveAttribute($customer, 'default_return');
            $saveData['table_rate']['return_address_id'] = $address->getId();
            $collection = $this->rateFactory->create();
            $collection->addFieldToFilter('seller_id', $seller->getSellerId());
            /** @var ShipTableRates $rate */
            $rate = $collection->getLastItem();
            $rate->addData($saveData['table_rate']);
            $this->rateResource->save($rate);
        }
    }

    /**
     * @param array $params
     * @param string $step
     * @return array[]
     */
    private function translateFields(array $params, string $step): array
    {
        $weightParam = array_keys(array_filter($params, function ($v, $k) {
            return str_contains($k, 'weight') !== false;
        }, ARRAY_FILTER_USE_BOTH)) ?: [];

        return match ($step) {
            'onboarding_1' => [
                'seller' => [
                    'owner_name' => $params['owner_name'],
                    'email' => $params['business_email'],
                    'business_registration_number' => $params['business_registration_number'],
                    'shop_title' => $params['business_name'],
                    'contact_number' => $params['phone_number'],
                    'shop_url' => $params['business_url'],
                    'others_info' => [
                        'wtm_type' => $params['wtm_type'],
                        'term_accepted' => $params['term_accepted'] === '1',
                    ]
                ],
                'shipping_settings' => [
                    'region' => $params['region'],
                    'region_id' => $params['region_id'],
                    'seller_id' => $this->currentCustomer->getCustomerId(),
                    'telephone' => $params['phone_number'],
                    'country_id' => $params['country'],
                    'city' => $params['city'],
                    'street' => json_encode([$params['street']]),
                    'postal_code' => $params['postal_code'],
                ]
            ],
            'onboarding_3' => [
                'table_rate' => [
                    'courier_name' => $params['carrier_name'],
                    'country' => $params['country'],
                    'region' => $params['region'] ?? $params['region_id'],
                    'weight' => $params[$weightParam[0]] ?? 0.1,
                    'shipping_price' => (float) $params['shipping_price'],
                    'seller_id' => $this->currentCustomer->getCustomerId(),
                    'service_type' => $params['service'],
                    'countries' => implode(
                        ',',
                        array_map(
                            function ($item) {
                                try {
                                    $itemArr = json_decode($item, true, JSON_THROW_ON_ERROR);
                                    return $itemArr['id'];
                                } catch (\Throwable) {
                                    return $item;
                                }
                            },
                            $params['countries']
                        )
                    ),
                    'packing_time' => (int) $params['packing_time'],
                    'delivery_time' => (int) $params['delivery_time'],
                    'total_lead_time' => (int) $params['total_lead_time'],
                ],
                'return_address' => [
                    'customer_id' => $this->currentCustomer->getCustomerId(),
                    'country_id' => $params['country'],
                    'firstname' => $params['firstname'],
                    'city' => $params['city'],
                    'lastname' => $params['lastname'],
                    'telephone' => $params['telephone'],
                    'region' => !empty($params['region']) ? $this->regionFactory->create([
                        'data' => [
                            'region' => $params['region'],
                            'region_id' => $params['region_id'],
                        ]
                    ]) : null,
                    'region_id' => $params['region_id'],
                    'street' => [$params['street']],
                    'postcode' => $params['postcode'],
                    'parent_id' => $this->currentCustomer->getCustomerId(),
                    'is_active' => 1,
                ]
            ],
            default => []
        };
    }
}
