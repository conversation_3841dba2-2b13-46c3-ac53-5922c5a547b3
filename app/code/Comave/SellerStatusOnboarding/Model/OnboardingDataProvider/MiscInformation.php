<?php

declare(strict_types=1);

namespace Comave\SellerStatusOnboarding\Model\OnboardingDataProvider;

use Comave\SellerStatusOnboarding\Api\OnboardingStepInterface;
use Magento\Customer\Api\CustomerRepositoryInterface;
use <PERSON>kul\Marketplace\Model\Seller;

class MiscInformation implements OnboardingStepInterface
{
    public const int WTM_TYPE_SALES_REPRESENTATIVE = 0;
    public const int WTM_TYPE_MARKETING = 1;
    public const int WTM_TYPE_REFERRAL = 2;
    public const int WTM_TYPE_OTHER = 3;

    /**
     * @param Seller $seller
     * @return array
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function getStepData(Seller $seller): array
    {
        $othersInfo = json_decode(
            $seller->getOthersInfo() ?: '{}',
            true
        );

        return [
            'wtm_type' => $othersInfo['wtm_type'] ?? null,
        ];
    }

    /**
     * @return string
     */
    public function getStepName(): string
    {
        return 'misc_information';
    }

    /**
     * @param Seller $seller
     * @return bool
     */
    public function isComplete(Seller $seller): bool
    {
        return !empty(
            array_filter(
                $this->getStepData($seller),
                [$this, 'filterArray']
            )
        );
    }

    /**
     * @param mixed $var
     * @return bool
     */
    public function filterArray(mixed $var): bool
    {
        return ($var !== null && $var !== false && $var !== '');
    }

    /**
     * @return string
     */
    public function belongsTo(): string
    {
        return 'onboarding_1';
    }
}
