<?php

declare(strict_types=1);

namespace Comave\SellerStatusOnboarding\Model\OnboardingDataProvider;

use Comave\SellerStatusOnboarding\Api\OnboardingStepInterface;
use Magento\Customer\Api\AddressRepositoryInterface;
use Magento\Customer\Model\CustomerFactory;
use Magento\Customer\Model\ResourceModel\Customer as CustomerResourceModel;
use Webkul\Marketplace\Model\Seller;

class ReturnAddress implements OnboardingStepInterface
{
    private ?array $loadedData = null;

    /**
     * @param AddressRepositoryInterface $addressRepository
     * @param CustomerFactory $customerFactory
     * @param CustomerResourceModel $customerResource
     */
    public function __construct(
        private readonly AddressRepositoryInterface $addressRepository,
        private readonly CustomerFactory $customerFactory,
        private readonly CustomerResourceModel $customerResource,
    ) {
    }

    /**
     * @param Seller $seller
     * @return string[]
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function getStepData(Seller $seller): array
    {
        if (!empty($this->loadedData)) {
            return $this->loadedData;
        }

        $customer = $this->customerFactory->create();
        $this->customerResource->load($customer, (int) $seller->getSellerId());

        $returnAddressId = $customer->getData('default_return');
        $address = [
            'entity_id' => $returnAddressId,
            'firstname' => $customer->getFirstname(),
            'lastname' => $customer->getLastname(),
            'telephone' => $customer->getCustomAttribute('phone_no')?->getValue() ?? '',
            'country' => '',
            'region_id' => '',
            'region' => '',
            'city' => '',
            'street' => '',
            'postcode' => ''
        ];

        if (!empty($returnAddressId)) {
            $addressEntity = $this->addressRepository->getById((int) $returnAddressId);
            $address = [
                'entity_id' => $addressEntity->getId(),
                'firstname' => $addressEntity->getFirstname(),
                'lastname' => $addressEntity->getLastname(),
                'telephone' => $addressEntity->getTelephone(),
                'country' => $addressEntity->getCountryId(),
                'region_id' => $addressEntity->getRegionId(),
                'region' => $addressEntity->getRegion()?->getRegionCode(),
                'city' => $addressEntity->getCity(),
                'street' => implode(', ', $addressEntity->getStreet() ?? []),
                'postcode' => $addressEntity->getPostcode()
            ];
        }

        $this->loadedData = $address;

        return $this->loadedData;
    }

    /**
     * @return string
     */
    public function getStepName(): string
    {
        return 'return_address';
    }

    /**
     * @param Seller $seller
     * @return bool
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function isComplete(Seller $seller): bool
    {
        return !empty(array_filter($this->getStepData($seller)));
    }

    /**
     * @return string
     */
    public function belongsTo(): string
    {
        return 'onboarding_3';
    }
}
