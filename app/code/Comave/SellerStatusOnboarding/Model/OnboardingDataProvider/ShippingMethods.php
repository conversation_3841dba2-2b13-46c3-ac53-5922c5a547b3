<?php

declare(strict_types=1);

namespace Comave\SellerStatusOnboarding\Model\OnboardingDataProvider;

use Coditron\CustomShippingRate\Block\TableRates;
use Coditron\CustomShippingRate\Model\ResourceModel\ShipTableRates\CollectionFactory;
use Comave\SellerStatusOnboarding\Api\OnboardingStepInterface;
use Webkul\Marketplace\Model\Seller;
use Magento\Directory\Api\CountryInformationAcquirerInterface;

class ShippingMethods implements OnboardingStepInterface
{
    private ?array $loadedData = null;

    /**
     * @param CollectionFactory $collectionFactory
     * @param CountryInformationAcquirerInterface $countryInfo
     * @param TableRates $tableRates
     */
    public function __construct(
        private readonly CollectionFactory $collectionFactory,
        private readonly CountryInformationAcquirerInterface $countryInfo,
        private readonly TableRates $tableRates,
    ) {
    }

    /**
     * @param Seller $seller
     * @return array
     */
    public function getStepData(Seller $seller): array
    {
        if (!empty($this->loadedData)) {
            return $this->loadedData;
        }

        $collection = $this->collectionFactory->create();
        $collection->addFieldToFilter('seller_id', $seller->getSellerId());
        $tableRate = $collection->getLastItem();

        $countries = [];

        if (!empty($tableRate->getCountries())) {
            foreach($tableRate->getCountries() as $countryId) {
                $countryInfo = $this->countryInfo->getCountryInfo($countryId);
                $countries[] = [
                    'id' => $countryId,
                    'text' => $countryInfo->getFullNameLocale() ?: $countryInfo->getFullNameEnglish()
                ];
            }

            $countries = json_encode($countries);
        }

        $this->loadedData = [
            'carrier_name' => $tableRate->getCourierName() ?: '',
            'service_options' => $this->tableRates->getServiceTypeOptions(),
            'selected_service' => $tableRate->getServiceType() ?: '',
            'countries' => !empty($countries) ? $countries : null,
            'total_lead_time' => $tableRate->getTotalLeadTime() ?: 0,
            'weight_(up_to_this_weight,_in_lbs)' => $tableRate->getWeight(),
            'shipping_price' => $tableRate->getShippingPrice() ?: 0,
            'packing_time' => $tableRate->getPackingTime() ?: 0,
            'delivery_time' => $tableRate->getDeliveryTime() ?: 0,
        ];

        return $this->loadedData;
    }

    /**
     * @return string
     */
    public function getStepName(): string
    {
        return 'shipping_method';
    }

    /**
     * @param Seller $seller
     * @return bool
     */
    public function isComplete(Seller $seller): bool
    {
        return !empty(array_filter($this->getStepData($seller)));
    }

    /**
     * @return string
     */
    public function belongsTo(): string
    {
        return 'onboarding_3';
    }
}
