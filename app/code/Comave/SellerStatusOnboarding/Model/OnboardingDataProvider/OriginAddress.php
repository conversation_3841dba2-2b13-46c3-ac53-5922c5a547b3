<?php

declare(strict_types=1);

namespace Comave\SellerStatusOnboarding\Model\OnboardingDataProvider;

use Comave\SellerStatusOnboarding\Api\OnboardingStepInterface;
use Webkul\Marketplace\Model\Seller;
use Webkul\MarketplaceBaseShipping\Model\ResourceModel\ShippingSetting;
use Webkul\MarketplaceBaseShipping\Model\ShippingSettingFactory;

class OriginAddress implements OnboardingStepInterface
{
    private ?array $loadedData = null;

    /**
     * @param ShippingSettingFactory $shippingOriginFactory
     * @param ShippingSetting $resourceModel
     */
    public function __construct(
        private readonly ShippingSettingFactory $shippingOriginFactory,
        private readonly ShippingSetting $resourceModel
    ) {
    }

    /**
     * @param Seller $seller
     * @return array
     */
    public function getStepData(Seller $seller): array
    {
        if (!empty($this->loadedData)) {
            return $this->loadedData;
        }

        $originAddress = $this->shippingOriginFactory->create();
        $this->resourceModel->load($originAddress, $seller->getSellerId(), 'seller_id');

        $this->loadedData = [
            'country' => $originAddress->getCountryId(),
            'region' => $originAddress->getRegion(),
            'region_id' => $originAddress->getRegionId(),
            'city' => $originAddress->getCity(),
            'street' => implode(' ', $originAddress->getStreet() ?: []),
            'postal_code' => $originAddress->getPostalCode()
        ];

        return $this->loadedData;
    }

    /**
     * @return string
     */
    public function getStepName(): string
    {
        return 'origin_address';
    }

    /**
     * @param Seller $seller
     * @return bool
     */
    public function isComplete(Seller $seller): bool
    {
        return !empty(array_filter($this->getStepData($seller)));
    }

    /**
     * @return string
     */
    public function belongsTo(): string
    {
        return 'onboarding_1';
    }
}
