<?php

declare(strict_types=1);

namespace Comave\SellerStatusOnboarding\Model\OnboardingDataProvider;

use Comave\SellerPayouts\Block\StripeDetails;
use Comave\SellerPayouts\Helper\Data;
use Comave\SellerStatusOnboarding\Api\OnboardingStepInterface;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Framework\View\LayoutInterface;
use Webkul\Marketplace\Model\Seller;

class StripeConnect implements OnboardingStepInterface
{
    /**
     * @param LayoutInterface $layout
     * @param CustomerRepositoryInterface $customerRepository
     * @param Data $payoutHelper
     */
    public function __construct(
        private readonly LayoutInterface $layout,
        private readonly CustomerRepositoryInterface $customerRepository,
        private readonly Data $payoutHelper,
    ) {
    }

    /**
     * @param Seller $seller
     * @return array
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function getStepData(Seller $seller): array
    {
        $stripeConnectHtml = $this->layout->createBlock(
            StripeDetails::class,
            'stripeConnect',
        )?->setTemplate('Comave_SellerPayouts::seller/stripeconnect.phtml');

        try {
            return [
                'html' => $stripeConnectHtml->toHtml(),
            ];
        } catch (\Exception) {
            //specifically for admin purposes
            return [
                'html' => ''
            ];
        }
    }

    /**
     * @return string
     */
    public function getStepName(): string
    {
        return 'stripe_connect';
    }

    /**
     * @param Seller $seller
     * @return bool
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function isComplete(Seller $seller): bool
    {
        $customer = $this->customerRepository->getById(
            (int) $seller->getSellerId()
        );
        $stripeClientId = $customer->getCustomAttribute('stripe_client_id')?->getValue();

        if (empty($stripeClientId)) {
            return false;
        }

        return $this->payoutHelper->getSellerAccountStatus($stripeClientId);
    }

    /**
     * @return string
     */
    public function belongsTo(): string
    {
        return 'onboarding_2';
    }
}
