<?php

declare(strict_types=1);

namespace Comave\SellerStatusOnboarding\Model\OnboardingDataProvider;

use Comave\SellerStatusOnboarding\Api\OnboardingStepInterface;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Webkul\Marketplace\Model\Seller;

class BusinessInformation implements OnboardingStepInterface
{
    private ?array $loadedData = [];

    /**
     * @param CustomerRepositoryInterface $customerRepository
     */
    public function __construct(private readonly CustomerRepositoryInterface $customerRepository)
    {
    }

    /**
     * @param Seller $seller
     * @return array
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function getStepData(Seller $seller): array
    {
        if (!empty($this->loadedData)) {
            return $this->loadedData;
        }

        $customerEntity = $this->customerRepository->getById(
            (int) $seller->getSellerId()
        );

        $this->loadedData = [
            'owner_name' => sprintf(
                '%s %s',
                $customerEntity->getFirstname(),
                $customerEntity->getLastname()
            ),
            'business_name' => $seller->getShopTitle(),
            'business_email' => $seller->getEmail(),
            'phone_number' => $seller->getContactNumber(),
            'business_url' => $seller->getShopUrl(),
            'vat_number' => $customerEntity->getTaxvat(),
            'business_registration_number' => $seller->getBusinessRegistrationNumber()
        ];

        return $this->loadedData;
    }

    /**
     * @return string
     */
    public function getStepName(): string
    {
        return 'business_information';
    }

    /**
     * @param Seller $seller
     * @return bool
     */
    public function isComplete(Seller $seller): bool
    {
        return !empty(array_filter($this->getStepData($seller)));
    }

    /**
     * @return string
     */
    public function belongsTo(): string
    {
        return 'onboarding_1';
    }
}
