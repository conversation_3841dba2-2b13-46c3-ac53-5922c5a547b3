<?php

declare(strict_types=1);

namespace Comave\SellerStatusOnboarding\Model\OnboardingDataProvider;

use Comave\SellerStatusOnboarding\Api\OnboardingStepInterface;
use Magento\Cms\Api\GetBlockByIdentifierInterface;
use Magento\Cms\Model\Template\FilterProvider;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Store\Model\StoreManagerInterface;
use Webkul\Marketplace\Model\Seller;

class Terms implements OnboardingStepInterface
{
    /**
     * @param StoreManagerInterface $storeManager
     * @param FilterProvider $filterProvider
     * @param GetBlockByIdentifierInterface $getBlockByIdentifier
     */
    public function __construct(
        private readonly StoreManagerInterface $storeManager,
        private readonly FilterProvider $filterProvider,
        private readonly GetBlockByIdentifierInterface $getBlockByIdentifier
    ) {
    }

    /**
     * @param Seller $seller
     * @return array
     */
    public function getStepData(Seller $seller): array
    {
        $othersInfo = json_decode(
            $seller->getOthersInfo() ?: '{}',
            true
        );

        try {
            $termsBlock = $this->getBlockByIdentifier->execute(
                'seller_terms_conditions',
                (int) $this->storeManager->getStore()->getId()
            );
            $html = $this->filterProvider->getBlockFilter()->filter(
                $termsBlock->getContent()
            );
        } catch (\Exception) {
            $html = 'please accept this ...'; //@todo
        }

        return [
            'html' => $html,
            'term_accepted' => (bool) ($othersInfo['term_accepted'] ?? false)
        ];
    }

    /**
     * @return string
     */
    public function getStepName(): string
    {
        return 'terms_conditions';
    }

    /**
     * @param Seller $seller
     * @return bool
     */
    public function isComplete(Seller $seller): bool
    {
        return $this->getStepData($seller)['term_accepted'];
    }

    /**
     * @return string
     */
    public function belongsTo(): string
    {
        return 'onboarding_1';
    }
}
