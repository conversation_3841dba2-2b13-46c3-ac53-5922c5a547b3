<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:App/etc/routes.xsd">
    <router id="standard">
        <route id="onboarding" frontName="onboarding">
            <module name="Comave_SellerStatusOnboarding"/>
        </route>
        <route id="marketplace">
            <module name="Comave_SellerStatusOnboarding" before="Comave_SellerStatus"/>
        </route>
        <route id="seller_payouts">
            <module name="Comave_SellerStatusOnboarding" before="Comave_SellerPayouts"/>
        </route>
    </router>
</config>
