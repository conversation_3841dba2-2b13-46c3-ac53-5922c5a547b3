<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Comave\SellerStatusOnboarding\Model\SellerOnboardingDataComposite">
        <arguments>
            <argument xsi:type="array" name="onboardingSteps">
                <item xsi:type="object" name="business_information">Comave\SellerStatusOnboarding\Model\OnboardingDataProvider\BusinessInformation</item>
                <item xsi:type="object" name="origin_address">Comave\SellerStatusOnboarding\Model\OnboardingDataProvider\OriginAddress</item>
                <item xsi:type="object" name="stripe_connect">Comave\SellerStatusOnboarding\Model\OnboardingDataProvider\StripeConnect</item>
                <item xsi:type="object" name="return_address">Comave\SellerStatusOnboarding\Model\OnboardingDataProvider\ReturnAddress</item>
                <item xsi:type="object" name="shipping_methods">Comave\SellerStatusOnboarding\Model\OnboardingDataProvider\ShippingMethods</item>
                <item xsi:type="object" name="misc">Comave\SellerStatusOnboarding\Model\OnboardingDataProvider\MiscInformation</item>
                <item xsi:type="object" name="terms">Comave\SellerStatusOnboarding\Model\OnboardingDataProvider\Terms</item>
            </argument>
        </arguments>
    </type>
</config>
