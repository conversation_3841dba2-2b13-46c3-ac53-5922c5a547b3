<?php

declare(strict_types=1);

namespace Comave\SellerStatusOnboarding\ViewModel;

use Comave\SellerStatusOnboarding\Model\SellerOnboardingDataComposite;
use Magento\Framework\View\Element\Block\ArgumentInterface;
use Webkul\Marketplace\Helper\Data;
use Webkul\Marketplace\Model\SellerFactory;
use Webkul\Marketplace\Model\ResourceModel\Seller;

class OnboardingData implements ArgumentInterface
{
    /**
     * @param Data $marketplaceHelper
     * @param Seller $resourceModel
     * @param SellerFactory $sellerFactory
     * @param SellerOnboardingDataComposite $sellerOnboardingDataComposite
     */
    public function __construct(
        private readonly Data $marketplaceHelper,
        private readonly Seller $resourceModel,
        private readonly SellerFactory $sellerFactory,
        private readonly SellerOnboardingDataComposite $sellerOnboardingDataComposite
    ) {

    }

    /**
     * @return string
     */
    public function getOnboardingSellerData(): string
    {
        $noData = json_encode([]);

        if (!$this->marketplaceHelper->isSeller()) {
            return $noData;
        }

        $seller = $this->sellerFactory->create();
        $this->resourceModel->load(
            $seller,
            $this->marketplaceHelper->getCustomerId(),
            'seller_id'
        );

        if (!$seller->getId()) {
            return $noData;
        }

        $data = $this->sellerOnboardingDataComposite->getStepData($seller);

        return json_encode($data);
    }
}
