<?php

declare(strict_types=1);

namespace Comave\SellerStatusOnboarding\Observer;

use Comave\SellerStatus\Model\ConfigProvider;
use Comave\SellerStatus\Model\RoleValidator\Onboarding;
use Comave\SellerStatusOnboarding\Model\SellerOnboardingDataComposite;
use Magento\Company\Api\Data\RoleInterface;
use Magento\Company\Api\RoleRepositoryInterface;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Webkul\Marketplace\Model\ResourceModel\Seller;
use Webkul\Marketplace\Model\SellerFactory;

class CheckFinishedOnboarding implements ObserverInterface
{
    /**
     * @param SellerFactory $sellerFactory
     * @param Seller $resourceModel
     * @param SellerOnboardingDataComposite $sellerOnboardingDataComposite
     * @param ConfigProvider $configProvider
     */
    public function __construct(
        private readonly SellerFactory $sellerFactory,
        private readonly Seller $resourceModel,
        private readonly SellerOnboardingDataComposite $sellerOnboardingDataComposite,
        private readonly ConfigProvider $configProvider
    ) {
    }

    /**
     * @param Observer $observer
     * @return void
     * @throws LocalizedException
     * @throws NoSuchEntityException
     */
    public function execute(Observer $observer): void
    {
        if ($this->configProvider->isTestModeEnabled()) {
            return;
        }

        $sellerId = $observer->getData('seller');

        if (!is_numeric($sellerId)) {
            throw new LocalizedException(__('Unable to determine seller, ID missing'));
        }

        $currentRole = $observer->getData('currentRole');

        if (!$currentRole instanceof RoleInterface) {
            throw new LocalizedException(__('Unable to determine seller role, ID %1', $sellerId));
        }

        $newRoleId = $observer->getData('newRoleId');

        if (empty($newRoleId)) {
            throw new LocalizedException(__('Unable to determine new role, ID missing'));
        }

        if ($currentRole->getRoleName() !== Onboarding::ROLE_NAME) {
            return;
        }

        $seller = $this->sellerFactory->create();
        $this->resourceModel->load($seller, $sellerId, 'seller_id');

        if (!$seller->getId()) {
            throw new NoSuchEntityException(__('Unable to determine seller, ID not found %1', $sellerId));
        }

        $onboardingData = $this->sellerOnboardingDataComposite->getStepData($seller);

        foreach ($onboardingData as $stepData) {
            if ($stepData['is_complete']) {
                continue;
            }

            unset($stepData['is_complete']);
            $unfinishedSteps = [];

            foreach ($stepData as $steps) {
                $unfinishedSteps[] = $steps['step_name'];
            }

            throw new LocalizedException(
                __(
                    'Current seller has not completed his onboarding process, missing %1 step(s)',
                    implode(',', $unfinishedSteps)
                )
            );
        }
    }
}
