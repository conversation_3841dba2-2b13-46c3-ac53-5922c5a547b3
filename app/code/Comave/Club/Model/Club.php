<?php
declare(strict_types=1);

namespace Comave\Club\Model;

use Comave\Club\Api\Data\ClubInterface;
use Comave\Club\Helper\Data;
use Comave\Club\Model\ResourceModel\Club\Collection;
use Magento\Catalog\Model\ResourceModel\Product\CollectionFactory;
use Magento\Framework\Data\Collection\AbstractDb;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Model\AbstractModel;
use Magento\Framework\Model\Context;
use Magento\Framework\Registry;
use Magento\Framework\UrlInterface;
use Magento\Store\Model\StoreManagerInterface;

/**
 * Club Model
 */
class Club extends AbstractModel implements ClubInterface
{
    /**
    * Club's Statuses
    */
    public const int STATUS_ENABLED = 1;
    public const int STATUS_DISABLED = 0;

    public function __construct(
        Context $context,
        Registry $registry,
        ResourceModel\Club $resource = null,
        Collection $resourceCollection = null,
        private readonly CollectionFactory $productCollectionFactory,
        private readonly StoreManagerInterface $storeManager,
        private readonly Data $clubHelper,
        private readonly ConfigProvider $configProvider,
        array $data = []
    )
    {
        parent::__construct($context, $registry, $resource, $resourceCollection, $data);
    }

    /**
     * Initialize customer model
     *
     * @return void
     */
    public function _construct()
    {
        $this->_init('Comave\Club\Model\ResourceModel\Club');
    }

    /**
     * Prepare page's statuses.
     *
     * @return array
     */
    public function getAvailableStatuses(): array
    {
        return [
            self::STATUS_ENABLED => __('Enabled'),
            self::STATUS_DISABLED => __('Disabled')
        ];
    }

    /**
     * Check if page identifier exist for specific store
     * return page id if page exists
     *
     * @param string $identifier
     * @param int $storeId
     * @return bool
     * @throws LocalizedException
     */
    public function checkIdentifier(string $identifier, int $storeId): bool
    {
        return (bool)$this->_getResource()->checkIdentifier($identifier, $storeId);
    }

    /**
     * Get category products collection
     *
     * @return AbstractDb
     */
    public function getProductCollection(): AbstractDb
    {
        $collection = $this->productCollectionFactory->create();
        $collection->addAttributeToSelect('*')
            ->addAttributeToFilter('product_club',
                array(
                    'eq'=>$this->getId()
                )
            );

        return $collection;
    }

    /**
     * @TODO: get url builder
     * Get club url
     *
     * @return string
     * @throws NoSuchEntityException
     */
    public function getUrl(): string
    {
        $url = $this->storeManager->getStore()->getBaseUrl();
        $storeId = $this->storeManager->getStore()->getId();
        $urlPrefix = $this->configProvider->getUrlPrefix((int)$storeId);
        $url_suffix = $this->configProvider->getUrlSuffix((int)$storeId);

        return $url . $urlPrefix . '/' . $this->getUrlKey() . $url_suffix;
    }

    /**
     * Retrieve image URL
     *
     * @return string
     * @throws NoSuchEntityException
     */
    public function getImageUrl(): string
    {
        $url = '';
        $image = $this->getImage();
        if ($image) {
            $url = $this->storeManager->getStore()->getBaseUrl(
                UrlInterface::URL_TYPE_MEDIA
                ) . $image;
        }

        return (string)$url;
    }

    /**
     * @TODO: Remove this method and replce it
     *
     * @param $club_name
     * @return $this
     * @throws LocalizedException
     */
    public function loadByClubName($club_name = "")
    {
        if($club_name) {
            $club_id = $this->_getResource()->getClubIdByName($club_name);
            if($club_id) {
                $this->load((int)$club_id);
            }
        }

        return $this;
    }

    /**
     * Save product association with club
     * 
     * Associates a product with the current club by delegating to the resource model.
     * Creates a relationship between the club and the specified product.
     * 
     * @param string $product_id Product ID to associate with the club
     * @return bool True if association was saved successfully, false otherwise
     */
    public function saveProduct($product_id = "0"): bool
    {
        if($product_id) {
            return $this->_getResource()->saveProduct($this, $product_id);
        }
        return false;
    }

    /**
     * Delete all club associations for a product
     * 
     * Removes all club associations for the specified product by delegating
     * to the resource model. This will dissociate the product from all clubs.
     * 
     * @param string $product_id Product ID to remove club associations for
     * @return bool True if deletion was successful, false otherwise
     */
    public function deleteClubsByProduct($product_id = "0"): bool
    {
        if($product_id) {
            return $this->_getResource()->deleteClubsByProduct($product_id);
        }

        return false;
    }

    /**
     * Retrive clogo URL
     * @TODO: get image path by asset
     *
     * @return string
     * @throws NoSuchEntityException
     */
    public function getClogoUrl(): string
    {
        $url = false;
        $clogo = $this->getClogo();
        if ($clogo) {
            $url = $this->storeManager->getStore()->getBaseUrl(
                UrlInterface::URL_TYPE_MEDIA
                ) . $clogo;
        }

        return (string)$url;
    }

    /**
     * Get club name
     * 
     * Retrieves the name of the club from the model data.
     * 
     * @return string Club name
     */
    public function getName(): string
    {
        return $this->getData(self::CLUB_NAME);
    }

    /**
     * Set club name
     * 
     * Sets the name of the club in the model data.
     * 
     * @param string $name Club name to set
     * @return self Returns this instance for method chaining
     */
    public function setName(string $name): self
    {
        return $this->setData(self::CLUB_NAME, $name);
    }

    public function getUrlKey(): string
    {
        return $this->getData(self::CLUB_URL_KEY);
    }

    public function setUrlKey(string $urlKey): self
    {
        return $this->setData(self::CLUB_URL_KEY, $urlKey);
    }

    public function getImage(): string
    {
        return $this->getData(self::CLUB_IMAGE);
    }

    public function setImage(string $image): self
    {
        return $this->setData(self::CLUB_IMAGE, $image);
    }

    public function getClogo(): string
    {
        return $this->getData(self::CLUB_LOGO);
    }

    public function setClogo($clogo): self
    {
        return $this->setData(self::CLUB_LOGO, $clogo);
    }

    public function getStatus(): int
    {
        return (int)$this->getData(self::CLUB_STATUS);
    }

    public function setStatus(int $status): self
    {
        return $this->setData(self::CLUB_STATUS, $status);
    }

    public function getUniqueId(): string
    {
        return $this->getData(self::CLUB_UNIQUE_ID);
    }

    public function setUniqueId(string $uniqueId): self
    {
        return $this->setData(self::CLUB_UNIQUE_ID, $uniqueId);
    }

    public function getClubBanner(): string
    {
        return $this->getData(self::CLUB_BANNER);
    }

    public function setClubBanner(string $clubBanner): self
    {
        return $this->setData(self::CLUB_BANNER, $clubBanner);
    }

    /**
     * @return string
     */
    public function getClubWatermarkImage(): string
    {
        return $this->getData(self::CLUB_WATERMARK_IMAGE);
    }

    public function getClubBannerUrl(): string
    {
        if (!empty($this->getClubBanner())) {
            return $this->clubHelper->getMediaUrl() . $this->getClubBanner();
        }

        return '';
    }

    /**
     * @inherit
     */
    public function getPartnered(): int
    {
        return (int)$this->getData(self::CLUB_PARTNERED);
    }

    /**
     * @inherit
     */
    public function setPartnered(int $partnered): self
    {
        return $this->setData(self::CLUB_PARTNERED, $partnered);
    }
}
