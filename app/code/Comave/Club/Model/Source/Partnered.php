<?php
declare(strict_types=1);

namespace Comave\Club\Model\Source;

use Magento\Framework\Data\OptionSourceInterface;

/**
 * Partnered Source Model
 */
class Partnered implements OptionSourceInterface
{
    /**
     * Return array of options as value-label pairs
     *
     * @return array
     */
    public function toOptionArray(): array
    {
        return [
            ['value' => 0, 'label' => __('No')],
            ['value' => 1, 'label' => __('Yes')]
        ];
    }
}