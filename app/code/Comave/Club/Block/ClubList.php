<?php
/**
 * Copyright © Commercial Avenue
 */
namespace Comave\Club\Block;
use Magento\Customer\Model\Context as CustomerContext;

class ClubList extends \Magento\Framework\View\Element\Template
{
    /**
     * Group Collection
     */
    protected $_clubCollection;

    protected $_collection = null;

	/**
     * Core registry
     *
     * @var \Magento\Framework\Registry
     */
    protected $_coreRegistry = null;

    /**
     * @var \Magento\Catalog\Helper\Category
     */
    protected $_clubHelper;

    /**
     * @var \Magento\Framework\App\Http\Context
     */
    protected $httpContext;

    /**
     * @param \Magento\Framework\View\Element\Template\Context $context         
     * @param \Magento\Framework\Registry                      $registry        
     * @param \Comave\Club\Helper\Data                           $clubHelper     
     * @param \Comave\Club\Model\Club                           $clubCollection 
     * @param array                                            $data            
     */
    public function __construct(
        \Magento\Framework\View\Element\Template\Context $context,
        \Magento\Framework\Registry $registry,
        \Comave\Club\Helper\Data $clubHelper,
        \Comave\Club\Model\Club $clubCollection,
        \Magento\Framework\App\Http\Context $httpContext,
        array $data = []
        ) {
        $this->_clubCollection = $clubCollection;
        $this->_clubHelper = $clubHelper;
        $this->_coreRegistry = $registry;
        $this->httpContext = $httpContext;
        parent::__construct($context, $data);
    }

    /**
     * Initialize block with template and configuration settings
     * 
     * Sets up the appropriate template based on carousel layout configuration
     * and validates module and block settings before initialization.
     * 
     * @return void
     */
    public function _construct(){
        if(!$this->getConfig('general_settings/enable') || !$this->getConfig('club_block/enable')) return;
        parent::_construct();
        $carousel_layout = $this->getConfig('club_block/carousel_layout');
        $template = '';
        if($carousel_layout == 'owl_carousel'){
            $template = 'block/club_list_owl.phtml';
        }else{
            $template = 'block/club_list_bootstrap.phtml';
        }
        if(!$this->getTemplate() && $template!=''){
            $this->setTemplate($template);
        }
    }

    /**
     * Get configuration value by key with optional default fallback
     * 
     * Retrieves configuration values from widget data or helper config.
     * Supports nested keys with forward slash separator.
     * 
     * @param string $key Configuration key (e.g., 'general_settings/enable')
     * @param string $default Default value to return if config is empty
     * @return mixed Configuration value or default
     */
    public function getConfig($key, $default = '')
    {   
        $widget_key = explode('/', $key);
        if( (count($widget_key)==2) && ($resultData = $this->hasData($widget_key[1])) )
        {
            return $this->getData($widget_key[1]);
        }
        $result = $this->_clubHelper->getConfig($key);
        if($result == ""){
            return $default;
        }
        return $result;
    }

    /**
     * Get filtered club collection for display
     * 
     * Returns a collection of active clubs filtered by group and store,
     * ordered by position with pagination applied.
     * 
     * @return \Comave\Club\Model\ResourceModel\Club\Collection
     */
    public function getClubCollection()
    {
        if(!$this->_collection) {
            $number_item = $this->getConfig('club_block/number_item');
            $clubGroups = $this->getConfig('club_block/club_groups');
            $store = $this->_storeManager->getStore();
            $collection = $this->_clubCollection->getCollection()
            ->setOrder('position','ASC')
            ->addStoreFilter($store)
            ->addFieldToFilter('status',1);
            $clubGroups = explode(',', $clubGroups);
            if(is_array($clubGroups) && count($clubGroups)>0)
            {
                $collection->addFieldToFilter('group_id',array('in' => $clubGroups));
            }
            $collection->setPageSize($number_item)
            ->setCurPage(1)
            ->setOrder('position','ASC');
            $this->_collection = $collection;
        }
        return $this->_collection;
    }


    /**
     * Get Key pieces for caching block content
     *
     * @return array
     */
    public function getCacheKeyInfo()
    {
        return [
        'MAGETOP_BRAND_LIST',
        $this->_storeManager->getStore()->getId(),
        $this->_design->getDesignTheme()->getId(),
        $this->httpContext->getValue(CustomerContext::CONTEXT_GROUP),
        'template' => $this->getTemplate(),
        $this->getProductsCount()
        ];
    }

    /**
     * Render block HTML output
     * 
     * Generates and returns the HTML content for the club list block.
     * This method is called during the rendering process to produce the final output.
     * 
     * @return string Generated HTML content
     */
    public function _toHtml()
    {
        return parent::_toHtml();
    }
}