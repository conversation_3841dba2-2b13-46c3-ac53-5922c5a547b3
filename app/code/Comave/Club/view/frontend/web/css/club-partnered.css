/* Partnered Club Styling */
.club-item {
    position: relative;
}

.club-item .club-image {
    position: relative;
    display: inline-block;
}

.partnered-badge {
    position: absolute;
    top: 8px;
    right: 8px;
    background: linear-gradient(45deg, #FFD700, #FFA500);
    color: #fff;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: bold;
    text-transform: uppercase;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    z-index: 10;
    border: 1px solid #FFD700;
}

.partnered-indicator {
    color: #FFD700;
    font-size: 16px;
    margin-left: 5px;
    display: inline-block;
    animation: sparkle 2s ease-in-out infinite;
}

@keyframes sparkle {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.8;
    }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .partnered-badge {
        font-size: 8px;
        padding: 2px 6px;
        top: 4px;
        right: 4px;
    }
    
    .partnered-indicator {
        font-size: 14px;
    }
}

/* Special styling for club listings */
.clublist .club-item.partnered {
    border: 2px solid #FFD700;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(255, 215, 0, 0.3);
}

.club-widget .club-item.partnered {
    background: linear-gradient(135deg, rgba(255, 215, 0, 0.1), rgba(255, 165, 0, 0.1));
    border-radius: 8px;
    padding: 8px;
}

/* Hover effects */
.club-item:hover .partnered-badge {
    background: linear-gradient(45deg, #FFA500, #FFD700);
    transform: scale(1.1);
    transition: all 0.3s ease;
}

.club-item:hover .partnered-indicator {
    color: #FFA500;
    transform: scale(1.3);
    transition: all 0.3s ease;
}