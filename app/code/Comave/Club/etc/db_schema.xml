<?xml version="1.0"?>
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
    <table name="comave_club" resource="default" engine="innodb"
           comment="Comave Club">
        <column xsi:type="int" name="club_id" unsigned="true" nullable="false" identity="true"
                comment="Club ID"/>
        <column xsi:type="varchar" name="orgid" nullable="false" length="255" comment="Lix Organisation ID"/>
        <column xsi:type="varchar" name="uniqueid" nullable="false" length="255" comment="Lix Club Unique ID"/>
        <column xsi:type="varchar" name="name" nullable="false" length="255" comment="Club Name"/>
        <column xsi:type="text" name="subtitle" nullable="false" comment="Sub Title"/>
        <column xsi:type="varchar" name="url_key" nullable="false" length="255" comment="Club Url Key"/>
        <column xsi:type="text" name="description" nullable="false" comment="Club Description"/>
        <column xsi:type="int" name="group_id" unsigned="true" nullable="false" identity="false" comment="Group ID"/>
        <column xsi:type="varchar" name="image" nullable="false" length="255" comment="Club Image"/>
        <column xsi:type="varchar" name="clogo" nullable="false" length="255" comment="Club Logo"/>
        <column xsi:type="timestamp" name="creation_time" on_update="false" nullable="false" default="CURRENT_TIMESTAMP"
                comment="Club Creation Time"/>
        <column xsi:type="timestamp" name="update_time" on_update="true" nullable="false" default="CURRENT_TIMESTAMP"
                comment="Club Modification Time"/>
        <column xsi:type="smallint" name="status" unsigned="true" nullable="false" identity="false" default="1"
                comment="Status"/>
        <column xsi:type="int" name="position" unsigned="true" nullable="false" identity="false" default="0"
                comment="Position"/>
        <column xsi:type="varchar" name="club_prefix" nullable="false" length="255" comment="Club Prefix"/>
        <column xsi:type="int" name="club_count" unsigned="true" nullable="false" identity="false" default="0"
                comment="Club Count"/>
        <column xsi:type="varchar" name="club_banner" nullable="false" length="255" comment="Club Banner"/>
        <column xsi:type="varchar" name="club_watermark_image" nullable="false" length="255"
                comment="Club Watermark Image"/>
        <column xsi:type="smallint" name="partnered" unsigned="true" nullable="false" identity="false" default="0"
                comment="Partnered Status"/>
        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="club_id"/>
        </constraint>

        <constraint xsi:type="unique" referenceId="COMAVE_CLUB_NAME">
            <column name="name"/>
        </constraint>
        <constraint xsi:type="unique" referenceId="COMAVE_CLUB_URL_KEY">
            <column name="url_key"/>
        </constraint>
        <constraint xsi:type="unique" referenceId="COMAVE_CLUB_UNIQUE_ID">
            <column name="uniqueid"/>
        </constraint>

        <index referenceId="COMAVE_CLUB_GROUP_ID" indexType="btree">
            <column name="group_id"/>
        </index>
        <index referenceId="COMAVE_CLUB_PARTNERED" indexType="btree">
            <column name="partnered"/>
        </index>
        <constraint xsi:type="foreign"
                    referenceId="COMAVE_CLUB_GROUP_ID_COMAVE_CLUB_GROUP_ID"
                    table="comave_club" column="group_id"
                    referenceTable="comave_club_group" referenceColumn="group_id"
                    onDelete="CASCADE"/>
    </table>
    <table name="comave_club_group" resource="default" engine="innodb"
           comment="Comave Club Group">
        <column xsi:type="int" name="group_id" unsigned="true" nullable="false" identity="true"
                comment="Group ID"/>
        <column xsi:type="varchar" name="name" nullable="false" length="255" comment="Group Name"/>
        <column xsi:type="varchar" name="url_key" nullable="false" length="255" comment="Group Url Key"/>
        <column xsi:type="int" name="position" unsigned="true" nullable="false" identity="false" default="0"
                comment="Position"/>
        <column xsi:type="smallint" name="status" unsigned="true" nullable="false" identity="false" default="1"
                comment="Status"/>
        <column xsi:type="smallint" name="shown_in_sidebar" unsigned="true" nullable="false" identity="false"
                default="1"
                comment="Show In Sidebar"/>
        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="group_id"/>
        </constraint>
    </table>
    <table name="comave_club_store" resource="default" engine="innodb"
           comment="Comave Club Store">
        <column xsi:type="int" name="club_id" unsigned="true" nullable="false" identity="false"
                comment="Club ID"/>
        <column xsi:type="smallint" name="store_id" unsigned="true" nullable="false" identity="false"
                comment="Store ID"/>
        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="club_id"/>
            <column name="store_id"/>
        </constraint>
        <index referenceId="COMAVE_CLUB_STORE_STORE_ID" indexType="btree">
            <column name="store_id"/>
        </index>
        <index referenceId="COMAVE_CLUB_STORE_CLUB_ID" indexType="btree">
            <column name="club_id"/>
        </index>
        <constraint xsi:type="foreign"
                    referenceId="COMAVE_CLUB_STORE_CLUB_ID_COMAVE_CLUB_CLUB_ID"
                    table="comave_club_store" column="club_id"
                    referenceTable="comave_club" referenceColumn="club_id"
                    onDelete="CASCADE"/>
        <constraint xsi:type="foreign"
                    referenceId="COMAVE_CLUB_STORE_STORE_ID_STORE_STORE_ID"
                    table="comave_club_store" column="store_id"
                    referenceTable="store" referenceColumn="store_id"
                    onDelete="CASCADE"/>
    </table>
    <table name="comave_club_product" resource="default" engine="innodb"
           comment="Comave Club Product Link">
        <column xsi:type="int" name="club_id" unsigned="true" nullable="false" identity="false"
                comment="Club ID"/>
        <column xsi:type="int" name="product_id" unsigned="true" nullable="false" identity="false"
                comment="Club Product ID"/>
        <column xsi:type="int" name="position" unsigned="true" nullable="false" identity="false" default="0"
                comment="Club Product Position"/>
        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="club_id"/>
            <column name="product_id"/>
        </constraint>
        <index referenceId="COMAVE_CLUB_PRODUCT_PRODUCT_ID" indexType="btree">
            <column name="product_id"/>
        </index>
        <index referenceId="COMAVE_CLUB_PRODUCT_CLUB_ID" indexType="btree">
            <column name="club_id"/>
        </index>
        <constraint xsi:type="foreign"
                    referenceId="OMAVE_CLUB_PRODUCT_PRDDUCT_ID_CATALOG_PRODUCT_ENTITY_ENTITY_ID"
                    table="comave_club_product" column="product_id"
                    referenceTable="catalog_product_entity" referenceColumn="entity_id"
                    onDelete="CASCADE"/>
    </table>
    <table name="coditron_club" resource="default" engine="innodb"
           comment="Comave Club" disabled="true"> </table>
    <table name="coditron_club_group" resource="default" engine="innodb"
           comment="Comave Club Group" disabled="true"> </table>
    <table name="coditron_club_store" resource="default" engine="innodb"
           comment="Comave Club Store" disabled="true"> </table>
    <table name="coditron_club_product" resource="default" engine="innodb"
           comment="Comave Club Product Link" disabled="true"> </table>
</schema>