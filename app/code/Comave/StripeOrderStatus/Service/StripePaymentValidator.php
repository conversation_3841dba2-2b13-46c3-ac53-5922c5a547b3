<?php

declare(strict_types=1);

namespace Comave\StripeOrderStatus\Service;

use Magento\Sales\Api\OrderRepositoryInterface;
use Magento\Sales\Model\Order;
use Magento\Framework\Exception\NoSuchEntityException;
use Psr\Log\LoggerInterface;

/**
 * Service class to validate Stripe payment data and extract payment intent IDs
 * 
 * This validator focuses on determining if an order has Stripe payment method
 * and extracting relevant payment identifiers from order payment data.
 */
class StripePaymentValidator
{
    /**
     * @param OrderRepositoryInterface $orderRepository
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly OrderRepositoryInterface $orderRepository,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * Check if order has Stripe payment method
     *
     * @param int $orderId
     * @return bool
     */
    public function hasStripePayment(int $orderId): bool
    {
        try {
            $order = $this->orderRepository->get($orderId);
            return $this->isStripePaymentMethod($order);
        } catch (NoSuchEntityException $e) {
            $this->logger->warning("Order not found: {$orderId}");
            return false;
        } catch (\Exception $e) {
            $this->logger->error("Error validating Stripe payment for order {$orderId}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Check if order payment method is Stripe-based
     *
     * @param Order $order
     * @return bool
     */
    public function isStripePaymentMethod(Order $order): bool
    {
        $payment = $order->getPayment();
        if (!$payment) {
            return false;
        }

        $paymentMethod = $payment->getMethod();
        return str_contains($paymentMethod, 'stripe');
    }

    /**
     * Extract Stripe payment intent ID or transaction ID from order
     *
     * @param int $orderId
     * @return string|null
     */
    public function getStripeTransactionId(int $orderId): ?string
    {
        try {
            $order = $this->orderRepository->get($orderId);
            return $this->extractTransactionId($order);
        } catch (\Exception $e) {
            $this->logger->error("Error getting transaction ID for order {$orderId}: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Extract transaction ID from order payment
     *
     * @param Order $order
     * @return string|null
     */
    private function extractTransactionId(Order $order): ?string
    {
        $payment = $order->getPayment();
        if (!$payment) {
            return null;
        }

        // Try last transaction ID first
        $transactionId = $payment->getLastTransId();
        if (!empty($transactionId)) {
            return $this->cleanTransactionId($transactionId);
        }

        // Try additional information
        $additionalInfo = $payment->getAdditionalInformation();
        
        // Check various possible fields
        $possibleFields = [
            'payment_intent',
            'payment_intent_id',
            'last_charge_id',
            'checkout_session_id'
        ];

        foreach ($possibleFields as $field) {
            if (isset($additionalInfo[$field]) && !empty($additionalInfo[$field])) {
                return $this->cleanTransactionId($additionalInfo[$field]);
            }
        }

        return null;
    }

    /**
     * Clean and validate transaction ID format
     *
     * @param string $transactionId
     * @return string|null
     */
    private function cleanTransactionId(string $transactionId): ?string
    {
        $cleaned = trim($transactionId);
        
        // Validate Stripe ID format (payment intent or charge)
        if (preg_match('/^(pi_|ch_)[a-zA-Z0-9_]+$/', $cleaned)) {
            return $cleaned;
        }

        return null;
    }
}
