<?php

declare(strict_types=1);

namespace Comave\StripeOrderStatus\Ui\Component\Listing\Column;

use Magento\Framework\View\Element\UiComponent\ContextInterface;
use Magento\Framework\View\Element\UiComponentFactory;
use Magento\Ui\Component\Listing\Columns\Column;
use Comave\StripeOrderStatus\Service\StripeStatusProcessor;
use Comave\StripeOrderStatus\Trait\StripeStatusRenderer;

/**
 * Stripe payment status column component for admin order grid
 * 
 * This component handles all business logic for retrieving, processing,
 * and formatting Stripe payment status data for display in the grid.
 * It integrates with the StripeStatusProcessor service to fetch status data.
 */
class StripeStatus extends Column
{
    use StripeStatusRenderer;
    /**
     * @param ContextInterface $context
     * @param UiComponentFactory $uiComponentFactory
     * @param StripeStatusProcessor $statusProcessor
     * @param array<string, mixed> $components
     * @param array<string, mixed> $data
     */
    public function __construct(
        ContextInterface $context,
        UiComponentFactory $uiComponentFactory,
        private readonly StripeStatusProcessor $statusProcessor,
        array $components = [],
        array $data = []
    ) {
        parent::__construct($context, $uiComponentFactory, $components, $data);
    }

    /**
     * Prepare data source with Stripe payment status
     * 
     * This method handles all the business logic for fetching and formatting
     * Stripe status data for each order in the grid. It replaces the functionality
     * that was previously handled by the plugin.
     *
     * @param array<string, mixed> $dataSource
     * @return array<string, mixed>
     */
    public function prepareDataSource(array $dataSource): array
    {
        if (isset($dataSource['data']['items'])) {
            foreach ($dataSource['data']['items'] as &$item) {
                $item[$this->getData('name')] = $this->formatStripeStatus($item);
            }
        }

        return $dataSource;
    }

    /**
     * Format Stripe status for display including fetching data and creating badge
     *
     * @param array<string, mixed> $item
     * @return string
     */
    private function formatStripeStatus(array $item): string
    {
        $orderId = (int)($item['entity_id'] ?? 0);
        $status = $this->getStripeStatusSafely($orderId, $this->statusProcessor);
        
        return $this->createStatusBadge($status, $this->statusProcessor);
    }
}
