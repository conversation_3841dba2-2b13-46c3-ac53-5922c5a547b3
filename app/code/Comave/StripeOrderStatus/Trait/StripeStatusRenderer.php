<?php

declare(strict_types=1);

namespace Comave\StripeOrderStatus\Trait;

use Comave\StripeOrderStatus\Service\StripeStatusProcessor;

/**
 * Trait for rendering Stripe payment status badges
 * 
 * Provides common functionality for creating HTML status badges
 * across different grid implementations (UI Component and Block).
 */
trait StripeStatusRenderer
{
    /**
     * Create HTML badge for status display
     *
     * @param string $status
     * @param StripeStatusProcessor $statusProcessor
     * @param callable|null $escapeHtml Optional HTML escaper function
     * @param callable|null $escapeHtmlAttr Optional HTML attribute escaper function
     * @return string
     */
    protected function createStatusBadge(
        string $status, 
        StripeStatusProcessor $statusProcessor,
        ?callable $escapeHtml = null,
        ?callable $escapeHtmlAttr = null
    ): string {
        $label = $statusProcessor->getStatusLabel($status);
        $cssClass = $statusProcessor->getStatusCssClass($status);
        
        // Use provided escapers or fallback to built-in
        $escapeHtml = $escapeHtml ?: fn($content) => htmlspecialchars($content, ENT_QUOTES | ENT_SUBSTITUTE, 'UTF-8');
        $escapeHtmlAttr = $escapeHtmlAttr ?: $escapeHtml;
        
        return sprintf(
            '<span class="%s"><span>%s</span></span>',
            $escapeHtmlAttr($cssClass),
            $escapeHtml((string)$label)
        );
    }

    /**
     * Get Stripe status with error handling
     *
     * @param int $orderId
     * @param StripeStatusProcessor $statusProcessor
     * @return string
     */
    protected function getStripeStatusSafely(int $orderId, StripeStatusProcessor $statusProcessor): string
    {
        if ($orderId === 0) {
            return 'unknown';
        }

        try {
            return $statusProcessor->getStatusForOrder($orderId);
        } catch (\Exception $e) {
            return 'unknown';
        }
    }
}
