<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceBlock name="sales.transactions.grid.columnSet">
            <block class="Magento\Backend\Block\Widget\Grid\Column" name="sales.transactions.grid.columnSet.stripe_payment_status" as="stripe_payment_status">
                <arguments>
                    <argument name="header" xsi:type="string" translate="true">Stripe Payment Status</argument>
                    <argument name="index" xsi:type="string">increment_id</argument>
                    <argument name="renderer" xsi:type="string">Comave\StripeOrderStatus\Block\Adminhtml\Grid\Column\StripeStatus</argument>
                    <argument name="filter" xsi:type="boolean">false</argument>
                    <argument name="sortable" xsi:type="boolean">false</argument>
                </arguments>
            </block>
        </referenceBlock>
    </body>
</page>
