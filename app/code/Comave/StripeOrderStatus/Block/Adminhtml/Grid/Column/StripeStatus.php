<?php

declare(strict_types=1);

namespace Comave\StripeOrderStatus\Block\Adminhtml\Grid\Column;

use Magento\Backend\Block\Widget\Grid\Column\Renderer\AbstractRenderer;
use Magento\Framework\DataObject;
use Comave\StripeOrderStatus\Service\StripeStatusProcessor;
use Comave\StripeOrderStatus\Trait\StripeStatusRenderer;

/**
 * Renders the Stripe Payment Status column in the admin grid.
 */
class StripeStatus extends AbstractRenderer
{
    use StripeStatusRenderer;

    /**
     * @param StripeStatusProcessor $statusProcessor
     * @param \Magento\Backend\Block\Context $context
     * @param array $data
     */
    public function __construct(
        private readonly StripeStatusProcessor $statusProcessor,
        \Magento\Backend\Block\Context $context,
        array $data = []
    ) {
        parent::__construct($context, $data);
    }

    /**
     * Renders the grid column.
     *
     * @param DataObject $row
     * @return string
     */
    public function render(DataObject $row): string
    {
        $orderId = (int)$row->getData($this->getColumn()->getIndex());
        $status = $this->getStripeStatusSafely($orderId, $this->statusProcessor);
        
        return $this->createStatusBadge(
            $status,
            $this->statusProcessor,
            fn($content) => $this->escapeHtml($content),
            fn($content) => $this->escapeHtmlAttr($content)
        );
    }
}
