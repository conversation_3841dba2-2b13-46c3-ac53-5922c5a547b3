<?php

declare(strict_types=1);

namespace Comave\StripeOrderStatus\Model\Source;

use Magento\Framework\Data\OptionSourceInterface;

/**
 * Option source for Stripe payment statuses
 * 
 * Provides status mappings, labels, and CSS classes for Stripe payment statuses
 */
class StripePaymentStatus implements OptionSourceInterface
{
    /**
     * Status constants
     */
    public const STATUS_PENDING = 'Pending';
    public const STATUS_UNCAPTURED = 'Uncaptured';
    public const STATUS_SUCCEEDED = 'Succeeded';
    public const STATUS_CANCELED = 'Canceled';
    public const STATUS_NOT_STRIPE = 'not_stripe';
    public const STATUS_NO_INTENT = 'no_intent';
    public const STATUS_API_ERROR = 'api_error';
    public const STATUS_UNKNOWN = 'unknown';

    /**
     * Map Stripe API status to user-friendly status
     *
     * @param string $stripeStatus
     * @return string
     */
    public function mapStripeStatus(string $stripeStatus): string
    {
        return match ($stripeStatus) {
            'requires_payment_method', 'requires_confirmation', 'requires_action', 'processing', 'pending' => self::STATUS_PENDING,
            'requires_capture' => self::STATUS_UNCAPTURED,
            'succeeded' => self::STATUS_SUCCEEDED,
            'canceled', 'cancelled' => self::STATUS_CANCELED,
            default => ucwords(str_replace('_', ' ', $stripeStatus))
        };
    }

    /**
     * Get user-friendly label for status
     *
     * @param string $status
     * @return \Magento\Framework\Phrase
     */
    public function getStatusLabel(string $status): \Magento\Framework\Phrase
    {
        return match ($status) {
            self::STATUS_PENDING => __('Pending'),
            self::STATUS_UNCAPTURED => __('Uncaptured'),
            self::STATUS_SUCCEEDED => __('Succeeded'),
            self::STATUS_CANCELED => __('Canceled'),
            self::STATUS_NOT_STRIPE => __('Not Stripe'),
            self::STATUS_NO_INTENT => __('No Intent'),
            self::STATUS_API_ERROR => __('API Error'),
            default => __($status)
        };
    }

    /**
     * Get CSS class for status styling
     *
     * @param string $status
     * @return string
     */
    public function getStatusCssClass(string $status): string
    {
        return match ($status) {
            self::STATUS_SUCCEEDED => 'grid-severity-notice stripe-payment-status-succeeded',
            self::STATUS_UNCAPTURED => 'grid-severity-minor stripe-payment-status-uncaptured',
            self::STATUS_PENDING => 'grid-severity-minor stripe-payment-status-pending',
            self::STATUS_CANCELED => 'grid-severity-critical stripe-payment-status-canceled',
            self::STATUS_NOT_STRIPE => 'grid-severity-minor stripe-payment-status-not-stripe',
            self::STATUS_NO_INTENT => 'grid-severity-minor stripe-payment-status-no-intent',
            self::STATUS_API_ERROR => 'grid-severity-major stripe-payment-status-api-error',
            default => 'grid-severity-minor stripe-payment-status-unknown'
        };
    }

    /**
     * Check if status indicates an error condition
     *
     * @param string $status
     * @return bool
     */
    public function isErrorStatus(string $status): bool
    {
        return in_array($status, [self::STATUS_API_ERROR, self::STATUS_NO_INTENT], true);
    }

    /**
     * Get array of all available statuses for option source
     *
     * @return array
     */
    public function toOptionArray(): array
    {
        return [
            ['value' => self::STATUS_PENDING, 'label' => $this->getStatusLabel(self::STATUS_PENDING)],
            ['value' => self::STATUS_UNCAPTURED, 'label' => $this->getStatusLabel(self::STATUS_UNCAPTURED)],
            ['value' => self::STATUS_SUCCEEDED, 'label' => $this->getStatusLabel(self::STATUS_SUCCEEDED)],
            ['value' => self::STATUS_CANCELED, 'label' => $this->getStatusLabel(self::STATUS_CANCELED)],
            ['value' => self::STATUS_NOT_STRIPE, 'label' => $this->getStatusLabel(self::STATUS_NOT_STRIPE)],
            ['value' => self::STATUS_NO_INTENT, 'label' => $this->getStatusLabel(self::STATUS_NO_INTENT)],
            ['value' => self::STATUS_API_ERROR, 'label' => $this->getStatusLabel(self::STATUS_API_ERROR)],
        ];
    }
}
