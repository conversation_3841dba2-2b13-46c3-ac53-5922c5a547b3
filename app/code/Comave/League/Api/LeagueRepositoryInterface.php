<?php
declare(strict_types=1);

namespace Comave\League\Api;

use Comave\League\Api\Data\LeagueInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;

/**
 * League repository interface
 *
 * @api
 */
interface LeagueRepositoryInterface
{
    /**
     * Save league.
     *
     * @param LeagueInterface $league
     * @return LeagueInterface
     * @throws LocalizedException
     */
    public function save(LeagueInterface $league);

    /**
     * Retrieve league.
     *
     * @param int $leagueId
     * @return LeagueInterface
     * @throws NoSuchEntityException
     */
    public function get(int $leagueId);

    /**
     * Delete league.
     *
     * @param LeagueInterface $league
     * @return bool true on success
     * @throws LocalizedException
     */
    public function delete(LeagueInterface $league);

    /**
     * Delete league by ID.
     *
     * @param int $leagueId
     * @return bool true on success
     * @throws NoSuchEntityException
     * @throws LocalizedException
     */
    public function deleteById(int $leagueId);

    /**
     * Clear caches instances
     *
     * @return void
     */
    public function clear(): void;
}