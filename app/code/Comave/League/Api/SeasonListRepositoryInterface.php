<?php
declare(strict_types=1);

namespace Comave\League\Api;

use Comave\League\Api\Data\SeasonSearchResultInterface;
use Magento\Framework\Api\SearchCriteriaInterface;
use Magento\Framework\Exception\LocalizedException;

/**
 * Season list repository interface.
 * @api
 */
interface SeasonListRepositoryInterface
{
    /**
     * Retrieve seasons matching the specified criteria.
     *
     * @param SearchCriteriaInterface $searchCriteria
     * @return SeasonSearchResultInterface
     * @throws LocalizedException
     */
    public function getList(SearchCriteriaInterface $searchCriteria);
}