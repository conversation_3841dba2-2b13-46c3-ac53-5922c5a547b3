<?php
declare(strict_types=1);

namespace Comave\League\Api;

use Magento\Framework\Api\SearchCriteriaInterface;
use Comave\League\Api\Data\LeagueSearchResultInterface;
use Magento\Framework\Exception\LocalizedException;

/**
 * League list repository interface for retrieving filtered league collections
 *
 * Provides methods to fetch league entities based on search criteria,
 * supporting filtered and paginated league data retrieval.
 *
 * @api
 */
interface LeagueListRepositoryInterface
{
    /**
     * @param SearchCriteriaInterface $searchCriteria
     * @return LeagueSearchResultInterface
     * @throws LocalizedException
     */
    public function getList(SearchCriteriaInterface $searchCriteria);
}