<?php
declare(strict_types=1);

namespace Comave\League\Api\Data;

use Magento\Framework\Api\ExtensibleDataInterface;

/**
 * Season interface.
 * @api
 */
interface SeasonInterface extends ExtensibleDataInterface
{
    public const int STATUS_ENABLED = 1;
    public const int STATUS_DISABLED = 0;

    public const string SEASON_ID = 'season_id';
    public const string LEAGUE_ID = 'league_id';
    public const string NAME = 'name';
    public const string START_DATE = 'start_date';
    public const string END_DATE = 'end_date';
    public const string MAX_CLUBS = 'max_clubs';
    public const string IS_CURRENT = 'is_current';
    public const string CREATED_AT = 'created_at';
    public const string UPDATED_AT = 'updated_at';

    /**
     * Get season id
     *
     * @return int|null
     */
    public function getId();

    /**
     * Set season id
     *
     * @param int $id
     * @return $this
     */
    public function setId($id);

    /**
     * @param int $id
     * @return self
     */
    public function setSeasonId(int $id): self;

    /**
     * @return int|null
     */
    public function getSeasonId(): ?int;

    /**
     * Get season name
     *
     * @return string|null
     */
    public function getName(): ?string;

    /**
     * Set season name
     *
     * @param string $name
     * @return $this
     */
    public function setName(string $name): self;

    /**
     * Get season league id
     *
     * @return int|null
     */
    public function getLeagueId(): ?int;

    /**
     * Set season league id
     *
     * @param int $leagueId
     * @return $this
     */
    public function setLeagueId(int $leagueId): self;

    /**
     * Get start date
     *
     * @return string|null
     */
    public function getStartDate(): ?string;

    /**
     * Set start date
     *
     * @param string $startDate
     * @return $this
     */
    public function setStartDate(string $startDate): self;

    /**
     * Get end date
     *
     * @return string|null
     */
    public function getEndDate(): ?string;

    /**
     * Set end date
     *
     * @param string $endDate
     * @return $this
     */
    public function setEndDate(string $endDate): self;

    /**
     * Get is current
     *
     * @return int|null
     */
    public function getIsCurrent(): ?int;

    /**
     * Set is current
     *
     * @param int $isCurrent
     * @return $this
     */
    public function setIsCurrent(int $isCurrent): self;

    /**
     * Get maximum clubs allowed
     *
     * @return int|null
     */
    public function getMaxClubs(): ?int;

    /**
     * Set maximum clubs allowed
     *
     * @param int $maxClubs
     * @return $this
     */
    public function setMaxClubs(int $maxClubs): self;

    /**
     * Get created at
     *
     * @return string|null
     */
    public function getCreatedAt(): ?string;

    /**
     * Set created at
     *
     * @param string $createdAt
     * @return $this
     */
    public function setCreatedAt(string $createdAt): self;

    /**
     * Get updated at
     *
     * @return string|null
     */
    public function getUpdatedAt(): ?string;

    /**
     * Set updated at
     *
     * @param string $updatedAt
     * @return $this
     */
    public function setUpdatedAt(string $updatedAt): self;
}