<?php
declare(strict_types=1);

namespace Comave\League\Api\Data;

use Magento\Framework\Api\ExtensibleDataInterface;

/**
 * League interface.
 * @api
 */
interface LeagueInterface extends ExtensibleDataInterface
{
    public const int STATUS_ENABLED = 1;
    public const int STATUS_DISABLED = 0;

    public const string LEAGUE_ID = 'league_id';
    public const string NAME = 'name';
    public const string CODE = 'code';
    public const string DESCRIPTION = 'description';
    public const string COUNTRY_CODE = 'country_code';
    public const string LOGO = 'logo';
    public const string STATUS = 'status';
    public const string CREATED_AT = 'created_at';
    public const string UPDATED_AT = 'updated_at';

    /**
     * Get league id
     *
     * @return int|null
     */
    public function getId();

    /**
     * Set league id
     *
     * @param int $id
     * @return $this
     */
    public function setId($id);

    /**
     * @param int $id
     * @return self
     */
    public function setLeagueId(int $id): self;

    /**
     * @return int|null
     */
    public function getLeagueId(): ?int;

    /**
     * Get league name
     *
     * @return string|null
     */
    public function getName(): ?string;

    /**
     * Set league name
     *
     * @param string $name
     * @return $this
     */
    public function setName(string $name): self;

    /**
     * Get league code
     *
     * @return string|null
     */
    public function getCode(): ?string;

    /**
     * Set league code
     *
     * @param string $code
     * @return $this
     */
    public function setCode(string $code): self;

    /**
     * Get description
     *
     * @return string|null
     */
    public function getDescription(): ?string;

    /**
     * Set description
     *
     * @param string $description
     * @return $this
     */
    public function setDescription(string $description): self;

    /**
     * Get country code
     *
     * @return string|null
     */
    public function getCountryCode(): ?string;

    /**
     * Set country code
     *
     * @param string $countryCode
     * @return $this
     */
    public function setCountryCode(string $countryCode): self;

    /**
     * Get logo
     *
     * @return string|null
     */
    public function getLogo(): ?string;

    /**
     * Set logo
     *
     * @param string $logo
     * @return $this
     */
    public function setLogo(string $logo): self;

    /**
     * Get status
     *
     * @return int|null
     */
    public function getStatus(): ?int;

    /**
     * Set status
     *
     * @param int $status
     * @return $this
     */
    public function setStatus(int $status): self;

    /**
     * Get created at
     *
     * @return string|null
     */
    public function getCreatedAt(): ?string;

    /**
     * Set created at
     *
     * @param string $createdAt
     * @return $this
     */
    public function setCreatedAt(string $createdAt): self;

    /**
     * Get updated at
     *
     * @return string|null
     */
    public function getUpdatedAt(): ?string;

    /**
     * Set updated at
     *
     * @param string $updatedAt
     * @return $this
     */
    public function setUpdatedAt(string $updatedAt): self;
}