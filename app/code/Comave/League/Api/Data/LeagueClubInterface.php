<?php
declare(strict_types=1);

namespace Comave\League\Api\Data;

use Magento\Framework\Api\ExtensibleDataInterface;

/**
 * LeagueClub Interface
 */
interface LeagueClubInterface extends ExtensibleDataInterface
{
    public const string LEAGUE_CLUB_ID = 'league_club_id';
    public const string SEASON_ID = 'season_id';
    public const string CLUB_ID = 'club_id';
    public const string RANK = 'rank';
    public const string POINTS = 'points';
    public const string MATCHES_PLAYED = 'matches_played';
    public const string WINS = 'wins';
    public const string DRAWS = 'draws';
    public const string LOSSES = 'losses';
    public const string GOALS_FOR = 'goals_for';
    public const string GOALS_AGAINST = 'goals_against';
    public const string CREATED_AT = 'created_at';
    public const string UPDATED_AT = 'updated_at';

    /**
     * Get league id
     *
     * @return int|null
     */
    public function getId();

    /**
     * Set league id
     *
     * @param int $id
     * @return $this
     */
    public function setId($id);

    /**
     * Get league club ID
     *
     * @return int|null
     */
    public function getLeagueClubId(): ?int;

    /**
     * Set league club ID
     *
     * @param int $leagueClubId
     * @return $this
     */
    public function setLeagueClubId(int $leagueClubId): self;

    /**
     * Get season ID
     *
     * @return int|null
     */
    public function getSeasonId(): ?int;

    /**
     * Set season ID
     *
     * @param int $seasonId
     * @return $this
     */
    public function setSeasonId(int $seasonId): self;

    /**
     * Get club ID
     *
     * @return int|null
     */
    public function getClubId(): ?int;

    /**
     * Set club ID
     *
     * @param int $clubId
     * @return $this
     */
    public function setClubId(int $clubId): self;

    /**
     * Get rank
     *
     * @return int|null
     */
    public function getRank(): ?int;

    /**
     * Set rank
     *
     * @param int $rank
     * @return $this
     */
    public function setRank(int $rank): self;

    /**
     * Get points
     *
     * @return int|null
     */
    public function getPoints(): ?int;

    /**
     * Set points
     *
     * @param int $points
     * @return $this
     */
    public function setPoints(int $points): self;

    /**
     * Get matches played
     *
     * @return int|null
     */
    public function getMatchesPlayed(): ?int;

    /**
     * Set matches played
     *
     * @param int $matchesPlayed
     * @return $this
     */
    public function setMatchesPlayed(int $matchesPlayed): self;

    /**
     * Get wins
     *
     * @return int|null
     */
    public function getWins(): ?int;

    /**
     * Set wins
     *
     * @param int $wins
     * @return $this
     */
    public function setWins(int $wins): self;

    /**
     * Get draws
     *
     * @return int|null
     */
    public function getDraws(): ?int;

    /**
     * Set draws
     *
     * @param int $draws
     * @return $this
     */
    public function setDraws(int $draws): self;

    /**
     * Get losses
     *
     * @return int|null
     */
    public function getLosses(): ?int;

    /**
     * Set losses
     *
     * @param int $losses
     * @return $this
     */
    public function setLosses(int $losses): self;

    /**
     * Get goals for
     *
     * @return int|null
     */
    public function getGoalsFor(): ?int;

    /**
     * Set goals for
     *
     * @param int $goalsFor
     * @return $this
     */
    public function setGoalsFor(int $goalsFor): self;

    /**
     * Get goals against
     *
     * @return int|null
     */
    public function getGoalsAgainst(): ?int;

    /**
     * Set goals against
     *
     * @param int $goalsAgainst
     * @return $this
     */
    public function setGoalsAgainst(int $goalsAgainst): self;

    /**
     * Get created at
     *
     * @return string|null
     */
    public function getCreatedAt(): ?string;

    /**
     * Set created at
     *
     * @param string $createdAt
     * @return $this
     */
    public function setCreatedAt(string $createdAt): self;

    /**
     * Get updated at
     *
     * @return string|null
     */
    public function getUpdatedAt(): ?string;

    /**
     * Set updated at
     *
     * @param string $updatedAt
     * @return $this
     */
    public function setUpdatedAt(string $updatedAt): self;
}