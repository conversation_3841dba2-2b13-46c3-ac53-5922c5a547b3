<?php
declare(strict_types=1);

namespace Comave\League\Api\Data;

use Magento\Framework\Api\SearchCriteriaInterface;

/**
 * LeagueClub Search Result Interface
 */
interface LeagueClubSearchResultInterface
{
    /**
     * get items
     *
     * @return LeagueClubInterface[]
     */
    public function getItems();

    /**
     * Set items
     *
     * @param LeagueClubInterface[] $items
     * @return $this
     */
    public function setItems(array $items);

    /**
     * @param SearchCriteriaInterface $searchCriteria
     * @return $this
     */
    public function setSearchCriteria(SearchCriteriaInterface $searchCriteria);

    /**
     * @param int $count
     * @return $this
     */
    public function setTotalCount($count);
}
