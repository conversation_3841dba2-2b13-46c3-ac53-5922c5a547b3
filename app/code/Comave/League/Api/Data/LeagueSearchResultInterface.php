<?php

declare(strict_types=1);

namespace Comave\League\Api\Data;

use Magento\Framework\Api\SearchCriteriaInterface;

/**
 * League search result interface for handling league search query results
 *
 * Provides methods to manage collections of league entities returned from search operations,
 * including items manipulation and search criteria handling.
 *
 * @api
 */
interface LeagueSearchResultInterface
{
    /**
     * get items
     *
     * @return LeagueInterface[]
     */
    public function getItems();

    /**
     * Set items
     *
     * @param LeagueInterface[] $items
     * @return $this
     */
    public function setItems(array $items);

    /**
     * @param SearchCriteriaInterface $searchCriteria
     * @return $this
     */
    public function setSearchCriteria(SearchCriteriaInterface $searchCriteria);

    /**
     * @param int $count
     * @return $this
     */
    public function setTotalCount($count);
}
