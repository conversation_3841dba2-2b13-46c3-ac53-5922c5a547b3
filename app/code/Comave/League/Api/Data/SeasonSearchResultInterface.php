<?php
declare(strict_types=1);

namespace Comave\League\Api\Data;

use Magento\Framework\Api\SearchResultsInterface;

/**
 * Interface for season search results.
 * @api
 */
interface SeasonSearchResultInterface extends SearchResultsInterface
{
    /**
     * Get seasons list.
     *
     * @return SeasonInterface[]
     */
    public function getItems();

    /**
     * Set seasons list.
     *
     * @param SeasonInterface[] $items
     * @return $this
     */
    public function setItems(array $items);
}