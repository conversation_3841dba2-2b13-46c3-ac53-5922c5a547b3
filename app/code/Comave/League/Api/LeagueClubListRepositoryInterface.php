<?php
declare(strict_types=1);

namespace Comave\League\Api;

use Comave\League\Api\Data\LeagueClubSearchResultInterface;
use Magento\Framework\Api\SearchCriteriaInterface;
use Magento\Framework\Exception\LocalizedException;

/**
 * LeagueClub List Repository Interface
 */
interface LeagueClubListRepositoryInterface
{
    /**
     * Retrieve seasons matching the specified criteria.
     *
     * @param SearchCriteriaInterface $searchCriteria
     * @return LeagueClubSearchResultInterface
     * @throws LocalizedException
     */
    public function getList(SearchCriteriaInterface $searchCriteria);
}