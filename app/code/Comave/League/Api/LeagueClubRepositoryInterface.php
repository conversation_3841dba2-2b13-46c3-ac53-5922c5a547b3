<?php
declare(strict_types=1);

namespace Comave\League\Api;

use Comave\League\Api\Data\LeagueClubInterface;
use Magento\Framework\Exception\CouldNotDeleteException;
use Magento\Framework\Exception\CouldNotSaveException;
use Magento\Framework\Exception\NoSuchEntityException;

/**
 * LeagueClub Repository Interface
 */
interface LeagueClubRepositoryInterface
{
    /**
     * Save league club
     *
     * @param LeagueClubInterface $leagueClub
     * @return LeagueClubInterface
     * @throws CouldNotSaveException
     */
    public function save(LeagueClubInterface $leagueClub);

    /**
     * @param int $leagueClubId
     * @return mixed
     */
    public function get(int $leagueClubId);

    /**
     * Delete league club
     *
     * @param LeagueClubInterface $leagueClub
     * @return bool
     * @throws CouldNotDeleteException
     */
    public function delete(LeagueClubInterface $leagueClub);

    /**
     * Delete league club by ID
     *
     * @param int $leagueClubId
     * @return bool
     * @throws CouldNotDeleteException
     * @throws NoSuchEntityException
     */
    public function deleteById(int $leagueClubId);

    /**
     * Clear caches instances
     *
     * @return void
     */
    public function clear(): void;
}