<?php
declare(strict_types=1);

namespace Comave\League\Api;

use Comave\League\Api\Data\SeasonInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;

/**
 * Season repository interface for managing season data persistence and retrieval
 *
 * Provides CRUD operations for season entities including save, retrieve, delete operations
 * and cache management functionality.
 *
 * @api
 */
interface SeasonRepositoryInterface
{
    /**
     * Save season.
     *
     * @param SeasonInterface $season
     * @return SeasonInterface
     * @throws LocalizedException
     */
    public function save(SeasonInterface $season);

    /**
     * Retrieve season.
     *
     * @param int $seasonId
     * @return SeasonInterface
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function get(int $seasonId);

    /**
     * Delete season.
     *
     * @param SeasonInterface $season
     * @return bool true on success
     * @throws LocalizedException
     */
    public function delete(SeasonInterface $season);

    /**
     * Delete season by ID.
     *
     * @param int $seasonId
     * @return bool true on success
     * @throws NoSuchEntityException
     * @throws LocalizedException
     */
    public function deleteById(int $seasonId);

    /**
     * Clear caches instances
     *
     * @return void
     */
    public function clear(): void;
}