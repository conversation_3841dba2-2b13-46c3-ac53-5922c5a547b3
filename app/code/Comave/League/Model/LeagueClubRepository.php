<?php
declare(strict_types=1);

namespace Comave\League\Model;

use Comave\League\Api\Data\LeagueClubInterface;
use Comave\League\Api\Data\LeagueClubInterfaceFactory;
use Comave\League\Api\LeagueClubRepositoryInterface;
use Comave\League\Model\ResourceModel\LeagueClub as LeagueClubResourceModel;
use Exception;
use Magento\Framework\Exception\CouldNotDeleteException;
use Magento\Framework\Exception\CouldNotSaveException;
use Magento\Framework\Exception\NoSuchEntityException;

class LeagueClubRepository implements LeagueClubRepositoryInterface
{
    /**
     * @var LeagueClubInterface[]
     */
    private array $cache = [];

    /**
     * @param LeagueClubInterfaceFactory $factory
     * @param LeagueClubResourceModel $resource
     */
    public function __construct(
        private readonly LeagueClubInterfaceFactory $factory,
        private readonly LeagueClubResourceModel $resource
    ) {
    }

    /**
     * @param \Comave\League\Api\Data\LeagueClubInterface $leagueClub
     * @return \Comave\League\Api\Data\LeagueClubInterface
     * @throws \Magento\Framework\Exception\CouldNotSaveException
     */
    public function save(LeagueClubInterface $leagueClub)
    {
        try {
            $this->resource->save($leagueClub);
        } catch (Exception $exception) {
            throw new CouldNotSaveException(
                __($exception->getMessage())
            );
        }

        return $leagueClub;
    }

    /**
     * @param int $leagueClubId
     * @return true
     * @throws \Magento\Framework\Exception\CouldNotDeleteException
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function deleteById(int $leagueClubId)
    {
        return $this->delete($this->get($leagueClubId));
    }

    /**
     * @param \Comave\League\Api\Data\LeagueClubInterface $leagueClub
     * @return true
     * @throws \Magento\Framework\Exception\CouldNotDeleteException
     */
    public function delete(LeagueClubInterface $leagueClub)
    {
        try {
            $id = $leagueClub->getLeagueClubId();
            $this->resource->delete($leagueClub);
            unset($this->cache[$id]);
        } catch (Exception $exception) {
            throw new CouldNotDeleteException(
                __($exception->getMessage())
            );
        }

        return true;
    }

    /**
     * @param int $leagueClubId
     * @return \Comave\League\Api\Data\LeagueClubInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function get(int $leagueClubId)
    {
        if (!isset($this->cache[$leagueClubId])) {
            $leagueClub = $this->factory->create();
            $this->resource->load($leagueClub, $leagueClubId);
            if (!$leagueClub->getId()) {
                throw new NoSuchEntityException(
                    __('The League Club with the ID "%1" does not exist . ', $leagueClubId)
                );
            }
            $this->cache[$leagueClubId] = $leagueClub;
        }

        return $this->cache[$leagueClubId];
    }

    /**
     * @inheritDoc
     */
    public function clear(): void
    {
        $this->cache = [];
    }
}
