<?php
declare(strict_types=1);

namespace Comave\League\Model;

use Comave\League\Api\Data\SeasonInterface;
use Comave\League\Api\SeasonListRepositoryInterface;
use Comave\League\Api\SeasonRepositoryInterface;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\Model\AbstractModel;
use Umc\Crud\Ui\EntityUiManagerInterface;

class SeasonUiManager implements EntityUiManagerInterface
{
    /**
     * @param \Magento\Framework\Api\SearchCriteriaBuilder $searchCriteriaBuilder
     * @param \Comave\League\Api\SeasonListRepositoryInterface $listRepository
     * @param \Comave\League\Api\SeasonRepositoryInterface $repository
     * @param \Comave\League\Model\SeasonFactory $factory
     */
    public function __construct(
        private readonly SearchCriteriaBuilder $searchCriteriaBuilder,
        private readonly SeasonListRepositoryInterface $listRepository,
        private readonly SeasonRepositoryInterface $repository,
        private readonly SeasonFactory $factory
    ) {
    }

    /**
     * @param \Magento\Framework\Model\AbstractModel $season
     * @return void
     */
    public function save(AbstractModel $season)
    {
        $this->repository->save($season);
    }

    /**
     * @param int $id
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     * @throws \Magento\Framework\Exception\CouldNotDeleteException
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function delete(int $id)
    {
        $this->repository->deleteById($id);
    }

    /**
     * @param int|null $id
     * @return \Magento\Framework\Model\AbstractModel | Season | SeasonInterface;
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function get(?int $id = null)
    {
        return ($id)
            ? $this->repository->get($id)
            : $this->factory->create();
    }

    /**
     * @return array
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getList(): array
    {
        return $this->listRepository->getList($this->searchCriteriaBuilder->create())->getItems();
    }

    /**
     * @param int|null $leagueId
     * @return \Comave\League\Api\Data\SeasonInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function getByLeagueId(?int $leagueId): SeasonInterface
    {
        $seasonId = null;
        if (!empty($leagueId)) {
            $seasons = $this->listRepository->getList(
                $this->searchCriteriaBuilder->addFilter(SeasonInterface::LEAGUE_ID, $leagueId)->create()
            )->getItems();
            foreach ($seasons as $season) {
                $seasonId = (int)$season->getLeagueId();
                break;
            }
        }

        return $this->get($seasonId);
    }
}