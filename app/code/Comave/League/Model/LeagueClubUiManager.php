<?php
declare(strict_types=1);

namespace Comave\League\Model;

use Comave\League\Api\Data\LeagueClubInterface;
use Comave\League\Api\LeagueClubListRepositoryInterface;
use Comave\League\Api\LeagueClubRepositoryInterface;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\Model\AbstractModel;
use Umc\Crud\Ui\EntityUiManagerInterface;

class LeagueClubUiManager implements EntityUiManagerInterface
{
    /**
     * @param SearchCriteriaBuilder $searchCriteriaBuilder
     * @param LeagueClubListRepositoryInterface $listRepository
     * @param LeagueClubRepositoryInterface $repository
     * @param LeagueClubFactory $factory
     */
    public function __construct(
        private readonly SearchCriteriaBuilder $searchCriteriaBuilder,
        private readonly LeagueClubListRepositoryInterface $listRepository,
        private readonly LeagueClubRepositoryInterface $repository,
        private readonly LeagueClubFactory $factory
    ) {
    }

    /**
     * @param AbstractModel $leagueClub
     * @return void
     */
    public function save(AbstractModel $leagueClub)
    {
        $this->repository->save($leagueClub);
    }

    /**
     * @param int $id
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     * @throws \Magento\Framework\Exception\CouldNotDeleteException
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function delete(int $id)
    {
        $this->repository->deleteById($id);
    }

    /**
     * @param int|null $id
     * @return AbstractModel|LeagueClub|LeagueClubInterface
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function get(?int $id = null)
    {
        return ($id)
            ? $this->repository->get($id)
            : $this->factory->create();
    }

    /**
     * @return array
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getList(): array
    {
        return $this->listRepository->getList($this->searchCriteriaBuilder->create())->getItems();
    }

    /**
     * @param int|null $leagueId
     * @param int|null $clubId
     * @return \Comave\League\Api\Data\LeagueClubInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function getBySeasonClub(?int $seasonId, ?int $clubId): LeagueClubInterface
    {
        $leagueClubId = null;
        if (!empty($seasonId) && !empty($clubId)) {
            $leagueClubs = $this->listRepository->getList(
                $this->searchCriteriaBuilder
                    ->addFilter(sprintf('main_table.%s', LeagueClubInterface::SEASON_ID), $seasonId)
                    ->addFilter(sprintf('main_table.%s', LeagueClubInterface::CLUB_ID), $clubId)
                    ->create()
            )->getItems();
            foreach ($leagueClubs as $leagueClub) {
                $leagueClubId = (int)$leagueClub->getId();
                break;
            }
        }

        return $this->get($leagueClubId);
    }
}