<?php
declare(strict_types=1);

namespace Comave\League\Model;

use Comave\League\Api\Data\LeagueInterface;
use Magento\Framework\Api\ExtensionAttributesInterface;
use Magento\Framework\Model\AbstractModel;

/**
 * League Model
 */
class League extends AbstractModel implements LeagueInterface
{
    /**
     * Cache tag
     */
    public const string CACHE_TAG = 'comave_league';

    /**
     * Cache tag
     */
    protected $_cacheTag = self::CACHE_TAG;

    /**
     * Event prefix
     */
    protected $_eventPrefix = 'comave_league';

    /**
     * Event object
     */
    protected $_eventObject = 'comave_league';


    /**
     * @inherit
     */
    public function getName(): ?string
    {
        return $this->getData(self::NAME);
    }

    /**
     * @inherit
     */
    public function setName(string $name): self
    {
        return $this->setData(self::NAME, $name);
    }

    /**
     * @inherit
     */
    public function getCode(): ?string
    {
        return $this->getData(self::CODE);
    }

    /**
     * Set league code
     *
     * @param string $code
     * @return $this
     */
    public function setCode(string $code): self
    {
        return $this->setData(self::CODE, $code);
    }

    /**
     * Get description
     *
     * @return string|null
     */
    public function getDescription(): ?string
    {
        return $this->getData(self::DESCRIPTION);
    }

    /**
     * Set description
     *
     * @param string $description
     * @return $this
     */
    public function setDescription(string $description): self
    {
        return $this->setData(self::DESCRIPTION, $description);
    }

    /**
     * Get country code
     *
     * @return string|null
     */
    public function getCountryCode(): ?string
    {
        return $this->getData(self::COUNTRY_CODE);
    }

    /**
     * Set country code
     *
     * @param string $countryCode
     * @return $this
     */
    public function setCountryCode(string $countryCode): self
    {
        return $this->setData(self::COUNTRY_CODE, $countryCode);
    }

    /**
     * Get logo
     *
     * @return string|null
     */
    public function getLogo(): ?string
    {
        return $this->getData(self::LOGO);
    }

    /**
     * Set logo
     *
     * @param string $logo
     * @return $this
     */
    public function setLogo(string $logo): self
    {
        return $this->setData(self::LOGO, $logo);
    }

    /**
     * Get status
     *
     * @return int|null
     */
    public function getStatus(): ?int
    {
        return (int)$this->getData(self::STATUS);
    }

    /**
     * Set status
     *
     * @param int $status
     * @return $this
     */
    public function setStatus(int $status): self
    {
        return $this->setData(self::STATUS, $status);
    }

    /**
     * Get created at
     *
     * @return string|null
     */
    public function getCreatedAt(): ?string
    {
        return $this->getData(self::CREATED_AT);
    }

    /**
     * Set created at
     *
     * @param string $createdAt
     * @return $this
     */
    public function setCreatedAt(string $createdAt): self
    {
        return $this->setData(self::CREATED_AT, $createdAt);
    }

    /**
     * Get updated at
     *
     * @return string|null
     */
    public function getUpdatedAt(): ?string
    {
        return $this->getData(self::UPDATED_AT);
    }

    /**
     * Set updated at
     *
     * @param string $updatedAt
     * @return $this
     */
    public function setUpdatedAt(string $updatedAt): self
    {
        return $this->setData(self::UPDATED_AT, $updatedAt);
    }

    /**
     * @param int $id
     * @return self
     */
    public function setLeagueId(int $id): LeagueInterface
    {
        return $this->setData(self::LEAGUE_ID, $id);
    }

    /**
     * @return int|null
     */
    public function getLeagueId(): ?int
    {
        return (int)$this->getData(self::LEAGUE_ID);
    }

    /**
     * @return \Magento\Framework\Api\ExtensionAttributesInterface|null
     */
    public function getExtensionAttributes(): ?ExtensionAttributesInterface
    {
        return $this->_getExtensionAttributes();
    }

    /**
     * @param \Magento\Framework\Api\ExtensionAttributesInterface $extensionAttributes
     * @return \Comave\League\Model\League
     */
    public function setExtensionAttributes(ExtensionAttributesInterface $extensionAttributes): League
    {
        return $this->_setExtensionAttributes($extensionAttributes);
    }

    /**
     * @return array
     */
    public function getIdentities(): array
    {
        return [sprintf("%s_%s", self::CACHE_TAG, $this->getId())];
    }

    /**
     * @inherit
     */
    protected function _construct(): void
    {
        $this->_init(ResourceModel\League::class);
    }
}
