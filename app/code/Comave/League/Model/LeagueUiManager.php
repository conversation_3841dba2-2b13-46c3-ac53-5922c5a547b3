<?php
declare(strict_types=1);

namespace Comave\League\Model;

use Comave\League\Api\Data\LeagueInterface;
use Comave\League\Api\LeagueListRepositoryInterface;
use Comave\League\Api\LeagueRepositoryInterface;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Model\AbstractModel;
use Umc\Crud\Ui\EntityUiManagerInterface;


/**
 * League UI manager for handling UI operations and interactions
 *
 * Provides UI-specific operations for league entities including save, delete, retrieve,
 * and list operations with additional business logic for UI layer interactions.
 */
class LeagueUiManager implements EntityUiManagerInterface
{
    /**
     * @param SearchCriteriaBuilder $searchCriteriaBuilder
     * @param LeagueListRepositoryInterface $listRepository
     * @param LeagueRepositoryInterface $repository
     * @param LeagueFactory $factory
     */
    public function __construct(
        private readonly SearchCriteriaBuilder $searchCriteriaBuilder,
        private readonly LeagueListRepositoryInterface $listRepository,
        private readonly LeagueRepositoryInterface $repository,
        private readonly LeagueFactory $factory
    ) {
    }

    /**
     * @param AbstractModel $league
     * @return void
     * @throws LocalizedException
     */
    public function save(AbstractModel $league)
    {
        $this->repository->save($league);
    }

    /**
     * @param int $id
     * @throws NoSuchEntityException
     * @throws CouldNotDeleteException
     * @throws LocalizedException
     */
    public function delete(int $id)
    {
        $this->repository->deleteById($id);
    }

    /**
     * @param int|null $id
     * @return AbstractModel | League | LeagueInterface;
     * @throws NoSuchEntityException
     */
    public function get(?int $id = null)
    {
        return ($id)
            ? $this->repository->get($id)
            : $this->factory->create();
    }

    /**
     * @return array
     * @throws LocalizedException
     */
    public function getList(): array
    {
        return $this->listRepository->getList($this->searchCriteriaBuilder->create())->getItems();
    }

    /**
     * @param string|null $code
     * @return LeagueInterface
     * @throws LocalizedException
     * @throws NoSuchEntityException
     */
    public function getByCode(?string $code): LeagueInterface
    {
        $leagueId = null;
        if (!empty($code)) {
            $leagues = $this->listRepository->getList(
                $this->searchCriteriaBuilder->addFilter(LeagueInterface::CODE, $code)->create()
            )->getItems();
            foreach ($leagues as $league) {
                $leagueId = (int)$league->getLeagueId();
                break;
            }
        }

        return $this->get($leagueId);
    }
}
