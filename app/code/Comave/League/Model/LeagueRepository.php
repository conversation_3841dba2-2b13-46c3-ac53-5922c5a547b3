<?php
declare(strict_types=1);

namespace Comave\League\Model;

use Comave\League\Api\Data\LeagueInterface;
use Comave\League\Api\Data\LeagueInterfaceFactory;
use Comave\League\Api\LeagueRepositoryInterface;
use Comave\League\Model\ResourceModel\League as LeagueResourceModel;
use Exception;
use Magento\Framework\Exception\CouldNotDeleteException;
use Magento\Framework\Exception\CouldNotSaveException;
use Magento\Framework\Exception\NoSuchEntityException;

/**
 * League repository implementation for data persistence and retrieval
 *
 * Handles CRUD operations for league entities with caching support,
 * providing save, retrieve, delete functionality and cache management.
 */
class LeagueRepository implements LeagueRepositoryInterface
{
    /**
     * @var LeagueInterface[]
     */
    private array $cache = [];

    /**
     * @param LeagueInterfaceFactory $factory
     * @param LeagueResourceModel $resource
     */
    public function __construct(
        private readonly LeagueInterfaceFactory $factory,
        private readonly LeagueResourceModel $resource
    ) {
    }

    /**
     * @param \Comave\League\Api\Data\LeagueInterface $league
     * @return \Comave\League\Api\Data\LeagueInterface
     * @throws \Magento\Framework\Exception\CouldNotSaveException
     */
    public function save(LeagueInterface $league)
    {
        try {
            $this->resource->save($league);
        } catch (Exception $exception) {
            throw new CouldNotSaveException(
                __($exception->getMessage())
            );
        }

        return $league;
    }

    /**
     * @param int $leagueId
     * @return true
     * @throws \Magento\Framework\Exception\CouldNotDeleteException
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function deleteById(int $leagueId)
    {
        return $this->delete($this->get($leagueId));
    }

    /**
     * @param \Comave\League\Api\Data\LeagueInterface $league
     * @return true
     * @throws \Magento\Framework\Exception\CouldNotDeleteException
     */
    public function delete(LeagueInterface $league)
    {
        try {
            $id = $league->getLeagueId();
            $this->resource->delete($league);
            unset($this->cache[$id]);
        } catch (Exception $exception) {
            throw new CouldNotDeleteException(
                __($exception->getMessage())
            );
        }

        return true;
    }

    /**
     * @param int $leagueId
     * @return \Comave\League\Api\Data\LeagueInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function get(int $leagueId)
    {
        if (!isset($this->cache[$leagueId])) {
            $league = $this->factory->create();
            $this->resource->load($league, $leagueId);
            if (!$league->getId()) {
                throw new NoSuchEntityException(
                    __('The League with the ID "%1" does not exist . ', $leagueId)
                );
            }
            $this->cache[$leagueId] = $league;
        }

        return $this->cache[$leagueId];
    }

    /**
     * @inheritDoc
     */
    public function clear(): void
    {
        $this->cache = [];
    }
}
