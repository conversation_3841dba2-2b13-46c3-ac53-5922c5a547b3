<?php
declare(strict_types=1);

namespace Comave\League\Model\ResourceModel\Season;

use Comave\League\Model\Season;
use Comave\League\Model\ResourceModel\Season as SeasonResource;
use Umc\Crud\Model\ResourceModel\Collection\AbstractCollection;

/**
 * Season Collection
 */
class Collection extends AbstractCollection
{
    /**
     * @var string
     */
    protected $_idFieldName = 'season_id';

    /**
     * Define resource model
     *
     * @return void
     */
    protected function _construct(): void
    {
        $this->_init(Season::class, SeasonResource::class);
    }
}