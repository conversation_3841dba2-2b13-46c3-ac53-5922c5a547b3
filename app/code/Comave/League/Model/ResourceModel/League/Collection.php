<?php
declare(strict_types=1);

namespace Comave\League\Model\ResourceModel\League;

use Comave\League\Model\League;
use Comave\League\Model\ResourceModel\League as LeagueResource;
use Umc\Crud\Model\ResourceModel\Collection\AbstractCollection;

/**
 * @api
 */
class Collection extends AbstractCollection
{
    /**
     * @var string
     * phpcs:disable PSR2.Classes.PropertyDeclaration.Underscore,PSR12.Classes.PropertyDeclaration.Underscore
     */
    protected $_idFieldName = 'league_id';
    //phpcs: enable

    /**
     * Define resource model
     *
     * @return void
     * @codeCoverageIgnore
     * //phpcs:disable PSR2.Methods.MethodDeclaration.Underscore, PSR12.Methods.MethodDeclaration.Underscore
     */
    protected function _construct()
    {
        $this->_init(League::class, LeagueResource::class);
    }
}
