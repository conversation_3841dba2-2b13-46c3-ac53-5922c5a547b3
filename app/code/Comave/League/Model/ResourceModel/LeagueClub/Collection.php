<?php
declare(strict_types=1);

namespace Comave\League\Model\ResourceModel\LeagueClub;

use Comave\League\Model\LeagueClub;
use Comave\League\Model\ResourceModel\LeagueClub as LeagueClubResourceModel;
use Umc\Crud\Model\ResourceModel\Collection\AbstractCollection;

/**
 * LeagueClub Collection
 */
class Collection extends AbstractCollection
{
    /**
     * @var string
     */
    protected $_idFieldName = 'league_club_id';

    /**
     * Resource initialization
     *
     * @return void
     */
    protected function _construct(): void
    {
        $this->_init(LeagueClub::class, LeagueClubResourceModel::class);
    }

    /**
     * Initialize select with JOIN to club table
     *
     * @return void
     */
    protected function _initSelect(): void
    {
        parent::_initSelect();

        $this->getSelect()->joinLeft(
            ['club' => $this->getTable('comave_club')],
            'main_table.club_id = club.club_id',
            [
                'club_id' => 'club.club_id',
                'club_name' => 'club.name',
                'club_url_key' => 'club.url_key',
                'club_status' => 'club.status',
            ]
        );
    }
}