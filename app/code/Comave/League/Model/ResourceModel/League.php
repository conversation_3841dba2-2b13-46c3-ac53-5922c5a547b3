<?php
declare(strict_types=1);

namespace Comave\League\Model\ResourceModel;

use Umc\Crud\Model\ResourceModel\AbstractModel;

class League extends AbstractModel
{
    /**
     * Initialize resource model
     *
     * @return void
     * @codeCoverageIgnore
     * //phpcs:disable PSR2.Methods.MethodDeclaration.Underscore, PSR12.Methods.MethodDeclaration.Underscore
     */
    protected function _construct()
    {
        $this->_init('comave_league', 'league_id');
    }
    //phpcs: enable
}
