<?php
declare(strict_types=1);

namespace Comave\League\Model;

use Comave\League\Api\Data\LeagueClubSearchResultInterface;
use Comave\League\Api\Data\LeagueClubSearchResultInterfaceFactory;
use Comave\League\Api\LeagueClubListRepositoryInterface;
use Comave\League\Model\ResourceModel\LeagueClub\Collection;
use Comave\League\Model\ResourceModel\LeagueClub\CollectionFactory;
use Magento\Framework\Api\SearchCriteria\CollectionProcessorInterface;
use Magento\Framework\Api\SearchCriteriaInterface;

/**
 * LeagueClub List Repository
 */
class LeagueClubListRepository implements LeagueClubListRepositoryInterface
{
    /**
     * @param CollectionFactory $collectionFactory
     * @param LeagueClubSearchResultInterfaceFactory $searchResultsFactory
     * @param CollectionProcessorInterface $collectionProcessor
     */
    public function __construct(
        private readonly CollectionFactory $collectionFactory,
        private readonly LeagueClubSearchResultInterfaceFactory $searchResultsFactory,
        private readonly CollectionProcessorInterface $collectionProcessor
    ) {
    }

    /**
     * Get list of league clubs
     *
     * @param SearchCriteriaInterface $searchCriteria
     * @return LeagueClubSearchResultInterface
     */
    public function getList(SearchCriteriaInterface $searchCriteria): LeagueClubSearchResultInterface
    {
        /** @var Collection $collection */
        $collection = $this->collectionFactory->create();
        $this->collectionProcessor->process($searchCriteria, $collection);

        /** @var LeagueClubSearchResultInterface $searchResults */
        $searchResults = $this->searchResultsFactory->create();
        $searchResults->setSearchCriteria($searchCriteria);
        $searchResults->setItems($collection->getItems());
        $searchResults->setTotalCount($collection->getSize());

        return $searchResults;
    }
}
