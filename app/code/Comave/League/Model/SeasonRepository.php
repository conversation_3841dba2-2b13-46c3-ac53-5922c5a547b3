<?php
declare(strict_types=1);

namespace Comave\League\Model;

use Comave\League\Api\Data\SeasonInterface;
use Comave\League\Api\Data\SeasonInterfaceFactory;
use Comave\League\Api\SeasonRepositoryInterface;
use Comave\League\Model\ResourceModel\Season as SeasonResourceModel;
use Exception;
use Magento\Framework\Exception\CouldNotDeleteException;
use Magento\Framework\Exception\CouldNotSaveException;
use Magento\Framework\Exception\NoSuchEntityException;

class SeasonRepository implements SeasonRepositoryInterface
{
    /**
     * @var SeasonInterface[]
     */
    private array $cache = [];

    /**
     * @param SeasonInterfaceFactory $factory
     * @param SeasonResourceModel $resource
     */
    public function __construct(
        private readonly SeasonInterfaceFactory $factory,
        private readonly SeasonResourceModel $resource
    ) {
    }

    /**
     * @param \Comave\League\Api\Data\SeasonInterface $season
     * @return \Comave\League\Api\Data\SeasonInterface
     * @throws \Magento\Framework\Exception\CouldNotSaveException
     */
    public function save(SeasonInterface $season)
    {
        try {
            $this->resource->save($season);
        } catch (Exception $exception) {
            throw new CouldNotSaveException(
                __($exception->getMessage())
            );
        }

        return $season;
    }

    /**
     * @param int $seasonId
     * @return true
     * @throws \Magento\Framework\Exception\CouldNotDeleteException
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function deleteById(int $seasonId)
    {
        return $this->delete($this->get($seasonId));
    }

    /**
     * @param \Comave\League\Api\Data\SeasonInterface $season
     * @return true
     * @throws \Magento\Framework\Exception\CouldNotDeleteException
     */
    public function delete(SeasonInterface $season)
    {
        try {
            $id = $season->getSeasonId();
            $this->resource->delete($season);
            unset($this->cache[$id]);
        } catch (Exception $exception) {
            throw new CouldNotDeleteException(
                __($exception->getMessage())
            );
        }

        return true;
    }

    /**
     * @param int $seasonId
     * @return \Comave\League\Api\Data\SeasonInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function get(int $seasonId)
    {
        if (!isset($this->cache[$seasonId])) {
            $season = $this->factory->create();
            $this->resource->load($season, $seasonId);
            if (!$season->getId()) {
                throw new NoSuchEntityException(
                    __('The Season with the ID "%1" does not exist . ', $seasonId)
                );
            }
            $this->cache[$seasonId] = $season;
        }

        return $this->cache[$seasonId];
    }

    /**
     * @inheritDoc
     */
    public function clear(): void
    {
        $this->cache = [];
    }
}