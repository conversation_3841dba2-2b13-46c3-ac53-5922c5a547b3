<?php
declare(strict_types=1);

namespace Comave\League\Model;

use Comave\League\Api\Data\LeagueClubInterface;
use Magento\Framework\Api\ExtensionAttributesInterface;
use Magento\Framework\Model\AbstractModel;

/**
 * LeagueClub Model
 */
class LeagueClub extends AbstractModel implements LeagueClubInterface
{
    /**
     * Cache tag
     */
    public const string CACHE_TAG = 'comave_league_club';

    /**
     * Cache tag
     */
    protected $_cacheTag = self::CACHE_TAG;

    /**
     * Event prefix
     */
    protected $_eventPrefix = 'comave_league_club';

    /**
     * Event object
     */
    protected $_eventObject = 'comave_league_club';

    /**
     * Get league club ID
     *
     * @return int|null
     */
    public function getLeagueClubId(): ?int
    {
        return (int)$this->getData(self::LEAGUE_CLUB_ID);
    }

    /**
     * Set league club ID
     *
     * @param int $leagueClubId
     * @return $this
     */
    public function setLeagueClubId(int $leagueClubId): self
    {
        return $this->setData(self::LEAGUE_CLUB_ID, $leagueClubId);
    }

    /**
     * Get season ID
     *
     * @return int|null
     */
    public function getSeasonId(): ?int
    {
        return (int)$this->getData(self::SEASON_ID);
    }

    /**
     * Set season ID
     *
     * @param int $seasonId
     * @return $this
     */
    public function setSeasonId(int $seasonId): self
    {
        return $this->setData(self::SEASON_ID, $seasonId);
    }

    /**
     * Get club ID
     *
     * @return int|null
     */
    public function getClubId(): ?int
    {
        return (int)$this->getData(self::CLUB_ID);
    }

    /**
     * Set club ID
     *
     * @param int $clubId
     * @return $this
     */
    public function setClubId(int $clubId): self
    {
        return $this->setData(self::CLUB_ID, $clubId);
    }

    /**
     * Get rank
     *
     * @return int|null
     */
    public function getRank(): ?int
    {
        return (int)$this->getData(self::RANK);
    }

    /**
     * Set rank
     *
     * @param int $rank
     * @return $this
     */
    public function setRank(int $rank): self
    {
        return $this->setData(self::RANK, $rank);
    }

    /**
     * Get points
     *
     * @return int|null
     */
    public function getPoints(): ?int
    {
        return (int)$this->getData(self::POINTS);
    }

    /**
     * Set points
     *
     * @param int $points
     * @return $this
     */
    public function setPoints(int $points): self
    {
        return $this->setData(self::POINTS, $points);
    }

    /**
     * Get matches played
     *
     * @return int|null
     */
    public function getMatchesPlayed(): ?int
    {
        return (int)$this->getData(self::MATCHES_PLAYED);
    }

    /**
     * Set matches played
     *
     * @param int $matchesPlayed
     * @return $this
     */
    public function setMatchesPlayed(int $matchesPlayed): self
    {
        return $this->setData(self::MATCHES_PLAYED, $matchesPlayed);
    }

    /**
     * Get wins
     *
     * @return int|null
     */
    public function getWins(): ?int
    {
        return (int)$this->getData(self::WINS);
    }

    /**
     * Set wins
     *
     * @param int $wins
     * @return $this
     */
    public function setWins(int $wins): self
    {
        return $this->setData(self::WINS, $wins);
    }

    /**
     * Get draws
     *
     * @return int|null
     */
    public function getDraws(): ?int
    {
        return (int)$this->getData(self::DRAWS);
    }

    /**
     * Set draws
     *
     * @param int $draws
     * @return $this
     */
    public function setDraws(int $draws): self
    {
        return $this->setData(self::DRAWS, $draws);
    }

    /**
     * Get losses
     *
     * @return int|null
     */
    public function getLosses(): ?int
    {
        return (int)$this->getData(self::LOSSES);
    }

    /**
     * Set losses
     *
     * @param int $losses
     * @return $this
     */
    public function setLosses(int $losses): self
    {
        return $this->setData(self::LOSSES, $losses);
    }

    /**
     * Get goals for
     *
     * @return int|null
     */
    public function getGoalsFor(): ?int
    {
        return (int)$this->getData(self::GOALS_FOR);
    }

    /**
     * Set goals for
     *
     * @param int $goalsFor
     * @return $this
     */
    public function setGoalsFor(int $goalsFor): self
    {
        return $this->setData(self::GOALS_FOR, $goalsFor);
    }

    /**
     * Get goals against
     *
     * @return int|null
     */
    public function getGoalsAgainst(): ?int
    {
        return (int)$this->getData(self::GOALS_AGAINST);
    }

    /**
     * Set goals against
     *
     * @param int $goalsAgainst
     * @return $this
     */
    public function setGoalsAgainst(int $goalsAgainst): self
    {
        return $this->setData(self::GOALS_AGAINST, $goalsAgainst);
    }

    /**
     * Get created at
     *
     * @return string|null
     */
    public function getCreatedAt(): ?string
    {
        return $this->getData(self::CREATED_AT);
    }

    /**
     * Set created at
     *
     * @param string $createdAt
     * @return $this
     */
    public function setCreatedAt(string $createdAt): self
    {
        return $this->setData(self::CREATED_AT, $createdAt);
    }

    /**
     * Get updated at
     *
     * @return string|null
     */
    public function getUpdatedAt(): ?string
    {
        return $this->getData(self::UPDATED_AT);
    }

    /**
     * Set updated at
     *
     * @param string $updatedAt
     * @return $this
     */
    public function setUpdatedAt(string $updatedAt): self
    {
        return $this->setData(self::UPDATED_AT, $updatedAt);
    }

    /**
     * Get extension attributes
     *
     * @return ExtensionAttributesInterface|null
     */
    public function getExtensionAttributes(): ?ExtensionAttributesInterface
    {
        return $this->_getExtensionAttributes();
    }

    /**
     * Set extension attributes
     *
     * @param ExtensionAttributesInterface $extensionAttributes
     * @return $this
     */
    public function setExtensionAttributes(ExtensionAttributesInterface $extensionAttributes): self
    {
        return $this->_setExtensionAttributes($extensionAttributes);
    }

    /**
     * Get identities
     *
     * @return array
     */
    public function getIdentities(): array
    {
        return [sprintf("%s_%s", self::CACHE_TAG, $this->getId())];
    }

    /**
     * @inherit
     */
    protected function _construct(): void
    {
        $this->_init(ResourceModel\LeagueClub::class);
    }
}