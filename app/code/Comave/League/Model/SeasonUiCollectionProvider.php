<?php
declare(strict_types=1);

namespace Comave\League\Model;

use Comave\League\Model\ResourceModel\Season\CollectionFactory;
use Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection;
use Umc\Crud\Ui\CollectionProviderInterface;

class SeasonUiCollectionProvider implements CollectionProviderInterface
{
    /**
     * @param \Comave\League\Model\ResourceModel\Season\CollectionFactory $factory
     */
    public function __construct(private readonly CollectionFactory $factory)
    {
    }

    /**
     * @return \Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection
     */
    public function getCollection(): AbstractCollection
    {
        return $this->factory->create();
    }
}