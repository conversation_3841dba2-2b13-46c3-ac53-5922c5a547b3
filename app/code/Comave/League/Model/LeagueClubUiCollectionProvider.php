<?php
declare(strict_types=1);

namespace Comave\League\Model;

use Comave\League\Model\ResourceModel\LeagueClub\CollectionFactory;
use Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection;
use Umc\Crud\Ui\CollectionProviderInterface;

class LeagueClubUiCollectionProvider implements CollectionProviderInterface
{
    /**
     * @param CollectionFactory $factory
     */
    public function __construct(private readonly CollectionFactory $factory)
    {
    }

    /**
     * @return AbstractCollection
     */
    public function getCollection(): AbstractCollection
    {
        return $this->factory->create();
    }
}