<?php
declare(strict_types=1);

namespace Comave\League\Model;

use Comave\League\Api\Data\LeagueSearchResultInterfaceFactory;
use Comave\League\Api\LeagueListRepositoryInterface;
use Comave\League\Model\ResourceModel\League\Collection;
use Comave\League\Model\ResourceModel\League\CollectionFactory;
use Magento\Framework\Api\SearchCriteria\CollectionProcessorInterface;
use Magento\Framework\Api\SearchCriteriaInterface;

/**
 * League list repository implementation for filtered data retrieval
 *
 * Handles retrieval of league collections based on search criteria,
 * providing filtered and paginated league data with search result management.
 */
class LeagueListRepository implements LeagueListRepositoryInterface
{
    /**
     * @param \Magento\Framework\Api\SearchCriteria\CollectionProcessorInterface $collectionProcessor
     * @param \Comave\League\Api\Data\LeagueSearchResultInterfaceFactory $searchResultFactory
     * @param \Comave\League\Model\ResourceModel\League\CollectionFactory $collectionFactory
     */
    public function __construct(
        private readonly CollectionProcessorInterface $collectionProcessor,
        private readonly LeagueSearchResultInterfaceFactory $searchResultFactory,
        private readonly CollectionFactory $collectionFactory
    ) {
    }

    /**
     * @param \Magento\Framework\Api\SearchCriteriaInterface $searchCriteria
     * @return LeagueSearchResultInterfaceFactory
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getList(SearchCriteriaInterface $searchCriteria)
    {
        /** @var Collection $collection */
        $collection = $this->collectionFactory->create();
        $this->collectionProcessor->process($searchCriteria, $collection);

        /** @var LeagueSearchResultInterfaceFactory $searchResult */
        $searchResult = $this->searchResultFactory->create();
        $searchResult->setSearchCriteria($searchCriteria);
        $collection->setCurPage($searchCriteria->getCurrentPage());
        $collection->setPageSize($searchCriteria->getPageSize());
        $searchResult->setTotalCount($collection->getSize());
        $searchResult->setItems($collection->getItems());

        return $searchResult;
    }
}
