<?php
declare(strict_types=1);

namespace Comave\League\Model;

use Comave\League\Api\Data\SeasonSearchResultInterfaceFactory;
use Comave\League\Api\SeasonListRepositoryInterface;
use Comave\League\Model\ResourceModel\Season\Collection;
use Comave\League\Model\ResourceModel\Season\CollectionFactory;
use Magento\Framework\Api\SearchCriteria\CollectionProcessorInterface;
use Magento\Framework\Api\SearchCriteriaInterface;

class SeasonListRepository implements SeasonListRepositoryInterface
{
    /**
     * @param \Magento\Framework\Api\SearchCriteria\CollectionProcessorInterface $collectionProcessor
     * @param \Comave\League\Api\Data\SeasonSearchResultInterfaceFactory $searchResultFactory
     * @param \Comave\League\Model\ResourceModel\Season\CollectionFactory $collectionFactory
     */
    public function __construct(
        private readonly CollectionProcessorInterface $collectionProcessor,
        private readonly SeasonSearchResultInterfaceFactory $searchResultFactory,
        private readonly CollectionFactory $collectionFactory
    ) {
    }

    /**
     * @param \Magento\Framework\Api\SearchCriteriaInterface $searchCriteria
     * @return SeasonSearchResultInterfaceFactory
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getList(SearchCriteriaInterface $searchCriteria)
    {
        /** @var Collection $collection */
        $collection = $this->collectionFactory->create();
        $this->collectionProcessor->process($searchCriteria, $collection);

        /** @var SeasonSearchResultInterfaceFactory $searchResult */
        $searchResult = $this->searchResultFactory->create();
        $searchResult->setSearchCriteria($searchCriteria);
        $collection->setCurPage($searchCriteria->getCurrentPage());
        $collection->setPageSize($searchCriteria->getPageSize());
        $searchResult->setTotalCount($collection->getSize());
        $searchResult->setItems($collection->getItems());

        return $searchResult;
    }
}