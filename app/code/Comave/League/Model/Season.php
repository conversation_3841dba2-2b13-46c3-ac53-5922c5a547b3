<?php
declare(strict_types=1);

namespace Comave\League\Model;

use Comave\League\Api\Data\SeasonInterface;
use Magento\Framework\Api\ExtensionAttributesInterface;
use Magento\Framework\Model\AbstractModel;

/**
 * Season Model
 */
class Season extends AbstractModel implements SeasonInterface
{
    /**
     * Cache tag
     */
    public const string CACHE_TAG = 'comave_season';

    /**
     * Cache tag
     */
    protected $_cacheTag = self::CACHE_TAG;

    /**
     * Event prefix
     */
    protected $_eventPrefix = 'comave_season';

    /**
     * Event object
     */
    protected $_eventObject = 'comave_season';

    /**
     * Get season name
     *
     * @return string|null
     */
    public function getName(): ?string
    {
        return $this->getData(self::NAME);
    }

    /**
     * Set season name
     *
     * @param string $name
     * @return $this
     */
    public function setName(string $name): self
    {
        return $this->setData(self::NAME, $name);
    }

    /**
     * @inherit
     */
    public function getLeagueId(): ?int
    {
        return (int)$this->getData(self::LEAGUE_ID);
    }

    /**
     * @inherit
     */
    public function setLeagueId(int $leagueId): self
    {
        return $this->setData(self::LEAGUE_ID, $leagueId);
    }

    /**
     * Get start date
     *
     * @return string|null
     */
    public function getStartDate(): ?string
    {
        return $this->getData(self::START_DATE);
    }

    /**
     * Set start date
     *
     * @param string $startDate
     * @return $this
     */
    public function setStartDate(string $startDate): self
    {
        return $this->setData(self::START_DATE, $startDate);
    }

    /**
     * Get end date
     *
     * @return string|null
     */
    public function getEndDate(): ?string
    {
        return $this->getData(self::END_DATE);
    }

    /**
     * Set end date
     *
     * @param string $endDate
     * @return $this
     */
    public function setEndDate(string $endDate): self
    {
        return $this->setData(self::END_DATE, $endDate);
    }

    /**
     * Get is current
     *
     * @return int|null
     */
    public function getIsCurrent(): ?int
    {
        return (int)$this->getData(self::IS_CURRENT);
    }

    /**
     * Set is current
     *
     * @param int $isCurrent
     * @return $this
     */
    public function setIsCurrent(int $isCurrent): self
    {
        return $this->setData(self::IS_CURRENT, $isCurrent);
    }

    /**
     * @inherit
     */
    public function getMaxClubs(): ?int
    {
        return (int)$this->getData(self::MAX_CLUBS);
    }

    /**
     * @inherit
     */
    public function setMaxClubs(int $maxClubs): self
    {
        return $this->setData(self::MAX_CLUBS, $maxClubs);
    }

    /**
     * Get created at
     *
     * @return string|null
     */
    public function getCreatedAt(): ?string
    {
        return $this->getData(self::CREATED_AT);
    }

    /**
     * Set created at
     *
     * @param string $createdAt
     * @return $this
     */
    public function setCreatedAt(string $createdAt): self
    {
        return $this->setData(self::CREATED_AT, $createdAt);
    }

    /**
     * Get updated at
     *
     * @return string|null
     */
    public function getUpdatedAt(): ?string
    {
        return $this->getData(self::UPDATED_AT);
    }

    /**
     * Set updated at
     *
     * @param string $updatedAt
     * @return $this
     */
    public function setUpdatedAt(string $updatedAt): self
    {
        return $this->setData(self::UPDATED_AT, $updatedAt);
    }

    /**
     * @param int $id
     * @return self
     */
    public function setSeasonId(int $id): SeasonInterface
    {
        return $this->setData(self::SEASON_ID, $id);
    }

    /**
     * @return int|null
     */
    public function getSeasonId(): ?int
    {
        return (int)$this->getData(self::SEASON_ID);
    }

    /**
     * @return \Magento\Framework\Api\ExtensionAttributesInterface|null
     */
    public function getExtensionAttributes(): ?ExtensionAttributesInterface
    {
        return $this->_getExtensionAttributes();
    }

    /**
     * @param \Magento\Framework\Api\ExtensionAttributesInterface $extensionAttributes
     * @return \Comave\League\Model\Season
     */
    public function setExtensionAttributes(ExtensionAttributesInterface $extensionAttributes): Season
    {
        return $this->_setExtensionAttributes($extensionAttributes);
    }

    /**
     * @return array
     */
    public function getIdentities(): array
    {
        return [sprintf("%s_%s", self::CACHE_TAG, $this->getId())];
    }

    /**
     * @inherit
     */
    protected function _construct(): void
    {
        $this->_init(ResourceModel\Season::class);
    }
}