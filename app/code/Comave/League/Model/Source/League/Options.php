<?php
declare(strict_types=1);

namespace Comave\League\Model\Source\League;

use Comave\League\Model\LeagueUiManager;
use Magento\Framework\Data\OptionSourceInterface;

class Options implements OptionSourceInterface
{
    /**
     * @param \Comave\League\Model\LeagueUiManager $leagueUiManager
     */
    public function __construct(
        private readonly LeagueUiManager $leagueUiManager
    ) {
    }

    /**
     * Return array of options as value-label pairs
     *
     * @return array Format: array(array('value' => '<value>', 'label' => '<label>'), ...)
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function toOptionArray(): array
    {
        $options = [];
        foreach ($this->leagueUiManager->getList() as $source) {
            $options[] = ['value' => $source->getId(), 'label' => $source->getName()];
        }

        return $options;
    }
}