<?php
declare(strict_types=1);

namespace Comave\League\Model\Source\Season;

use Comave\League\Model\LeagueUiManager;
use Comave\League\Model\ResourceModel\Season\CollectionFactory;
use Comave\League\Model\SeasonUiManager;
use Magento\Framework\Data\OptionSourceInterface;
use Magento\Framework\Exception\NoSuchEntityException;
use Psr\Log\LoggerInterface;

/**
 * Season Source Model
 */
class Options implements OptionSourceInterface
{
    /**
     * @var array
     */
    private array $leagueCache = [];

    /**
     * @param \Comave\League\Model\SeasonUiManager $seasonUiManager
     * @param \Comave\League\Model\LeagueUiManager $leagueUiManager
     * @param \Psr\Log\LoggerInterface $logger
     */
    public function __construct(
        private readonly SeasonUiManager $seasonUiManager,
        private readonly LeagueUiManager $leagueUiManager,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * Return array of options as value-label pairs
     *
     * @return array Format: array(array('value' => '<value>', 'label' => '<label>'), ...)
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function toOptionArray(): array
    {
        $options = [];
        foreach ($this->seasonUiManager->getList() as $season) {
            $leagueName = $this->getLeagueName($season->getLeagueId());
            $startYear = $this->extractYear($season->getStartDate());
            $endYear = $this->extractYear($season->getEndDate());
            
            $label = sprintf(
                '%s (%s) - %s/%s',
                $leagueName,
                $season->getName(),
                $startYear,
                $endYear
            );
            
            $options[] = [
                'value' => $season->getId(),
                'label' => $label
            ];
        }

        return $options;
    }

    /**
     * Get league name by ID with caching
     *
     * @param int|null $leagueId
     * @return string
     */
    private function getLeagueName(?int $leagueId): string
    {
        if ($leagueId === null) {
            return 'Unknown League';
        }

        if (!isset($this->leagueCache[$leagueId])) {
            try {
                $league = $this->leagueUiManager->get($leagueId);
                $this->leagueCache[$leagueId] = $league->getName() ?? 'Unknown League';
            } catch (NoSuchEntityException $e) {
                $this->logger->warning(
                    sprintf('League with ID %d not found: %s', $leagueId, $e->getMessage())
                );
                $this->leagueCache[$leagueId] = 'Unknown League';
            } catch (\Exception $e) {
                $this->logger->error(
                    sprintf('Error loading league with ID %d: %s', $leagueId, $e->getMessage())
                );
                $this->leagueCache[$leagueId] = 'Unknown League';
            }
        }

        return $this->leagueCache[$leagueId];
    }

    /**
     * Extract year from date string
     *
     * @param string|null $date
     * @return string
     */
    private function extractYear(?string $date): string
    {
        if ($date === null) {
            return '----';
        }

        try {
            $timestamp = strtotime($date);
            if ($timestamp === false) {
                return '----';
            }
            return date('Y', $timestamp);
        } catch (\Exception $e) {
            $this->logger->warning(
                sprintf('Error parsing date "%s": %s', $date, $e->getMessage())
            );
            return '----';
        }
    }
}
