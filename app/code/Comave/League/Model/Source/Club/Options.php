<?php
declare(strict_types=1);

namespace Comave\League\Model\Source\Club;

use Comave\Club\Model\ClublistFactory;
use Magento\Framework\Data\OptionSourceInterface;

/**
 * Clubs Source Model
 */
class Options implements OptionSourceInterface
{
    /**
     * @param \Comave\Club\Model\ClublistFactory $clubListFactory
     */
    public function __construct(
        private readonly ClublistFactory $clubListFactory
    ) {
    }

    /**
     * Get options
     *
     * @return array
     */
    public function toOptionArray(): array
    {
        $clubList = $this->clubListFactory->create();

        return $clubList->getAllOptions(false);
    }
}