<?xml version="1.0"?>
<form xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">
                comave_season_form.comave_season_form_data_source
            </item>
        </item>
        <item name="label" xsi:type="string" translate="true">Season Information</item>
        <item name="template" xsi:type="string">templates/form/collapsible</item>
    </argument>
    <settings>
        <buttons>
            <button name="back" class="ComaveSeasonButtonBack"/>
            <button name="delete" class="ComaveSeasonButtonDelete"/>
            <button name="save" class="ComaveSeasonButtonSave"/>
        </buttons>
        <namespace>comave_season_form</namespace>
        <dataScope>data</dataScope>
        <deps>
            <dep>comave_season_form.comave_season_form_data_source</dep>
        </deps>
    </settings>
    <dataSource name="comave_season_form_data_source">
        <argument name="data" xsi:type="array">
            <item name="js_config" xsi:type="array">
                <item name="component" xsi:type="string">Magento_Ui/js/form/provider</item>
            </item>
        </argument>
        <settings>
            <submitUrl path="comave_league/season/save"/>
        </settings>
        <dataProvider class="ComaveSeasonUiFormDataProvider" name="comave_season_form_data_source"/>
    </dataSource>
    <fieldset name="season" sortOrder="10">
        <settings>
            <collapsible>false</collapsible>
            <opened>true</opened>
            <label translate="true">General Information</label>
        </settings>
        <field name="league_id" component="Magento_Ui/js/form/element/ui-select" formElement="select" sortOrder="10">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="filterOptions" xsi:type="boolean">true</item>
                    <item name="multiple" xsi:type="boolean">false</item>
                    <item name="showCheckbox" xsi:type="boolean">false</item>
                    <item name="disableLabel" xsi:type="boolean">true</item>
                    <item name="levelsVisibility" xsi:type="number">1</item>
                    <item name="isRemoveSelectedIcon" xsi:type="boolean">true</item>
                </item>
            </argument>
            <settings>
                <required>true</required>
                <validation>
                    <rule name="required-entry" xsi:type="boolean">true</rule>
                </validation>
                <elementTmpl>ui/grid/filters/elements/ui-select</elementTmpl>
                <label translate="true">League</label>
                <dataScope>league_id</dataScope>
                <componentType>field</componentType>
                <listens>
                    <link name="${ $.namespace }.${ $.namespace }:responseData">setParsed</link>
                </listens>
            </settings>
            <formElements>
                <select>
                    <settings>
                        <options class="Comave\League\Model\Source\League\Options"/>
                    </settings>
                </select>
            </formElements>
        </field>
        <field name="name" formElement="input" sortOrder="20">
            <settings>
                <dataType>text</dataType>
                <label translate="true">Name</label>
                <dataScope>name</dataScope>
                <required>true</required>
                <validation>
                    <rule name="required-entry" xsi:type="boolean">true</rule>
                </validation>
            </settings>
        </field>
        <field name="max_clubs" formElement="input" sortOrder="30">
            <settings>
                <dataType>text</dataType>
                <label translate="true">Maximum Clubs</label>
                <dataScope>max_clubs</dataScope>
                <required>true</required>
                <validation>
                    <rule name="required-entry" xsi:type="boolean">true</rule>
                </validation>
            </settings>
        </field>
        <field name="start_date" formElement="date" sortOrder="40">
            <settings>
                <dataType>date</dataType>
                <label translate="true">Start Date</label>
                <dataScope>start_date</dataScope>
                <required>true</required>
                <validation>
                    <rule name="required-entry" xsi:type="boolean">true</rule>
                </validation>
            </settings>
            <formElements>
                <date>
                    <settings>
                        <options>
                            <option name="dateFormat" xsi:type="string">yyyy-MM-dd</option>
                            <option name="timeFormat" xsi:type="string">HH:mm:ss</option>
                            <option name="showsTime" xsi:type="boolean">false</option>
                        </options>
                    </settings>
                </date>
            </formElements>
        </field>
        <field name="end_date" formElement="date" sortOrder="50">
            <settings>
                <dataType>date</dataType>
                <label translate="true">End Date</label>
                <dataScope>end_date</dataScope>
                <required>true</required>
                <validation>
                    <rule name="required-entry" xsi:type="boolean">true</rule>
                </validation>
            </settings>
            <formElements>
                <date>
                    <settings>
                        <options>
                            <option name="dateFormat" xsi:type="string">yyyy-MM-dd</option>
                            <option name="timeFormat" xsi:type="string">HH:mm:ss</option>
                            <option name="showsTime" xsi:type="boolean">false</option>
                        </options>
                    </settings>
                </date>
            </formElements>
        </field>
        <field name="is_current" formElement="checkbox" sortOrder="60">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="default" xsi:type="number">0</item>
                </item>
            </argument>
            <settings>
                <dataType>boolean</dataType>
                <label translate="true">Is Current Season</label>
                <dataScope>is_current</dataScope>
            </settings>
            <formElements>
                <checkbox>
                    <settings>
                        <valueMap>
                            <map name="false" xsi:type="number">0</map>
                            <map name="true" xsi:type="number">1</map>
                        </valueMap>
                        <prefer>toggle</prefer>
                    </settings>
                </checkbox>
            </formElements>
        </field>
    </fieldset>
</form>