<?xml version="1.0"?>
<form xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">
                comave_league_club_form.comave_league_club_form_data_source
            </item>
        </item>
        <item name="label" xsi:type="string" translate="true">League Club Information</item>
        <item name="template" xsi:type="string">templates/form/collapsible</item>
    </argument>
    <settings>
        <buttons>
            <button name="back" class="ComaveLeagueClubButtonBack"/>
            <button name="delete" class="ComaveLeagueClubButtonDelete"/>
            <button name="save" class="ComaveLeagueClubButtonSave"/>
        </buttons>
        <namespace>comave_league_club_form</namespace>
        <dataScope>data</dataScope>
        <deps>
            <dep>comave_league_club_form.comave_league_club_form_data_source</dep>
        </deps>
    </settings>
    <dataSource name="comave_league_club_form_data_source">
        <argument name="data" xsi:type="array">
            <item name="js_config" xsi:type="array">
                <item name="component" xsi:type="string">Magento_Ui/js/form/provider</item>
            </item>
        </argument>
        <settings>
            <submitUrl path="comave_league/leagueClub/save"/>
        </settings>
        <dataProvider class="ComaveLeagueClubUiFormDataProvider" name="comave_league_club_form_data_source"/>
    </dataSource>
    <fieldset name="league_club">
        <settings>
            <label translate="true">General Information</label>
        </settings>
        <field name="league_club_id" formElement="input">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="source" xsi:type="string">league_club</item>
                </item>
            </argument>
            <settings>
                <dataType>text</dataType>
                <visible>false</visible>
                <dataScope>league_club_id</dataScope>
            </settings>
        </field>
        <field name="season_id" formElement="select">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="source" xsi:type="string">league_club</item>
                </item>
            </argument>
            <settings>
                <dataType>int</dataType>
                <label translate="true">Season</label>
                <dataScope>season_id</dataScope>
                <validation>
                    <rule name="required-entry" xsi:type="boolean">true</rule>
                </validation>
            </settings>
            <formElements>
                <select>
                    <settings>
                        <options class="Comave\League\Model\Source\Season\Options"/>
                    </settings>
                </select>
            </formElements>
        </field>
        <field name="club_id" formElement="select">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="source" xsi:type="string">league_club</item>
                </item>
            </argument>
            <settings>
                <dataType>int</dataType>
                <label translate="true">Club</label>
                <dataScope>club_id</dataScope>
                <validation>
                    <rule name="required-entry" xsi:type="boolean">true</rule>
                </validation>
            </settings>
            <formElements>
                <select>
                    <settings>
                        <options class="Comave\League\Model\Source\Club\Options"/>
                    </settings>
                </select>
            </formElements>
        </field>
        <field name="rank" formElement="input">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="source" xsi:type="string">league_club</item>
                </item>
            </argument>
            <settings>
                <dataType>int</dataType>
                <label translate="true">Rank</label>
                <dataScope>rank</dataScope>
                <validation>
                    <rule name="required-entry" xsi:type="boolean">true</rule>
                    <rule name="validate-greater-than-zero" xsi:type="boolean">true</rule>
                </validation>
            </settings>
        </field>
        <field name="points" formElement="input">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="source" xsi:type="string">league_club</item>
                </item>
            </argument>
            <settings>
                <dataType>int</dataType>
                <label translate="true">Points</label>
                <dataScope>points</dataScope>
                <validation>
                    <rule name="required-entry" xsi:type="boolean">true</rule>
                    <rule name="validate-zero-or-greater" xsi:type="boolean">true</rule>
                </validation>
            </settings>
        </field>
    </fieldset>
    <fieldset name="statistics">
        <settings>
            <label translate="true">Match Statistics</label>
        </settings>
        <field name="matches_played" formElement="input">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="source" xsi:type="string">league_club</item>
                </item>
            </argument>
            <settings>
                <dataType>int</dataType>
                <label translate="true">Matches Played</label>
                <dataScope>matches_played</dataScope>
                <validation>
                    <rule name="required-entry" xsi:type="boolean">true</rule>
                    <rule name="validate-zero-or-greater" xsi:type="boolean">true</rule>
                </validation>
            </settings>
        </field>
        <field name="wins" formElement="input">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="source" xsi:type="string">league_club</item>
                </item>
            </argument>
            <settings>
                <dataType>int</dataType>
                <label translate="true">Wins</label>
                <dataScope>wins</dataScope>
                <validation>
                    <rule name="required-entry" xsi:type="boolean">true</rule>
                    <rule name="validate-zero-or-greater" xsi:type="boolean">true</rule>
                </validation>
            </settings>
        </field>
        <field name="draws" formElement="input">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="source" xsi:type="string">league_club</item>
                </item>
            </argument>
            <settings>
                <dataType>int</dataType>
                <label translate="true">Draws</label>
                <dataScope>draws</dataScope>
                <validation>
                    <rule name="required-entry" xsi:type="boolean">true</rule>
                    <rule name="validate-zero-or-greater" xsi:type="boolean">true</rule>
                </validation>
            </settings>
        </field>
        <field name="losses" formElement="input">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="source" xsi:type="string">league_club</item>
                </item>
            </argument>
            <settings>
                <dataType>int</dataType>
                <label translate="true">Losses</label>
                <dataScope>losses</dataScope>
                <validation>
                    <rule name="required-entry" xsi:type="boolean">true</rule>
                    <rule name="validate-zero-or-greater" xsi:type="boolean">true</rule>
                </validation>
            </settings>
        </field>
        <field name="goals_for" formElement="input">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="source" xsi:type="string">league_club</item>
                </item>
            </argument>
            <settings>
                <dataType>int</dataType>
                <label translate="true">Goals For</label>
                <dataScope>goals_for</dataScope>
                <validation>
                    <rule name="required-entry" xsi:type="boolean">true</rule>
                    <rule name="validate-zero-or-greater" xsi:type="boolean">true</rule>
                </validation>
            </settings>
        </field>
        <field name="goals_against" formElement="input">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="source" xsi:type="string">league_club</item>
                </item>
            </argument>
            <settings>
                <dataType>int</dataType>
                <label translate="true">Goals Against</label>
                <dataScope>goals_against</dataScope>
                <validation>
                    <rule name="required-entry" xsi:type="boolean">true</rule>
                    <rule name="validate-zero-or-greater" xsi:type="boolean">true</rule>
                </validation>
            </settings>
        </field>
    </fieldset>
</form>