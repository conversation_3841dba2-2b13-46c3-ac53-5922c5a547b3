<?xml version="1.0"?>
<form xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">
                comave_league_form.comave_league_form_data_source
            </item>
        </item>
        <item name="label" xsi:type="string" translate="true">Blacklist Information</item>
        <item name="template" xsi:type="string">templates/form/collapsible</item>
    </argument>
    <settings>
        <buttons>
            <button name="back" class="ComaveLeagueButtonBack"/>
            <button name="delete" class="ComaveLeagueButtonDelete"/>
            <button name="save" class="ComaveLeagueButtonSave"/>
        </buttons>
        <namespace>comave_league_form</namespace>
        <dataScope>data</dataScope>
        <deps>
            <dep>comave_league_form.comave_league_form_data_source</dep>
        </deps>
    </settings>
    <dataSource name="comave_league_form_data_source">
        <argument name="data" xsi:type="array">
            <item name="js_config" xsi:type="array">
                <item name="component" xsi:type="string">Magento_Ui/js/form/provider</item>
            </item>
        </argument>
        <settings>
            <submitUrl path="comave_league/league/save"/>
        </settings>
        <dataProvider class="ComaveLeagueUiFormDataProvider" name="comave_league_form_data_source"/>
    </dataSource>
    <fieldset name="league" sortOrder="10">
        <settings>
            <collapsible>false</collapsible>
            <opened>true</opened>
            <label translate="true">General Information</label>
        </settings>
        <field name="name" formElement="input" sortOrder="10">
            <settings>
                <dataType>text</dataType>
                <label translate="true">Name</label>
                <dataScope>name</dataScope>
                <required>true</required>
                <validation>
                    <rule name="required-entry" xsi:type="boolean">true</rule>
                </validation>
            </settings>
        </field>
        <field name="code" formElement="input" sortOrder="20">
            <settings>
                <dataType>text</dataType>
                <label translate="true">Code</label>
                <dataScope>code</dataScope>
                <required>true</required>
                <validation>
                    <rule name="required-entry" xsi:type="boolean">true</rule>
                    <rule name="max_text_length" xsi:type="number">50</rule>
                </validation>
            </settings>
        </field>
        <field name="logo" formElement="imageUploader" sortOrder="30">
            <settings>
                <elementTmpl>ui/form/element/uploader/image</elementTmpl>
                <dataType>string</dataType>
                <label translate="true">Logo</label>
                <visible>true</visible>
                <required>false</required>
            </settings>
            <formElements>
                <imageUploader>
                    <settings>
                        <required>false</required>
                        <uploaderConfig>
                            <param xsi:type="url" name="url" path="comave_league/league_image/upload/field/logo"/>
                        </uploaderConfig>
                        <previewTmpl>Umc_Crud/preview</previewTmpl>
                        <initialMediaGalleryOpenSubpath>comave/league</initialMediaGalleryOpenSubpath>
                        <allowedExtensions>jpg jpeg gif png</allowedExtensions>
                    </settings>
                </imageUploader>
            </formElements>
        </field>
        <field name="country_code" formElement="select" sortOrder="40">
            <settings>
                <dataType>select</dataType>
                <label translate="true">Country</label>
                <dataScope>country_code</dataScope>
                <required>true</required>
                <validation>
                    <rule name="required-entry" xsi:type="boolean">true</rule>
                </validation>
            </settings>
            <formElements>
                <select>
                    <settings>
                        <options class="Magento\Directory\Model\Config\Source\Country"/>
                    </settings>
                </select>
            </formElements>
        </field>
        <field name="description" formElement="textarea" sortOrder="50">
            <settings>
                <dataType>text</dataType>
                <label translate="true">Description</label>
                <dataScope>description</dataScope>
            </settings>
        </field>
        <field name="status" formElement="select" sortOrder="60">
            <settings>
                <dataType>select</dataType>
                <label translate="true">Status</label>
                <dataScope>status</dataScope>
                <validation>
                    <rule name="required-entry" xsi:type="boolean">true</rule>
                </validation>
            </settings>
            <formElements>
                <select>
                    <settings>
                        <options class="ComaveLeagueStatusSource"/>
                        <caption translate="true">-- Please Select --</caption>
                    </settings>
                </select>
            </formElements>
        </field>
    </fieldset>
</form>