<?xml version="1.0"?>
<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">
                comave_season_listing.comave_season_listing_data_source
            </item>
        </item>
    </argument>
    <settings>
        <buttons>
            <button name="add">
                <url path="*/*/new"/>
                <class>primary</class>
                <label translate="true">Add New Season</label>
            </button>
        </buttons>
        <spinner>comave_season_columns</spinner>
        <deps>
            <dep>comave_season_listing.comave_season_listing_data_source</dep>
        </deps>
    </settings>
    <dataSource name="comave_season_listing_data_source" component="Magento_Ui/js/grid/provider">
        <settings>
            <storageConfig>
                <param name="indexField" xsi:type="string">season_id</param>
            </storageConfig>
            <updateUrl path="mui/index/render"/>
        </settings>
        <dataProvider class="Magento\Framework\View\Element\UiComponent\DataProvider\DataProvider"
                      name="comave_season_listing_data_source">
            <settings>
                <requestFieldName>season_id</requestFieldName>
                <primaryFieldName>season_id</primaryFieldName>
            </settings>
        </dataProvider>
    </dataSource>
    <listingToolbar name="listing_top">
        <settings>
            <sticky>true</sticky>
        </settings>
        <bookmark name="bookmarks"/>
        <columnsControls name="columns_controls"/>
        <filterSearch name="fulltext"/>
        <filters name="listing_filters">
            <argument name="data" xsi:type="array">
                <item name="observers" xsi:type="array">
                    <item name="column" xsi:type="string">column</item>
                </item>
            </argument>
            <settings>
                <templates>
                    <filters>
                        <select>
                            <param name="template" xsi:type="string">ui/grid/filters/elements/ui-select</param>
                            <param name="component" xsi:type="string">Magento_Ui/js/form/element/ui-select</param>
                        </select>
                    </filters>
                </templates>
            </settings>
        </filters>
        <massaction name="listing_massaction">
            <action name="delete">
                <settings>
                    <confirm>
                        <message translate="true">Are you sure you want to delete the selected seasons?</message>
                        <title translate="true">Delete</title>
                    </confirm>
                    <url path="comave_league/season/massDelete"/>
                    <type>delete</type>
                    <label translate="true">Delete</label>
                </settings>
            </action>
            <action name="edit">
                <settings>
                    <callback>
                        <target>editSelected</target>
                        <provider>comave_season_listing.comave_season_listing.comave_season_columns_editor</provider>
                    </callback>
                    <type>edit</type>
                    <label translate="true">Edit</label>
                </settings>
            </action>
        </massaction>
        <paging name="listing_paging"/>
    </listingToolbar>
    <columns name="comave_season_columns">
        <settings>
            <editorConfig>
                <param name="clientConfig" xsi:type="array">
                    <item name="saveUrl" xsi:type="url" path="comave_league/season/inlineEdit"/>
                    <item name="validateBeforeSave" xsi:type="boolean">false</item>
                </param>
                <param name="indexField" xsi:type="string">season_id</param>
                <param name="enabled" xsi:type="boolean">true</param>
                <param name="selectProvider" xsi:type="string">
                    comave_season_listing.comave_season_listing.comave_season_columns.ids
                </param>
            </editorConfig>
            <childDefaults>
                <param name="fieldAction" xsi:type="array">
                    <item name="provider" xsi:type="string">
                        comave_season_listing.comave_season_listing.comave_season_columns_editor
                    </item>
                    <item name="target" xsi:type="string">startEdit</item>
                    <item name="params" xsi:type="array">
                        <item name="0" xsi:type="string">${ $.$data.rowIndex }</item>
                        <item name="1" xsi:type="boolean">true</item>
                    </item>
                </param>
            </childDefaults>
        </settings>
        <selectionsColumn name="ids">
            <settings>
                <indexField>season_id</indexField>
                <resizeEnabled>false</resizeEnabled>
                <resizeDefaultWidth>20</resizeDefaultWidth>
            </settings>
        </selectionsColumn>
        <column name="season_id">
            <settings>
                <filter>textRange</filter>
                <label translate="true">ID</label>
                <sorting>asc</sorting>
                <visible>false</visible>
            </settings>
        </column>
        <column name="league_id" component="Magento_Ui/js/grid/columns/select">
            <settings>
                <filter>select</filter>
                <dataType>select</dataType>
                <options class="Comave\League\Model\Source\League\Options"/>
                <label translate="true">League</label>
                <draggable>false</draggable>
                <editor>
                    <editorType>select</editorType>
                </editor>
            </settings>
        </column>
        <column name="name">
            <settings>
                <filter>text</filter>
                <label translate="true">Name</label>
                <draggable>false</draggable>
                <sortable>true</sortable>
                <editor>
                    <validation>
                        <rule name="required-entry" xsi:type="boolean">true</rule>
                    </validation>
                    <editorType>text</editorType>
                </editor>
            </settings>
        </column>
        <column name="max_clubs">
            <settings>
                <filter>text</filter>
                <label translate="true">Maximum Clubs</label>
                <draggable>false</draggable>
                <sortable>true</sortable>
                <editor>
                    <validation>
                        <rule name="required-entry" xsi:type="boolean">true</rule>
                    </validation>
                    <editorType>text</editorType>
                </editor>
            </settings>
        </column>
        <column name="start_date" class="Magento\Ui\Component\Listing\Columns\Date"
                component="Magento_Ui/js/grid/columns/date">
            <settings>
                <filter>dateRange</filter>
                <dataType>date</dataType>
                <label translate="true">Start Date</label>
                <draggable>false</draggable>
                <sortable>true</sortable>
                <editor>
                    <validation>
                        <rule name="required-entry" xsi:type="boolean">true</rule>
                    </validation>
                    <editorType>date</editorType>
                </editor>
            </settings>
        </column>
        <column name="end_date" class="Magento\Ui\Component\Listing\Columns\Date"
                component="Magento_Ui/js/grid/columns/date">
            <settings>
                <filter>dateRange</filter>
                <dataType>date</dataType>
                <label translate="true">End Date</label>
                <draggable>false</draggable>
                <sortable>true</sortable>
                <editor>
                    <validation>
                        <rule name="required-entry" xsi:type="boolean">true</rule>
                    </validation>
                    <editorType>date</editorType>
                </editor>
            </settings>
        </column>
        <column name="is_current" component="Magento_Ui/js/grid/columns/select">
            <settings>
                <filter>select</filter>
                <dataType>select</dataType>
                <label translate="true">Is Current</label>
                <draggable>false</draggable>
                <options class="Magento\Config\Model\Config\Source\Yesno"/>
                <editor>
                    <editorType>select</editorType>
                </editor>
            </settings>
        </column>
        <column name="created_at" class="Magento\Ui\Component\Listing\Columns\Date"
                component="Magento_Ui/js/grid/columns/date">
            <settings>
                <filter>dateRange</filter>
                <dataType>date</dataType>
                <label translate="true">Created At</label>
                <draggable>false</draggable>
                <visible>false</visible>
            </settings>
        </column>
        <column name="updated_at" class="Magento\Ui\Component\Listing\Columns\Date"
                component="Magento_Ui/js/grid/columns/date">
            <settings>
                <filter>dateRange</filter>
                <dataType>date</dataType>
                <label translate="true">Updated At</label>
                <draggable>false</draggable>
                <visible>false</visible>
            </settings>
        </column>
        <actionsColumn name="actions" class="ComaveSeasonGridActions">
            <settings>
                <indexField>season_id</indexField>
                <resizeEnabled>false</resizeEnabled>
            </settings>
        </actionsColumn>
    </columns>
</listing>