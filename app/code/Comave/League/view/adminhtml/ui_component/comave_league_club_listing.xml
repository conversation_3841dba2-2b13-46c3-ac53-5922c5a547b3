<?xml version="1.0"?>
<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">
                comave_league_club_listing.comave_league_club_listing_data_source
            </item>
        </item>
    </argument>
    <settings>
        <buttons>
            <button name="add">
                <url path="*/*/new"/>
                <class>primary</class>
                <label translate="true">Assign Club to League Season</label>
            </button>
        </buttons>
        <spinner>comave_league_club_columns</spinner>
        <deps>
            <dep>comave_league_club_listing.comave_league_club_listing_data_source</dep>
        </deps>
    </settings>
    <dataSource name="comave_league_club_listing_data_source" component="Magento_Ui/js/grid/provider">
        <settings>
            <storageConfig>
                <param name="indexField" xsi:type="string">league_club_id</param>
            </storageConfig>
            <updateUrl path="mui/index/render"/>
        </settings>
        <dataProvider class="Magento\Framework\View\Element\UiComponent\DataProvider\DataProvider"
                      name="comave_league_club_listing_data_source">
            <settings>
                <requestFieldName>league_club_id</requestFieldName>
                <primaryFieldName>league_club_id</primaryFieldName>
            </settings>
        </dataProvider>
    </dataSource>
    <listingToolbar name="listing_top">
        <settings>
            <sticky>true</sticky>
        </settings>
        <bookmark name="bookmarks"/>
        <columnsControls name="columns_controls"/>
        <filterSearch name="fulltext"/>
        <filters name="listing_filters">
            <argument name="data" xsi:type="array">
                <item name="observers" xsi:type="array">
                    <item name="column" xsi:type="string">column</item>
                </item>
            </argument>
            <settings>
                <templates>
                    <filters>
                        <select>
                            <param name="template" xsi:type="string">ui/grid/filters/elements/ui-select</param>
                            <param name="component" xsi:type="string">Magento_Ui/js/form/element/ui-select</param>
                        </select>
                    </filters>
                </templates>
            </settings>
        </filters>
        <massaction name="listing_massaction">
            <action name="delete">
                <settings>
                    <confirm>
                        <message translate="true">Are you sure you want to delete the selected club from league?</message>
                        <title translate="true">Delete</title>
                    </confirm>
                    <url path="comave_league/leagueClub/massDelete"/>
                    <type>delete</type>
                    <label translate="true">Delete</label>
                </settings>
            </action>
            <action name="edit">
                <settings>
                    <callback>
                        <target>editSelected</target>
                        <provider>comave_league_club_listing.comave_league_club_listing.comave_league_club_columns_editor</provider>
                    </callback>
                    <type>edit</type>
                    <label translate="true">Edit</label>
                </settings>
            </action>
        </massaction>
        <paging name="listing_paging"/>
    </listingToolbar>
    <columns name="comave_league_club_columns">
        <settings>
            <editorConfig>
                <param name="clientConfig" xsi:type="array">
                    <item name="saveUrl" xsi:type="url" path="comave_league/leagueClub/inlineEdit"/>
                    <item name="validateBeforeSave" xsi:type="boolean">false</item>
                </param>
                <param name="indexField" xsi:type="string">league_club_id</param>
                <param name="enabled" xsi:type="boolean">true</param>
                <param name="selectProvider" xsi:type="string">comave_league_club_listing.comave_league_club_listing.comave_league_club_columns.ids</param>
            </editorConfig>
            <childDefaults>
                <param name="fieldAction" xsi:type="array">
                    <item name="provider" xsi:type="string">comave_league_club_listing.comave_league_club_listing.comave_league_club_columns_editor</item>
                    <item name="target" xsi:type="string">startEdit</item>
                    <item name="params" xsi:type="array">
                        <item name="0" xsi:type="string">${ $.$data.rowIndex }</item>
                        <item name="1" xsi:type="boolean">true</item>
                    </item>
                </param>
            </childDefaults>
        </settings>
        <selectionsColumn name="ids">
            <settings>
                <indexField>league_club_id</indexField>
            </settings>
        </selectionsColumn>
        <column name="league_club_id">
            <settings>
                <filter>textRange</filter>
                <label translate="true">ID</label>
                <sorting>asc</sorting>
                <visible>false</visible>
            </settings>
        </column>
        <column name="season_id" component="Magento_Ui/js/grid/columns/select">
            <settings>
                <filter>select</filter>
                <dataType>select</dataType>
                <options class="Comave\League\Model\Source\Season\Options"/>
                <label translate="true">Season</label>
                <draggable>false</draggable>
            </settings>
        </column>
        <column name="rank">
            <settings>
                <filter>textRange</filter>
                <editor>
                    <editorType>text</editorType>
                    <validation>
                        <rule name="required-entry" xsi:type="boolean">true</rule>
                        <rule name="validate-greater-than-zero" xsi:type="boolean">true</rule>
                    </validation>
                </editor>
                <label translate="true">Rank</label>
                <draggable>false</draggable>
                <sorting>asc</sorting>
            </settings>
        </column>
        <column name="club_id" component="Magento_Ui/js/grid/columns/select">
            <settings>
                <filter>select</filter>
                <dataType>select</dataType>
                <options class="Comave\League\Model\Source\Club\Options"/>
                <label translate="true">Club</label>
                <draggable>false</draggable>
            </settings>
        </column>
        <column name="points">
            <settings>
                <filter>textRange</filter>
                <editor>
                    <editorType>text</editorType>
                    <validation>
                        <rule name="required-entry" xsi:type="boolean">true</rule>
                        <rule name="validate-zero-or-greater" xsi:type="boolean">true</rule>
                    </validation>
                </editor>
                <label translate="true">Points</label>
            </settings>
        </column>
        <column name="matches_played">
            <settings>
                <filter>textRange</filter>
                <editor>
                    <editorType>text</editorType>
                    <validation>
                        <rule name="required-entry" xsi:type="boolean">true</rule>
                        <rule name="validate-zero-or-greater" xsi:type="boolean">true</rule>
                    </validation>
                </editor>
                <label translate="true">Played</label>
            </settings>
        </column>
        <column name="wins">
            <settings>
                <filter>textRange</filter>
                <editor>
                    <editorType>text</editorType>
                    <validation>
                        <rule name="required-entry" xsi:type="boolean">true</rule>
                        <rule name="validate-zero-or-greater" xsi:type="boolean">true</rule>
                    </validation>
                </editor>
                <label translate="true">Wins</label>
            </settings>
        </column>
        <column name="draws">
            <settings>
                <filter>textRange</filter>
                <editor>
                    <editorType>text</editorType>
                    <validation>
                        <rule name="required-entry" xsi:type="boolean">true</rule>
                        <rule name="validate-zero-or-greater" xsi:type="boolean">true</rule>
                    </validation>
                </editor>
                <label translate="true">Draws</label>
            </settings>
        </column>
        <column name="losses">
            <settings>
                <filter>textRange</filter>
                <editor>
                    <editorType>text</editorType>
                    <validation>
                        <rule name="required-entry" xsi:type="boolean">true</rule>
                        <rule name="validate-zero-or-greater" xsi:type="boolean">true</rule>
                    </validation>
                </editor>
                <label translate="true">Losses</label>
            </settings>
        </column>
        <column name="goals_for">
            <settings>
                <filter>textRange</filter>
                <editor>
                    <editorType>text</editorType>
                    <validation>
                        <rule name="required-entry" xsi:type="boolean">true</rule>
                        <rule name="validate-zero-or-greater" xsi:type="boolean">true</rule>
                    </validation>
                </editor>
                <label translate="true">Goals For</label>
            </settings>
        </column>
        <column name="goals_against">
            <settings>
                <filter>textRange</filter>
                <editor>
                    <editorType>text</editorType>
                    <validation>
                        <rule name="required-entry" xsi:type="boolean">true</rule>
                        <rule name="validate-zero-or-greater" xsi:type="boolean">true</rule>
                    </validation>
                </editor>
                <label translate="true">Goals Against</label>
            </settings>
        </column>
        <column name="created_at" class="Magento\Ui\Component\Listing\Columns\Date" component="Magento_Ui/js/grid/columns/date">
            <settings>
                <filter>dateRange</filter>
                <dataType>date</dataType>
                <label translate="true">Created</label>
                <visible>false</visible>
            </settings>
        </column>
        <column name="updated_at" class="Magento\Ui\Component\Listing\Columns\Date" component="Magento_Ui/js/grid/columns/date">
            <settings>
                <filter>dateRange</filter>
                <dataType>date</dataType>
                <label translate="true">Updated</label>
                <visible>false</visible>
            </settings>
        </column>
        <actionsColumn name="actions" class="ComaveLeagueClubGridActions">
            <settings>
                <indexField>league_club_id</indexField>
            </settings>
        </actionsColumn>
    </columns>
</listing>