<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <!--[+] define the ui config for entity -->
    <virtualType name="ComaveLeagueUiConfig" type="Umc\Crud\Ui\EntityUiConfig">
        <arguments>
            <argument name="interface" xsi:type="string">Comave\League\Api\Data\LeagueInterface</argument>
            <argument name="data" xsi:type="array">
                <!-- this is the attribute / field name that represents your entity -->
                <item name="name_attribute" xsi:type="string">name</item>
                <item name="edit_url" xsi:type="string">comave_league/league/edit</item>
                <item name="delete_url" xsi:type="string">comave_league/league/delete</item>
                <item name="labels" xsi:type="array">
                    <item name="new" xsi:type="string" translatable="true">Add New League</item>
                    <item name="back" xsi:type="string" translatable="true">Back to list</item>
                    <item name="save" xsi:type="string" translatable="true">Save League</item>
                    <item name="delete" xsi:type="string" translatable="true">Delete League</item>
                    <item name="delete_message" xsi:type="string" translatable="true">
                        Are you sure you want to delete this league?
                    </item>
                </item>
                <item name="save" xsi:type="array">
                    <item name="allow_close" xsi:type="boolean">false</item>
                    <item name="allow_duplicate" xsi:type="boolean">false</item>
                </item>
                <item name="list" xsi:type="array">
                    <item name="page_title" xsi:type="string" translatable="true">League Management</item>
                </item>
                <item name="messages" xsi:type="array">
                    <item name="delete" xsi:type="array">
                        <item name="success" xsi:type="string" translatable="true">
                            League successfully deleted
                        </item>
                        <item name="missing_entity" xsi:type="string" translatable="true">
                            Couldn't find the league to delete
                        </item>
                        <item name="error" xsi:type="string" translatable="true">
                            There was a problem deleting the league
                        </item>
                    </item>
                    <item name="save" xsi:type="array">
                        <item name="success" xsi:type="string" translatable="true">
                            League successfully saved
                        </item>
                        <item name="error" xsi:type="string" translatable="true">
                            There was a problem saving the league
                        </item>
                        <item name="duplicate" xsi:type="string" translatable="true">
                            League duplicated successfully
                        </item>
                    </item>
                    <item name="mass_delete" xsi:type="array">
                        <item name="success" xsi:type="string" translatable="true">
                            %1 Leagues were successfully deleted
                        </item>
                        <item name="error" xsi:type="string" translatable="true">
                            There was a problem deleting these leagues
                        </item>
                    </item>
                </item>
            </argument>
        </arguments>
    </virtualType>
    <!--[-] define the ui config for "league" entity -->
    <!--[+] configure admin controllers to use the ui entity config for "league" -->
    <virtualType name="ComaveLeagueDataModifier"
                 type="Umc\Crud\Ui\Form\DataModifier\CompositeDataModifier">
        <arguments>
            <argument name="modifiers" xsi:type="array">
                <item name="image" xsi:type="object">ComaveLeagueUiFormImageModifier</item>
            </argument>
        </arguments>
    </virtualType>
    <virtualType name="ComaveLeagueSaveDataProcessor"
                 type="Umc\Crud\Ui\SaveDataProcessor\CompositeProcessor">
        <arguments>
            <argument name="modifiers" xsi:type="array">
                <item name="image" xsi:type="object">ComaveLeagueSaveImageProcessor</item>
            </argument>
        </arguments>
    </virtualType>
    <type name="Comave\League\Controller\Adminhtml\League\Index">
        <arguments>
            <argument name="uiConfig" xsi:type="object">ComaveLeagueUiConfig</argument>
        </arguments>
    </type>
    <type name="Comave\League\Controller\Adminhtml\League\Edit">
        <arguments>
            <argument name="uiConfig" xsi:type="object">ComaveLeagueUiConfig</argument>
            <argument name="entityUiManager" xsi:type="object">Comave\League\Model\LeagueUiManager</argument>
        </arguments>
    </type>
    <type name="Comave\League\Controller\Adminhtml\League\Save">
        <arguments>
            <argument name="entityUiManager" xsi:type="object">Comave\League\Model\LeagueUiManager</argument>
            <argument name="dataProcessor" xsi:type="object">ComaveLeagueSaveDataProcessor</argument>
            <argument name="uiConfig" xsi:type="object">ComaveLeagueUiConfig</argument>
        </arguments>
    </type>
    <type name="Comave\League\Controller\Adminhtml\League\InlineEdit">
        <arguments>
            <argument name="entityUiManager" xsi:type="object">Comave\League\Model\LeagueUiManager</argument>
            <argument name="dataProcessor" xsi:type="object">Umc\Crud\Ui\SaveDataProcessor\NullProcessor</argument>
            <argument name="uiConfig" xsi:type="object">ComaveLeagueUiConfig</argument>
        </arguments>
    </type>
    <type name="Comave\League\Controller\Adminhtml\League\Delete">
        <arguments>
            <argument name="uiConfig" xsi:type="object">ComaveLeagueUiConfig</argument>
            <argument name="uiManager" xsi:type="object">Comave\League\Model\LeagueUiManager</argument>
        </arguments>
    </type>
    <type name="Comave\League\Controller\Adminhtml\League\MassDelete">
        <arguments>
            <argument name="uiConfig" xsi:type="object">ComaveLeagueUiConfig</argument>
            <argument name="uiManager" xsi:type="object">Comave\League\Model\LeagueUiManager</argument>
            <argument name="collectionProvider" xsi:type="object">Comave\League\Model\LeagueUiCollectionProvider
            </argument>
        </arguments>
    </type>
    <type name="Comave\League\Controller\Adminhtml\League\Image\Upload">
        <arguments>
            <argument name="uploader" xsi:type="object">ComaveLeagueImageUploader</argument>
        </arguments>
    </type>
    <!--[+] configure admin controllers to use the ui entity config for "league" -->
    <virtualType name="ComaveLeagueFormDataModifier" type="Umc\Crud\Ui\Form\DataModifier\CompositeDataModifier">
        <arguments>
            <argument name="modifiers" xsi:type="array">
                <!-- add here all form data modifiers -->
                <item name="image" xsi:type="object">ComaveLeagueUiFormImageModifier</item>
            </argument>
        </arguments>
    </virtualType>
    <virtualType name="ComaveLeagueUiFormImageModifier" type="Umc\Crud\Ui\Form\DataModifier\Upload">
        <arguments>
            <argument name="uploader" xsi:type="object">ComaveLeagueImageUploader</argument>
            <argument name="fileInfo" xsi:type="object">ComaveLeagueImageInfo</argument>
            <argument name="fields" xsi:type="array">
                <item name="logo" xsi:type="string">logo</item>
            </argument>
        </arguments>
    </virtualType>
    <virtualType name="ComaveLeagueUiFormDataProvider" type="Umc\Crud\Ui\Form\DataProvider">
        <arguments>
            <argument name="primaryFieldName" xsi:type="object">league_id</argument>
            <argument name="requestFieldName" xsi:type="object">league_id</argument>
            <argument name="uiConfig" xsi:type="object">ComaveLeagueUiConfig</argument>
            <argument name="dataModifier" xsi:type="object">ComaveLeagueDataModifier</argument>
            <argument name="collectionProvider" xsi:type="object">
                Comave\League\Model\LeagueUiCollectionProvider
            </argument>
        </arguments>
    </virtualType>
    <virtualType name="ComaveLeagueSaveImageProcessor" type="Umc\Crud\Ui\SaveDataProcessor\Upload">
        <arguments>
            <argument name="fileInfo" xsi:type="object">ComaveLeagueImageInfo</argument>
            <argument name="uploader" xsi:type="object">ComaveLeagueImageUploader</argument>
            <argument name="fields" xsi:type="array">
                <item name="logo" xsi:type="string">logo</item>
            </argument>
            <argument name="strict" xsi:type="boolean">true</argument>
        </arguments>
    </virtualType>
    <!--[+] form button configuration for 'league' -->
    <virtualType name="ComaveLeagueButtonBack" type="Umc\Crud\Block\Adminhtml\Button\Back">
        <arguments>
            <argument name="uiConfig" xsi:type="object">ComaveLeagueUiConfig</argument>
        </arguments>
    </virtualType>
    <virtualType name="ComaveLeagueButtonSave" type="Umc\Crud\Block\Adminhtml\Button\Save">
        <arguments>
            <argument name="uiConfig" xsi:type="object">ComaveLeagueUiConfig</argument>
        </arguments>
    </virtualType>
    <virtualType name="ComaveLeagueButtonDelete" type="Umc\Crud\Block\Adminhtml\Button\Delete">
        <arguments>
            <argument name="uiConfig" xsi:type="object">ComaveLeagueUiConfig</argument>
            <argument name="entityUiManager" xsi:type="object">Comave\League\Model\LeagueUiManager
            </argument>
        </arguments>
    </virtualType>
    <!--[-] form button configuration for 'league' -->
    <!--[+] configure the grid actions column  for "league" entity-->
    <virtualType name="ComaveLeagueGridActions" type="Umc\Crud\Ui\Component\Listing\ActionsColumn">
        <arguments>
            <argument name="uiConfig" xsi:type="object">ComaveLeagueUiConfig</argument>
        </arguments>
    </virtualType>
    <!--[-] configure the grid actions column for "league" entity-->
    <!--[+] configure image uploader for 'league' entity-->
    <virtualType name="ComaveLeagueImageUploader" type="Umc\Crud\Model\Uploader">
        <arguments>
            <argument name="baseTmpPath" xsi:type="string">comave/league/image</argument>
            <argument name="basePath" xsi:type="string">comave/league/image</argument>
            <argument name="allowedExtensions" xsi:type="array">
                <item name="jpg" xsi:type="string">jpg</item>
                <item name="jpeg" xsi:type="string">jpeg</item>
                <item name="gif" xsi:type="string">gif</item>
                <item name="png" xsi:type="string">png</item>
            </argument>
        </arguments>
    </virtualType>
    <!--[-] configure image uploader for 'league' entity-->
    
    <!-- Season UI Config -->
    <virtualType name="ComaveSeasonUiConfig" type="Umc\Crud\Ui\EntityUiConfig">
        <arguments>
            <argument name="interface" xsi:type="string">Comave\League\Api\Data\SeasonInterface</argument>
            <argument name="data" xsi:type="array">
                <item name="name_attribute" xsi:type="string">name</item>
                <item name="edit_url" xsi:type="string">comave_league/season/edit</item>
                <item name="delete_url" xsi:type="string">comave_league/season/delete</item>
                <item name="labels" xsi:type="array">
                    <item name="new" xsi:type="string" translatable="true">Add New Season</item>
                    <item name="back" xsi:type="string" translatable="true">Back to list</item>
                    <item name="save" xsi:type="string" translatable="true">Save Season</item>
                    <item name="delete" xsi:type="string" translatable="true">Delete Season</item>
                    <item name="delete_message" xsi:type="string" translatable="true">
                        Are you sure you want to delete this season?
                    </item>
                </item>
                <item name="save" xsi:type="array">
                    <item name="allow_close" xsi:type="boolean">false</item>
                    <item name="allow_duplicate" xsi:type="boolean">false</item>
                </item>
                <item name="list" xsi:type="array">
                    <item name="page_title" xsi:type="string" translatable="true">Season Management</item>
                </item>
                <item name="messages" xsi:type="array">
                    <item name="delete" xsi:type="array">
                        <item name="success" xsi:type="string" translatable="true">Season successfully deleted</item>
                        <item name="missing_entity" xsi:type="string" translatable="true">Couldn't find the season to delete</item>
                        <item name="error" xsi:type="string" translatable="true">There was a problem deleting the season</item>
                    </item>
                    <item name="save" xsi:type="array">
                        <item name="success" xsi:type="string" translatable="true">Season successfully saved</item>
                        <item name="error" xsi:type="string" translatable="true">There was a problem saving the season</item>
                    </item>
                    <item name="mass_delete" xsi:type="array">
                        <item name="success" xsi:type="string" translatable="true">%1 Seasons were successfully deleted</item>
                        <item name="error" xsi:type="string" translatable="true">There was a problem deleting these seasons</item>
                    </item>
                </item>
            </argument>
        </arguments>
    </virtualType>
    
    <!-- Season Controller DI Configuration -->
    <type name="Comave\League\Controller\Adminhtml\Season\Index">
        <arguments>
            <argument name="uiConfig" xsi:type="object">ComaveSeasonUiConfig</argument>
        </arguments>
    </type>
    <type name="Comave\League\Controller\Adminhtml\Season\Edit">
        <arguments>
            <argument name="uiConfig" xsi:type="object">ComaveSeasonUiConfig</argument>
            <argument name="entityUiManager" xsi:type="object">Comave\League\Model\SeasonUiManager</argument>
        </arguments>
    </type>
    <virtualType name="ComaveSeasonSaveDataProcessor"
                 type="Umc\Crud\Ui\SaveDataProcessor\CompositeProcessor">
        <arguments>
            <argument name="modifiers" xsi:type="array">
                <item name="date" xsi:type="object">ComaveSeasonSaveDateProcessor</item>
            </argument>
        </arguments>
    </virtualType>
    <virtualType name="ComaveSeasonSaveDateProcessor"
                 type="Umc\Crud\Ui\SaveDataProcessor\Date">
        <arguments>
            <argument name="fields" xsi:type="array">
                <item name="start_date" xsi:type="string">start_date</item>
                <item name="end_date" xsi:type="string">end_date</item>
            </argument>
        </arguments>
    </virtualType>
    <type name="Comave\League\Controller\Adminhtml\Season\Save">
        <arguments>
            <argument name="entityUiManager" xsi:type="object">Comave\League\Model\SeasonUiManager</argument>
            <argument name="dataProcessor" xsi:type="object">ComaveSeasonSaveDataProcessor</argument>
            <argument name="uiConfig" xsi:type="object">ComaveSeasonUiConfig</argument>
        </arguments>
    </type>
    <type name="Comave\League\Controller\Adminhtml\Season\InlineEdit">
        <arguments>
            <argument name="entityUiManager" xsi:type="object">Comave\League\Model\SeasonUiManager</argument>
            <argument name="dataProcessor" xsi:type="object">ComaveSeasonSaveDataProcessor</argument>
            <argument name="uiConfig" xsi:type="object">ComaveSeasonUiConfig</argument>
        </arguments>
    </type>
    <type name="Comave\League\Controller\Adminhtml\Season\Delete">
        <arguments>
            <argument name="uiConfig" xsi:type="object">ComaveSeasonUiConfig</argument>
            <argument name="uiManager" xsi:type="object">Comave\League\Model\SeasonUiManager</argument>
        </arguments>
    </type>
    <type name="Comave\League\Controller\Adminhtml\Season\MassDelete">
        <arguments>
            <argument name="uiConfig" xsi:type="object">ComaveSeasonUiConfig</argument>
            <argument name="uiManager" xsi:type="object">Comave\League\Model\SeasonUiManager</argument>
            <argument name="collectionProvider" xsi:type="object">Comave\League\Model\SeasonUiCollectionProvider</argument>
        </arguments>
    </type>
    
    <!-- Season Form Data Provider -->
    <virtualType name="ComaveSeasonUiFormDataProvider" type="Umc\Crud\Ui\Form\DataProvider">
        <arguments>
            <argument name="primaryFieldName" xsi:type="string">season_id</argument>
            <argument name="requestFieldName" xsi:type="string">season_id</argument>
            <argument name="uiConfig" xsi:type="object">ComaveSeasonUiConfig</argument>
            <argument name="dataModifier" xsi:type="object">Umc\Crud\Ui\Form\DataModifier\NullModifier</argument>
            <argument name="collectionProvider" xsi:type="object">Comave\League\Model\SeasonUiCollectionProvider</argument>
        </arguments>
    </virtualType>
    
    <!-- Season Form Buttons -->
    <virtualType name="ComaveSeasonButtonBack" type="Umc\Crud\Block\Adminhtml\Button\Back">
        <arguments>
            <argument name="uiConfig" xsi:type="object">ComaveSeasonUiConfig</argument>
        </arguments>
    </virtualType>
    <virtualType name="ComaveSeasonButtonSave" type="Umc\Crud\Block\Adminhtml\Button\Save">
        <arguments>
            <argument name="uiConfig" xsi:type="object">ComaveSeasonUiConfig</argument>
        </arguments>
    </virtualType>
    <virtualType name="ComaveSeasonButtonDelete" type="Umc\Crud\Block\Adminhtml\Button\Delete">
        <arguments>
            <argument name="uiConfig" xsi:type="object">ComaveSeasonUiConfig</argument>
            <argument name="entityUiManager" xsi:type="object">Comave\League\Model\SeasonUiManager</argument>
        </arguments>
    </virtualType>
    
    <!-- Season Grid Actions -->
    <virtualType name="ComaveSeasonGridActions" type="Umc\Crud\Ui\Component\Listing\ActionsColumn">
        <arguments>
            <argument name="uiConfig" xsi:type="object">ComaveSeasonUiConfig</argument>
        </arguments>
    </virtualType>
    
    <!-- League Club UI Config -->
    <virtualType name="ComaveLeagueClubUiConfig" type="Umc\Crud\Ui\EntityUiConfig">
        <arguments>
            <argument name="interface" xsi:type="string">Comave\League\Api\Data\LeagueClubInterface</argument>
            <argument name="data" xsi:type="array">
                <item name="name_attribute" xsi:type="string">league_club_id</item>
                <item name="edit_url" xsi:type="string">comave_league/leagueClub/edit</item>
                <item name="delete_url" xsi:type="string">comave_league/leagueClub/delete</item>
                <item name="labels" xsi:type="array">
                    <item name="new" xsi:type="string" translatable="true">Assign New Club to League</item>
                    <item name="back" xsi:type="string" translatable="true">Back to list</item>
                    <item name="save" xsi:type="string" translatable="true">Assign Club to League</item>
                    <item name="delete" xsi:type="string" translatable="true">Unassign Club from League</item>
                    <item name="delete_message" xsi:type="string" translatable="true">Are you sure you want to delete this league club?</item>
                </item>
                <item name="save" xsi:type="array">
                    <item name="allow_close" xsi:type="boolean">false</item>
                    <item name="allow_duplicate" xsi:type="boolean">false</item>
                </item>
                <item name="list" xsi:type="array">
                    <item name="page_title" xsi:type="string" translatable="true">League Club Management</item>
                </item>
                <item name="messages" xsi:type="array">
                    <item name="delete" xsi:type="array">
                        <item name="success" xsi:type="string" translatable="true">League Club successfully deleted</item>
                        <item name="missing_entity" xsi:type="string" translatable="true">Couldn't find the league club to delete</item>
                        <item name="error" xsi:type="string" translatable="true">There was a problem deleting the league club</item>
                    </item>
                    <item name="save" xsi:type="array">
                        <item name="success" xsi:type="string" translatable="true">League Club successfully saved</item>
                        <item name="error" xsi:type="string" translatable="true">There was a problem saving the league club</item>
                    </item>
                    <item name="mass_delete" xsi:type="array">
                        <item name="success" xsi:type="string" translatable="true">%1 League Clubs were successfully deleted</item>
                        <item name="error" xsi:type="string" translatable="true">There was a problem deleting these league clubs</item>
                    </item>
                </item>
            </argument>
        </arguments>
    </virtualType>
    
    <!-- League Club Controller DI Configuration -->
    <type name="Comave\League\Controller\Adminhtml\LeagueClub\Index">
        <arguments>
            <argument name="uiConfig" xsi:type="object">ComaveLeagueClubUiConfig</argument>
        </arguments>
    </type>
    <type name="Comave\League\Controller\Adminhtml\LeagueClub\Edit">
        <arguments>
            <argument name="uiConfig" xsi:type="object">ComaveLeagueClubUiConfig</argument>
            <argument name="entityUiManager" xsi:type="object">Comave\League\Model\LeagueClubUiManager</argument>
        </arguments>
    </type>
    <type name="Comave\League\Controller\Adminhtml\LeagueClub\Save">
        <arguments>
            <argument name="entityUiManager" xsi:type="object">Comave\League\Model\LeagueClubUiManager</argument>
            <argument name="dataProcessor" xsi:type="object">Umc\Crud\Ui\SaveDataProcessor\NullProcessor</argument>
            <argument name="uiConfig" xsi:type="object">ComaveLeagueClubUiConfig</argument>
        </arguments>
    </type>
    <type name="Comave\League\Controller\Adminhtml\LeagueClub\InlineEdit">
        <arguments>
            <argument name="entityUiManager" xsi:type="object">Comave\League\Model\LeagueClubUiManager</argument>
            <argument name="dataProcessor" xsi:type="object">Umc\Crud\Ui\SaveDataProcessor\NullProcessor</argument>
            <argument name="uiConfig" xsi:type="object">ComaveLeagueClubUiConfig</argument>
        </arguments>
    </type>
    <type name="Comave\League\Controller\Adminhtml\LeagueClub\Delete">
        <arguments>
            <argument name="uiConfig" xsi:type="object">ComaveLeagueClubUiConfig</argument>
            <argument name="uiManager" xsi:type="object">Comave\League\Model\LeagueClubUiManager</argument>
        </arguments>
    </type>
    <type name="Comave\League\Controller\Adminhtml\LeagueClub\MassDelete">
        <arguments>
            <argument name="uiConfig" xsi:type="object">ComaveLeagueClubUiConfig</argument>
            <argument name="uiManager" xsi:type="object">Comave\League\Model\LeagueClubUiManager</argument>
            <argument name="collectionProvider" xsi:type="object">Comave\League\Model\LeagueClubUiCollectionProvider</argument>
        </arguments>
    </type>
    
    <!-- League Club Form Data Provider -->
    <virtualType name="ComaveLeagueClubUiFormDataProvider" type="Umc\Crud\Ui\Form\DataProvider">
        <arguments>
            <argument name="primaryFieldName" xsi:type="string">league_club_id</argument>
            <argument name="requestFieldName" xsi:type="string">league_club_id</argument>
            <argument name="uiConfig" xsi:type="object">ComaveLeagueClubUiConfig</argument>
            <argument name="dataModifier" xsi:type="object">Umc\Crud\Ui\Form\DataModifier\NullModifier</argument>
            <argument name="collectionProvider" xsi:type="object">Comave\League\Model\LeagueClubUiCollectionProvider</argument>
        </arguments>
    </virtualType>
    
    <!-- League Club Grid Data Provider -->
    <virtualType name="ComaveLeagueClubGridDataProvider" type="Umc\Crud\Ui\DataProvider\Grid">
        <arguments>
            <argument name="primaryFieldName" xsi:type="string">league_club_id</argument>
            <argument name="requestFieldName" xsi:type="string">league_club_id</argument>
            <argument name="collectionProvider" xsi:type="object">Comave\League\Model\LeagueClubUiCollectionProvider</argument>
        </arguments>
    </virtualType>
    
    <!-- League Club Form Buttons -->
    <virtualType name="ComaveLeagueClubButtonBack" type="Umc\Crud\Block\Adminhtml\Button\Back">
        <arguments>
            <argument name="uiConfig" xsi:type="object">ComaveLeagueClubUiConfig</argument>
        </arguments>
    </virtualType>
    <virtualType name="ComaveLeagueClubButtonSave" type="Umc\Crud\Block\Adminhtml\Button\Save">
        <arguments>
            <argument name="uiConfig" xsi:type="object">ComaveLeagueClubUiConfig</argument>
        </arguments>
    </virtualType>
    <virtualType name="ComaveLeagueClubButtonDelete" type="Umc\Crud\Block\Adminhtml\Button\Delete">
        <arguments>
            <argument name="uiConfig" xsi:type="object">ComaveLeagueClubUiConfig</argument>
            <argument name="entityUiManager" xsi:type="object">Comave\League\Model\LeagueClubUiManager</argument>
        </arguments>
    </virtualType>
    
    <!-- League Club Grid Actions -->
    <virtualType name="ComaveLeagueClubGridActions" type="Umc\Crud\Ui\Component\Listing\ActionsColumn">
        <arguments>
            <argument name="uiConfig" xsi:type="object">ComaveLeagueClubUiConfig</argument>
        </arguments>
    </virtualType>
</config>
