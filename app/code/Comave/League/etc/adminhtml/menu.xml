<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="../../../Backend/etc/menu.xsd">
    <menu>
        <add id="Comave_League::sports_management"
             title="Sports Management"
             module="Comave_League"
             sortOrder="25"
             resource="Comave_League::sports_management"/>
        <add id="Comave_League::sports_management_leagues"
             title="Leagues"
             module="Comave_League"
             sortOrder="20"
             parent="Comave_League::sports_management"
             resource="Comave_League::sports_management_leagues"
        />
        <add id="Comave_League::sports_management_league"
             title="League Management"
             module="Comave_League"
             sortOrder="10"
             action="comave_league/league/index"
             parent="Comave_League::sports_management_leagues"
             resource="Comave_League::sports_management_league"
        />
        <add id="Comave_League::sports_management_season"
             title="Season Management"
             module="Comave_League"
             sortOrder="20"
             action="comave_league/season/index"
             parent="Comave_League::sports_management_leagues"
             resource="Comave_League::sports_management_season"
        />
        <add id="Comave_League::sports_management_league_club"
             title="League Club Management"
             module="Comave_League"
             sortOrder="30"
             action="comave_league/leagueClub/index"
             parent="Comave_League::sports_management_leagues"
             resource="Comave_League::sports_management_league_club"
        />
    </menu>
</config>