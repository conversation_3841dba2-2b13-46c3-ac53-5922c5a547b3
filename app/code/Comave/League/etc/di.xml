<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <!-- League preferences -->
    <preference for="Comave\League\Api\Data\LeagueInterface" type="Comave\League\Model\League"/>
    <preference for="Comave\League\Api\Data\LeagueSearchResultInterface"
                type="Comave\League\Model\LeagueSearchResults"/>
    <preference for="Comave\League\Api\LeagueRepositoryInterface" type="Comave\League\Model\LeagueRepository"/>
    <preference for="Comave\League\Api\LeagueListRepositoryInterface"
                type="Comave\League\Model\LeagueListRepository"/>
    
    <!-- Season preferences -->
    <preference for="Comave\League\Api\Data\SeasonInterface" type="Comave\League\Model\Season"/>
    <preference for="Comave\League\Api\Data\SeasonSearchResultInterface"
                type="Comave\League\Model\SeasonSearchResults"/>
    <preference for="Comave\League\Api\SeasonRepositoryInterface" type="Comave\League\Model\SeasonRepository"/>
    <preference for="Comave\League\Api\SeasonListRepositoryInterface"
                type="Comave\League\Model\SeasonListRepository"/>
    
    <!-- League Club preferences -->
    <preference for="Comave\League\Api\Data\LeagueClubInterface" type="Comave\League\Model\LeagueClub"/>
    <preference for="Comave\League\Api\Data\LeagueClubSearchResultInterface" type="Comave\League\Model\LeagueClubSearchResults"/>
    <preference for="Comave\League\Api\LeagueClubRepositoryInterface" type="Comave\League\Model\LeagueClubRepository"/>
    <preference for="Comave\League\Api\LeagueClubListRepositoryInterface" type="Comave\League\Model\LeagueClubListRepository"/>
    
    <type name="Magento\Framework\EntityManager\MetadataPool">
        <arguments>
            <argument name="metadata" xsi:type="array">
                <item name="Comave\League\Api\Data\LeagueInterface" xsi:type="array">
                    <item name="entityTableName" xsi:type="string">comave_league</item>
                    <item name="identifierField" xsi:type="string">league_id</item>
                </item>
                <item name="Comave\League\Api\Data\SeasonInterface" xsi:type="array">
                    <item name="entityTableName" xsi:type="string">comave_season</item>
                    <item name="identifierField" xsi:type="string">season_id</item>
                </item>
                <item name="Comave\League\Api\Data\LeagueClubInterface" xsi:type="array">
                    <item name="entityTableName" xsi:type="string">comave_league_club</item>
                    <item name="identifierField" xsi:type="string">league_club_id</item>
                </item>
            </argument>
        </arguments>
    </type>
    <type name="Comave\League\Model\ResourceModel\League">
        <arguments>
            <argument name="interfaceClass" xsi:type="string">
                Comave\League\Api\Data\LeagueInterface
            </argument>
        </arguments>
    </type>
    <type name="Comave\League\Model\ResourceModel\League\Collection">
        <arguments>
            <argument name="mainTable" xsi:type="string">comave_league</argument>
            <argument name="model" xsi:type="string">Comave\League\Model\League</argument>
            <argument name="resourceModel" xsi:type="string">
                Comave\League\Model\ResourceModel\League
            </argument>
            <argument name="idFieldName" xsi:type="string">league_id</argument>
            <argument name="eventPrefix" xsi:type="string">comave_league_collection</argument>
            <argument name="eventObject" xsi:type="string">comave_league_collection</argument>
            <argument name="interfaceClass" xsi:type="string">
                Comave\League\Api\Data\LeagueInterface
            </argument>
        </arguments>
    </type>
    <type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
        <arguments>
            <argument name="collections" xsi:type="array">
                <item name="comave_league_listing_data_source" xsi:type="string">ComaveLeagueGridCollection</item>
                <item name="comave_season_listing_data_source" xsi:type="string">ComaveSeasonGridCollection</item>
                <item name="comave_league_club_listing_data_source" xsi:type="string">ComaveLeagueClubGridCollection</item>
            </argument>
        </arguments>
    </type>
    <virtualType name="ComaveLeagueGridCollection"
                 type="Comave\League\Model\ResourceModel\League\Collection">
        <arguments>
            <argument name="mainTable" xsi:type="string">comave_league</argument>
            <argument name="model" xsi:type="string">Magento\Framework\View\Element\UiComponent\DataProvider\Document</argument>
            <argument name="resourceModel" xsi:type="string">Comave\League\Model\ResourceModel\League</argument>
            <argument name="idFieldName" xsi:type="string">league_id</argument>
            <argument name="eventPrefix" xsi:type="string">comave_league_grid_collection</argument>
            <argument name="eventObject" xsi:type="string">comave_league_grid_collection</argument>
            <argument name="interfaceClass" xsi:type="string">Comave\League\Api\Data\LeagueInterface</argument>
        </arguments>
    </virtualType>
    <virtualType name="ComaveLeagueStatusSource" type="Umc\Crud\Source\Options">
        <arguments>
            <argument name="options" xsi:type="array">
                <item name="0" xsi:type="array">
                    <item name="value" xsi:type="number">1</item>
                    <item name="label" xsi:type="string" translatable="true">Enabled</item>
                </item>
                <item name="1" xsi:type="array">
                    <item name="value" xsi:type="number">0</item>
                    <item name="label" xsi:type="string" translatable="true">Disabled</item>
                </item>
            </argument>
        </arguments>
    </virtualType>
    <virtualType name="ComaveLeagueImageInfo" type="Umc\Crud\Model\FileInfo">
        <arguments>
            <argument name="filePath" xsi:type="string">comave/league/image</argument>
        </arguments>
    </virtualType>
    
    <!-- Season Resource Model and Collection configuration -->
    <type name="Comave\League\Model\ResourceModel\Season">
        <arguments>
            <argument name="interfaceClass" xsi:type="string">
                Comave\League\Api\Data\SeasonInterface
            </argument>
        </arguments>
    </type>
    <type name="Comave\League\Model\ResourceModel\Season\Collection">
        <arguments>
            <argument name="mainTable" xsi:type="string">comave_season</argument>
            <argument name="model" xsi:type="string">Comave\League\Model\Season</argument>
            <argument name="resourceModel" xsi:type="string">Comave\League\Model\ResourceModel\Season</argument>
            <argument name="idFieldName" xsi:type="string">season_id</argument>
            <argument name="eventPrefix" xsi:type="string">comave_season_collection</argument>
            <argument name="eventObject" xsi:type="string">comave_season_collection</argument>
            <argument name="interfaceClass" xsi:type="string">Comave\League\Api\Data\SeasonInterface</argument>
        </arguments>
    </type>
    <virtualType name="ComaveSeasonGridCollection" type="Comave\League\Model\ResourceModel\Season\Collection">
        <arguments>
            <argument name="mainTable" xsi:type="string">comave_season</argument>
            <argument name="model" xsi:type="string">Magento\Framework\View\Element\UiComponent\DataProvider\Document</argument>
            <argument name="resourceModel" xsi:type="string">Comave\League\Model\ResourceModel\Season</argument>
            <argument name="idFieldName" xsi:type="string">season_id</argument>
            <argument name="eventPrefix" xsi:type="string">comave_season_grid_collection</argument>
            <argument name="eventObject" xsi:type="string">comave_season_grid_collection</argument>
            <argument name="interfaceClass" xsi:type="string">Comave\League\Api\Data\SeasonInterface</argument>
        </arguments>
    </virtualType>
    <virtualType name="ComaveSeasonStatusSource" type="Umc\Crud\Source\Options">
        <arguments>
            <argument name="options" xsi:type="array">
                <item name="0" xsi:type="array">
                    <item name="value" xsi:type="number">1</item>
                    <item name="label" xsi:type="string" translatable="true">Enabled</item>
                </item>
                <item name="1" xsi:type="array">
                    <item name="value" xsi:type="number">0</item>
                    <item name="label" xsi:type="string" translatable="true">Disabled</item>
                </item>
            </argument>
        </arguments>
    </virtualType>
    
    <!-- League Club Resource Model and Collection configuration -->
    <type name="Comave\League\Model\ResourceModel\LeagueClub">
        <arguments>
            <argument name="interfaceClass" xsi:type="string">
                Comave\League\Api\Data\LeagueClubInterface
            </argument>
        </arguments>
    </type>
    <type name="Comave\League\Model\ResourceModel\LeagueClub\Collection">
        <arguments>
            <argument name="mainTable" xsi:type="string">comave_league_club</argument>
            <argument name="model" xsi:type="string">Comave\League\Model\LeagueClub</argument>
            <argument name="resourceModel" xsi:type="string">Comave\League\Model\ResourceModel\LeagueClub</argument>
            <argument name="idFieldName" xsi:type="string">league_club_id</argument>
            <argument name="eventPrefix" xsi:type="string">comave_league_club_collection</argument>
            <argument name="eventObject" xsi:type="string">comave_league_club_collection</argument>
            <argument name="interfaceClass" xsi:type="string">Comave\League\Api\Data\LeagueClubInterface</argument>
        </arguments>
    </type>
    <virtualType name="ComaveLeagueClubGridCollection" type="Comave\League\Model\ResourceModel\LeagueClub\Collection">
        <arguments>
            <argument name="mainTable" xsi:type="string">comave_league_club</argument>
            <argument name="model" xsi:type="string">Magento\Framework\View\Element\UiComponent\DataProvider\Document</argument>
            <argument name="resourceModel" xsi:type="string">Comave\League\Model\ResourceModel\LeagueClub</argument>
            <argument name="idFieldName" xsi:type="string">league_club_id</argument>
            <argument name="eventPrefix" xsi:type="string">comave_league_club_grid_collection</argument>
            <argument name="eventObject" xsi:type="string">comave_league_club_grid_collection</argument>
            <argument name="interfaceClass" xsi:type="string">Comave\League\Api\Data\LeagueClubInterface</argument>
        </arguments>
    </virtualType>
    
</config>