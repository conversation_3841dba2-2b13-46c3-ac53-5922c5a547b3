<?xml version="1.0"?>
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">

    <!-- League Table -->
    <table name="comave_league" resource="default" engine="innodb" comment="Comave League">
        <column xsi:type="int" name="league_id" unsigned="true" nullable="false" identity="true" comment="League ID"/>
        <column xsi:type="varchar" name="name" nullable="false" length="255" comment="League Name"/>
        <column xsi:type="varchar" name="code" nullable="false" length="50" comment="League Code"/>
        <column xsi:type="text" name="description" nullable="true" comment="League Description"/>
        <column xsi:type="varchar" name="country_code" nullable="false" length="3" comment="ISO Country Code"/>
        <column xsi:type="varchar" name="logo" nullable="true" length="255" comment="League Logo"/>
        <column xsi:type="smallint" name="status" unsigned="true" nullable="false" default="1" comment="Status"/>
        <column xsi:type="timestamp" name="created_at" on_update="false" nullable="false" default="CURRENT_TIMESTAMP"
                comment="Created At"/>
        <column xsi:type="timestamp" name="updated_at" on_update="true" nullable="false" default="CURRENT_TIMESTAMP"
                comment="Updated At"/>
        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="league_id"/>
        </constraint>
        <constraint xsi:type="unique" referenceId="COMAVE_LEAGUE_CODE">
            <column name="code"/>
        </constraint>
        <index referenceId="COMAVE_LEAGUE_COUNTRY_CODE" indexType="btree">
            <column name="country_code"/>
        </index>
        <index referenceId="COMAVE_LEAGUE_STATUS" indexType="btree">
            <column name="status"/>
        </index>
    </table>

    <!-- League Season Table -->
    <table name="comave_season" resource="default" engine="innodb" comment="Comave Season">
        <column xsi:type="int" name="season_id" unsigned="true" nullable="false" identity="true" comment="Season ID"/>
        <column xsi:type="int" name="league_id" unsigned="true" nullable="false" comment="League ID"/>
        <column xsi:type="varchar" name="name" nullable="false" length="255" comment="Season Name"/>
        <column xsi:type="int" name="max_clubs" unsigned="true" nullable="true" comment="Maximum Clubs"/>
        <column xsi:type="date" name="start_date" nullable="false" comment="Season Start Date"/>
        <column xsi:type="date" name="end_date" nullable="false" comment="Season End Date"/>
        <column xsi:type="smallint" name="is_current" unsigned="true" nullable="false" default="0"
                comment="Is Current Season"/>
        <column xsi:type="timestamp" name="created_at" on_update="false" nullable="false" default="CURRENT_TIMESTAMP"
                comment="Created At"/>
        <column xsi:type="timestamp" name="updated_at" on_update="true" nullable="false" default="CURRENT_TIMESTAMP"
                comment="Updated At"/>
        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="season_id"/>
        </constraint>
        <constraint xsi:type="foreign" referenceId="COMAVE_SEASON_LEAGUE_ID_COMAVE_LEAGUE_LEAGUE_ID"
                    table="comave_season" column="league_id"
                    referenceTable="comave_league" referenceColumn="league_id"
                    onDelete="CASCADE"/>
        <index referenceId="COMAVE_LEAGUE_SEASON_LEAGUE_ID" indexType="btree">
            <column name="league_id"/>
        </index>
        <index referenceId="COMAVE_SEASON_IS_CURRENT" indexType="btree">
            <column name="is_current"/>
        </index>
        <index referenceId="COMAVE_SEASON_MAX_CLUBS" indexType="btree">
            <column name="max_clubs"/>
        </index>
        <index referenceId="COMAVE_SEASON_START_DATE" indexType="btree">
            <column name="start_date"/>
        </index>
        <index referenceId="COMAVE_SEASON_END_DATE" indexType="btree">
            <column name="end_date"/>
        </index>
    </table>

    <!-- League Club Table -->
    <table name="comave_league_club" resource="default" engine="innodb" comment="Comave League Club">
        <column xsi:type="int" name="league_club_id" unsigned="true" nullable="false" identity="true"
                comment="League Club ID"/>
        <column xsi:type="int" name="season_id" unsigned="true" nullable="false" comment="League Season ID"/>
        <column xsi:type="int" name="club_id" unsigned="true" nullable="false" comment="Club ID"/>
        <column xsi:type="int" name="rank" unsigned="true" nullable="false" default="0" comment="League Rank"/>
        <column xsi:type="int" name="points" unsigned="true" nullable="false" default="0" comment="League Points"/>
        <column xsi:type="int" name="matches_played" unsigned="true" nullable="false" default="0"
                comment="Matches Played"/>
        <column xsi:type="int" name="wins" unsigned="true" nullable="false" default="0" comment="Wins"/>
        <column xsi:type="int" name="draws" unsigned="true" nullable="false" default="0" comment="Draws"/>
        <column xsi:type="int" name="losses" unsigned="true" nullable="false" default="0" comment="Losses"/>
        <column xsi:type="int" name="goals_for" unsigned="true" nullable="false" default="0" comment="Goals For"/>
        <column xsi:type="int" name="goals_against" unsigned="true" nullable="false" default="0"
                comment="Goals Against"/>
        <column xsi:type="timestamp" name="created_at" on_update="false" nullable="false" default="CURRENT_TIMESTAMP"
                comment="Created At"/>
        <column xsi:type="timestamp" name="updated_at" on_update="true" nullable="false" default="CURRENT_TIMESTAMP"
                comment="Updated At"/>
        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="league_club_id"/>
        </constraint>
        <constraint xsi:type="unique" referenceId="COMAVE_LEAGUE_CLUB_UNIQUE">
            <column name="season_id"/>
            <column name="club_id"/>
        </constraint>
        <constraint xsi:type="foreign"
                    referenceId="COMAVE_LEAGUE_CLUB_LEAGUE_SEASON_ID_COMAVE_SEASON_SEASON_ID"
                    table="comave_league_club" column="season_id"
                    referenceTable="comave_season" referenceColumn="season_id"
                    onDelete="CASCADE"/>
        <constraint xsi:type="foreign" referenceId="COMAVE_LEAGUE_CLUB_CLUB_ID_COMAVE_CLUB_CLUB_ID"
                    table="comave_league_club" column="club_id"
                    referenceTable="comave_club" referenceColumn="club_id"
                    onDelete="CASCADE"/>
        <index referenceId="COMAVE_LEAGUE_CLUB_SEASON_ID" indexType="btree">
            <column name="season_id"/>
        </index>
        <index referenceId="COMAVE_LEAGUE_CLUB_CLUB_ID" indexType="btree">
            <column name="club_id"/>
        </index>
        <index referenceId="COMAVE_LEAGUE_CLUB_RANK" indexType="btree">
            <column name="rank"/>
        </index>
        <index referenceId="COMAVE_LEAGUE_CLUB_POINTS" indexType="btree">
            <column name="points"/>
        </index>
    </table>
</schema>