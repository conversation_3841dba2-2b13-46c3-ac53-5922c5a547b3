<?php
declare(strict_types=1);

namespace Comave\League\Ui\Component\Listing\Column;

use Magento\Framework\UrlInterface;
use Magento\Framework\View\Element\UiComponent\ContextInterface;
use Magento\Framework\View\Element\UiComponentFactory;
use Magento\Store\Model\StoreManagerInterface;
use Magento\Ui\Component\Listing\Columns\Column;

class Thumbnail extends Column
{
    /**
     * Constructor for Thumbnail column component
     * 
     * Initializes the thumbnail column component with required dependencies
     * for rendering image thumbnails in UI component listings.
     * 
     * @param ContextInterface $context UI component context
     * @param UiComponentFactory $uiComponentFactory Factory for creating UI components
     * @param StoreManagerInterface $storeManager Store manager for URL generation
     * @param array $components Child components array
     * @param array $data Component configuration data
     */
    public function __construct(
        ContextInterface $context,
        UiComponentFactory $uiComponentFactory,
        private readonly StoreManagerInterface $storeManager,
        array $components = [],
        array $data = []
    ) {
        parent::__construct($context, $uiComponentFactory, $components, $data);
    }

    /**
     * Prepare Data Source
     *
     * @param array $dataSource
     * @return array
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function prepareDataSource(array $dataSource): array
    {
        if (isset($dataSource['data']['items'])) {
            $fieldName = $this->getData('name');
            foreach ($dataSource['data']['items'] as &$item) {
                if (!empty($item[$fieldName])) {
                    $item[sprintf("%s_src", $fieldName)] = $this->getImageUrl($item[$fieldName]);
                    $item[sprintf("%s_alt", $fieldName)] = $item['name'];
                    $item[sprintf("%s_orig_src", $fieldName)] = $this->getImageUrl($item[$fieldName]);
                }
            }
        }

        return $dataSource;
    }

    /**
     * Generate full image URL from file path
     * 
     * Constructs a complete URL for an image by combining the store's media base URL
     * with the provided file path. Used for displaying images in the admin grid.
     * 
     * @param string $filePath Relative path to the image file
     * @return string Complete URL to the image
     * @throws \Magento\Framework\Exception\NoSuchEntityException If store cannot be found
     */
    private function getImageUrl(string $filePath): string
    {
        return sprintf(
            "%s%s",
            $this->storeManager->getStore()->getBaseUrl(UrlInterface::URL_TYPE_MEDIA),
            $filePath
        );
    }
}
