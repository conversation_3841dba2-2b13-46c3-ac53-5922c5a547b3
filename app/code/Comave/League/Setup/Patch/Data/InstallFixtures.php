<?php
declare(strict_types=1);

namespace Comave\League\Setup\Patch\Data;

use Comave\Club\Api\Data\ClubInterface;
use Comave\Club\Exception\NotUniquePropertyException;
use Comave\Club\Model\ClubFactory;
use Comave\Club\Model\Repository\ClubRepository;
use Comave\Club\Model\Validator;
use Comave\League\Api\Data\LeagueInterface;
use Comave\League\Model\LeagueClubUiManager;
use Comave\League\Model\LeagueUiManager;
use Comave\League\Model\SeasonUiManager;
use Comave\Marketplace\Model\FixtureManager;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\Setup\Patch\DataPatchInterface;
use \Magento\Framework\Exception\CouldNotSaveException;

class InstallFixtures implements DataPatchInterface
{
    /**
     * @param ModuleDataSetupInterface $moduleDataSetup
     * @param FixtureManager $fixtureManager
     * @param LeagueUiManager $leagueUiManager
     * @param SeasonUiManager $seasonUiManager
     * @param LeagueClubUiManager $leagueClubUiManager
     * @param ClubRepository $clubRepository
     * @param ClubFactory $clubFactory
     * @param Validator $validator
     */
    public function __construct(
        private readonly ModuleDataSetupInterface $moduleDataSetup,
        private readonly FixtureManager $fixtureManager,
        private readonly LeagueUiManager $leagueUiManager,
        private readonly SeasonUiManager $seasonUiManager,
        private readonly LeagueClubUiManager $leagueClubUiManager,
        private readonly ClubRepository $clubRepository,
        private readonly ClubFactory $clubFactory,
        private readonly Validator $validator,
    ) {
    }

    /**
     * @return array|string[]
     */
    public static function getDependencies(): array
    {
        return [];
    }

    /**
     * Get aliases (previous names) for the patch.
     *
     * @return string[]
     */
    public function getAliases(): array
    {
        return [];
    }

    /**
     * @return $this
     */
    public function apply(): self
    {
        $this->moduleDataSetup->startSetup();
        try {
            $this->createLeagues();
            $this->createSeasons();
            $this->manageClubs();
        } catch (NoSuchEntityException|LocalizedException|NotUniquePropertyException $e) {
        }
        $this->moduleDataSetup->endSetup();

        return $this;
    }

    /**
     * @return void
     * @throws LocalizedException
     * @throws NoSuchEntityException
     */
    private function createLeagues(): void
    {
        $this->fixtureManager->setModuleName('Comave_League');
        $leagues = $this->fixtureManager->getData('leagues');

        foreach ($leagues['data'] ?? [] as $league) {
            $model = $this->leagueUiManager->getByCode($league['code']);
            $model->setName($league['name']);
            $model->SetCode($league['code']);
            $model->setDescription($league['description']);
            $model->setCountryCode($league['country_code']);
            $model->setStatus((int)$league['status']);
            $this->leagueUiManager->save($model);
        }
    }

    /**
     * @throws NoSuchEntityException
     * @throws LocalizedException
     */
    private function createSeasons(): void
    {
        $this->fixtureManager->setModuleName('Comave_League');
        $seasons = $this->fixtureManager->getData('seasons');
        foreach ($seasons['data'] ?? [] as $season) {
            $league = $this->leagueUiManager->getByCode($season['league_code']);
            if (($leagueId = $league->getLeagueId())) {
                $model = $this->seasonUiManager->getByLeagueId($leagueId);
                $model->setLeagueId($leagueId);
                $model->setName($season['name']);
                $model->setMaxClubs((int)$season['max_clubs']);
                $model->setStartDate($season['start_date']);
                $model->setEndDate($season['end_date']);
                $model->setIsCurrent((int)$season['is_current']);
                $this->seasonUiManager->save($model);
            }
        }
    }

    /**
     * @return void
     * @throws CouldNotSaveException
     * @throws LocalizedException
     * @throws NoSuchEntityException
     */
    private function manageClubs(): void
    {
        $this->fixtureManager->setModuleName('Comave_League');
        $clubGroup = $this->getLastInsertedIdClubGroup();
        $clubs = $this->fixtureManager->getData('clubs');
        foreach ($clubs ?? [] as $leagueCode => $club) {
            $league = $this->leagueUiManager->getByCode($leagueCode);
            foreach ($club as $clubData) {
                $clubData['group_id'] = $clubGroup;
                try {
                    $model = $this->clubRepository->getByUniqueId($clubData['uniqueid']);
                } catch (NoSuchEntityException $e) {
                    $model = $this->clubFactory->create();
                }
                $model = $this->populateClubData($model, $clubData);
                $this->clubRepository->save($model);
                try {
                    $this->assignLeagueClub($league, $model);
                } catch (NoSuchEntityException|LocalizedException $e) {
                }
            }
        }
    }

    /**
     * @return string
     */
    private function getLastInsertedIdClubGroup(): string
    {
        $select = $this->moduleDataSetup->getConnection()->select();
        $select->from(
            $this->moduleDataSetup->getConnection()->getTableName('comave_club_group')
        )->order('group_id DESC');

        return $this->moduleDataSetup->getConnection()->fetchOne($select);
    }

    /**
     * @param \Comave\Club\Api\Data\ClubInterface $model
     * @param array $clubData
     * @return \Comave\Club\Api\Data\ClubInterface
     */
    private function populateClubData(ClubInterface $model, array $clubData): ClubInterface
    {
        if ($model->getId()) {
            foreach ($clubData as $field => $value) {
                if ($model->getData($field) !== $value) {
                    $model->setData($field, $value);
                }
            }
        } else {
            $model->setData($clubData);
        }

        return $model;
    }

    /**
     * @param LeagueInterface $league
     * @param ClubInterface $club
     * @return void
     * @throws LocalizedException
     * @throws NoSuchEntityException
     */
    private function assignLeagueClub(LeagueInterface $league, ClubInterface $club): void
    {
        $season = $this->seasonUiManager->getByLeagueId($league->getId());
        if ($season->getId()) {
            $leagueClub = $this->leagueClubUiManager->getBySeasonClub((int)$season->getId(), (int)$club->getId());
            if (!$leagueClub->getId()) {
                $leagueClub->setSeasonId((int)$season->getId());
                $leagueClub->setClubId((int)$club->getId());
                $leagueClub->setRank(1);
                $this->leagueClubUiManager->save($leagueClub);
            }
        }
    }
}
