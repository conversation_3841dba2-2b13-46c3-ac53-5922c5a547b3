<?php

declare(strict_types=1);

namespace Comave\Catalog\Controller\Adminhtml\Category\Image;

use Comave\Catalog\Model\Category\Image\OrientationValidator;
use Magento\Backend\App\Action;
use Magento\Backend\App\Action\Context;
use Magento\Catalog\Model\ImageUploader;
use Magento\Catalog\Model\ImageUploaderFactory;
use Magento\Framework\App\Action\HttpPostActionInterface;
use Magento\Framework\Controller\ResultFactory;
use Magento\Framework\Controller\ResultInterface;
use Magento\Framework\Exception\LocalizedException;
use Psr\Log\LoggerInterface;

/**
 * Unified controller for category orientation image uploads
 */
class Upload extends Action implements HttpPostActionInterface
{
    /**
     * Authorization level of a basic admin session
     */
    public const ADMIN_RESOURCE = 'Magento_Catalog::categories';

    /**
     * Request parameter names
     */
    public const PARAM_NAME = 'param_name';
    public const DEFAULT_IMAGE_ID = 'image';

    /**
     * Path templates
     */
    public const BASE_TMP_PATH_TEMPLATE = 'catalog/category/%s';
    public const BASE_PATH_TEMPLATE = 'catalog/category/%s';

    /**
     * Allowed file extensions
     */
    public const ALLOWED_EXTENSIONS = ['gif', 'jpg', 'png', 'jpeg', 'webp', 'avif', 'jfif', 'svg'];

    /**
     * Allowed MIME types
     */
    public const ALLOWED_MIME_TYPES = [
        'image/jpg',
        'image/jpeg',
        'image/gif',
        'image/png',
        'image/webp',
        'image/avif',
        'image/svg+xml'
    ];

    /**
     * @param Context $context
     * @param ImageUploaderFactory $imageUploaderFactory
     * @param OrientationValidator $orientationValidator
     * @param LoggerInterface $logger
     */
    public function __construct(
        Context $context,
        private readonly ImageUploaderFactory $imageUploaderFactory,
        private readonly OrientationValidator $orientationValidator,
        private readonly LoggerInterface $logger
    ) {
        parent::__construct($context);
    }

    /**
     * Upload file controller action
     *
     * @return ResultInterface
     */
    public function execute(): ResultInterface
    {
        $imageId = $this->_request->getParam(self::PARAM_NAME, self::DEFAULT_IMAGE_ID);
        $orientation = $this->orientationValidator->extractOrientation($imageId);

        try {
            $this->orientationValidator->validateOrientation($orientation);

            $imageUploader = $this->createImageUploader($orientation);
            $result = $imageUploader->saveFileToTmpDir($imageId);

            $result['cookie'] = [
                'name' => $this->_getSession()->getName(),
                'value' => $this->_getSession()->getSessionId(),
                'lifetime' => $this->_getSession()->getCookieLifetime(),
                'path' => $this->_getSession()->getCookiePath(),
                'domain' => $this->_getSession()->getCookieDomain(),
            ];
        } catch (\Exception $e) {
            $this->logger->error('Category orientation image upload failed', [
                'exception' => $e->getMessage(),
                'orientation' => $orientation,
                'image_id' => $imageId,
                'trace' => $e->getTraceAsString()
            ]);
            $result = ['error' => $e->getMessage(), 'errorcode' => $e->getCode()];
        }

        return $this->resultFactory->create(ResultFactory::TYPE_JSON)->setData($result);
    }

    /**
     * Create image uploader for specific orientation
     *
     * @param string $orientation
     * @return ImageUploader
     */
    private function createImageUploader(string $orientation): ImageUploader
    {
        $this->orientationValidator->validateOrientation($orientation);

        $baseTmpPath = sprintf(self::BASE_TMP_PATH_TEMPLATE, $orientation);
        $basePath = sprintf(self::BASE_PATH_TEMPLATE, $orientation);

        return $this->imageUploaderFactory->create([
            'baseTmpPath' => $baseTmpPath,
            'basePath' => $basePath,
            'allowedExtensions' => self::ALLOWED_EXTENSIONS,
            'allowedMimeTypes' => self::ALLOWED_MIME_TYPES
        ]);
    }
}
