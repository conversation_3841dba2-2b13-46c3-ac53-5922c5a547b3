<?php

declare(strict_types=1);

/**
 * Copyright © Commercial Avenue
 */

namespace Comave\Catalog\Setup\Patch\Data;

use Comave\Catalog\Model\Category\Attribute\Backend\OrientationImage;
use Magento\Catalog\Model\Category;
use Magento\Eav\Model\Entity\Attribute\ScopedAttributeInterface;
use Magento\Eav\Setup\EavSetup;
use Magento\Eav\Setup\EavSetupFactory;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\Setup\Patch\DataPatchInterface;
use Psr\Log\LoggerInterface;

/**
 * Create category horizontal and vertical image attributes
 */
class CreateCategoryOrientationImageFields implements DataPatchInterface
{
    /**
     * Attribute names
     */
    public const HORIZONTAL_IMAGE_ATTRIBUTE = 'horizontal_image';
    public const VERTICAL_IMAGE_ATTRIBUTE = 'vertical_image';

    /**
     * Attribute labels
     */
    public const HORIZONTAL_IMAGE_LABEL = 'Horizontal Image';
    public const VERTICAL_IMAGE_LABEL = 'Vertical Image';

    /**
     * Attribute configuration
     */
    public const ATTRIBUTE_TYPE = 'varchar';
    public const ATTRIBUTE_INPUT = 'image';
    public const ATTRIBUTE_GROUP = 'Content';
    public const HORIZONTAL_SORT_ORDER = 41;
    public const VERTICAL_SORT_ORDER = 42;

    /**
     * @param ModuleDataSetupInterface $moduleDataSetup
     * @param EavSetupFactory $eavSetupFactory
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly ModuleDataSetupInterface $moduleDataSetup,
        private readonly EavSetupFactory $eavSetupFactory,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * @inheritdoc
     */
    public function apply()
    {
        $this->moduleDataSetup->getConnection()->startSetup();

        /** @var EavSetup $eavSetup */
        $eavSetup = $this->eavSetupFactory->create(['setup' => $this->moduleDataSetup]);

        $this->createHorizontalImageAttribute($eavSetup);
        $this->createVerticalImageAttribute($eavSetup);

        $this->moduleDataSetup->getConnection()->endSetup();
    }

    /**
     * Create horizontal image attribute
     *
     * @param EavSetup $eavSetup
     * @throws \Exception
     */
    private function createHorizontalImageAttribute(EavSetup $eavSetup): void
    {
        if ($eavSetup->getAttributeId(Category::ENTITY, self::HORIZONTAL_IMAGE_ATTRIBUTE)) {
            $this->logger->info('Horizontal image attribute already exists, skipping creation');
            return;
        }

        try {
            $eavSetup->addAttribute(
                Category::ENTITY,
                self::HORIZONTAL_IMAGE_ATTRIBUTE,
                [
                    'type' => self::ATTRIBUTE_TYPE,
                    'label' => self::HORIZONTAL_IMAGE_LABEL,
                    'input' => self::ATTRIBUTE_INPUT,
                    'backend' => OrientationImage::class,
                    'required' => false,
                    'sort_order' => self::HORIZONTAL_SORT_ORDER,
                    'global' => ScopedAttributeInterface::SCOPE_STORE,
                    'group' => self::ATTRIBUTE_GROUP,
                    'used_in_product_listing' => false,
                    'visible_on_front' => false,
                    'user_defined' => false,
                    'default' => '',
                ]
            );

            $this->logger->info('Successfully created horizontal image attribute');
        } catch (\Exception $e) {
            $this->logger->error('Failed to create horizontal image attribute', [
                'exception' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * Create vertical image attribute
     *
     * @param EavSetup $eavSetup
     * @throws \Exception
     */
    private function createVerticalImageAttribute(EavSetup $eavSetup): void
    {
        if ($eavSetup->getAttributeId(Category::ENTITY, self::VERTICAL_IMAGE_ATTRIBUTE)) {
            $this->logger->info('Vertical image attribute already exists, skipping creation');
            return;
        }

        try {
            $eavSetup->addAttribute(
                Category::ENTITY,
                self::VERTICAL_IMAGE_ATTRIBUTE,
                [
                    'type' => self::ATTRIBUTE_TYPE,
                    'label' => self::VERTICAL_IMAGE_LABEL,
                    'input' => self::ATTRIBUTE_INPUT,
                    'backend' => OrientationImage::class,
                    'required' => false,
                    'sort_order' => self::VERTICAL_SORT_ORDER,
                    'global' => ScopedAttributeInterface::SCOPE_STORE,
                    'group' => self::ATTRIBUTE_GROUP,
                    'used_in_product_listing' => false,
                    'visible_on_front' => false,
                    'user_defined' => false,
                    'default' => '',
                ]
            );

            $this->logger->info('Successfully created vertical image attribute');
        } catch (\Exception $e) {
            $this->logger->error('Failed to create vertical image attribute', [
                'exception' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * @inheritdoc
     */
    public static function getDependencies()
    {
        return [];
    }

    /**
     * @inheritdoc
     */
    public function getAliases()
    {
        return [];
    }
}
