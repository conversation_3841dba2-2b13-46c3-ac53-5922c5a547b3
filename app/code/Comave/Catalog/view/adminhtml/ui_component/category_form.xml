<?xml version="1.0" encoding="UTF-8"?>
<form xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <fieldset name="content" sortOrder="10">
        <field name="horizontal_image" sortOrder="41" formElement="imageUploader">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="source" xsi:type="string">category</item>
                </item>
            </argument>
            <settings>
                <elementTmpl>ui/form/element/uploader/image</elementTmpl>
                <dataType>string</dataType>
                <label translate="true">Horizontal Image</label>
                <visible>true</visible>
                <required>false</required>
            </settings>
            <formElements>
                <imageUploader>
                    <settings>
                        <required>false</required>
                        <uploaderConfig>
                            <param xsi:type="url" name="url" path="comave_catalog/category_image/upload"/>
                        </uploaderConfig>
                        <previewTmpl>Magento_Catalog/image-preview</previewTmpl>
                        <openDialogTitle>Media Gallery</openDialogTitle>
                        <initialMediaGalleryOpenSubpath>catalog/category</initialMediaGalleryOpenSubpath>
                        <allowedExtensions>jpg jpeg gif png webp avif jfif svg</allowedExtensions>
                        <maxFileSize>4194304</maxFileSize>
                    </settings>
                </imageUploader>
            </formElements>
        </field>
        <field name="vertical_image" sortOrder="42" formElement="imageUploader">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="source" xsi:type="string">category</item>
                </item>
            </argument>
            <settings>
                <elementTmpl>ui/form/element/uploader/image</elementTmpl>
                <dataType>string</dataType>
                <label translate="true">Vertical Image</label>
                <visible>true</visible>
                <required>false</required>
            </settings>
            <formElements>
                <imageUploader>
                    <settings>
                        <required>false</required>
                        <uploaderConfig>
                            <param xsi:type="url" name="url" path="comave_catalog/category_image/upload"/>
                        </uploaderConfig>
                        <previewTmpl>Magento_Catalog/image-preview</previewTmpl>
                        <openDialogTitle>Media Gallery</openDialogTitle>
                        <initialMediaGalleryOpenSubpath>catalog/category</initialMediaGalleryOpenSubpath>
                        <allowedExtensions>jpg jpeg gif png webp avif jfif svg</allowedExtensions>
                        <maxFileSize>4194304</maxFileSize>
                    </settings>
                </imageUploader>
            </formElements>
        </field>
    </fieldset>
</form>
