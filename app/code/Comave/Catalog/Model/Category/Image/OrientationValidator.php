<?php

declare(strict_types=1);

namespace Comave\Catalog\Model\Category\Image;

/**
 * Validator for category orientation image operations
 */
class OrientationValidator
{
    /**
     * Supported orientation types
     */
    public const SUPPORTED_ORIENTATIONS = ['horizontal', 'vertical'];

    /**
     * Default orientation fallback
     */
    public const DEFAULT_ORIENTATION = 'horizontal';

    /**
     * Extract orientation from image parameter name
     *
     * @param string $imageId
     * @return string
     */
    public function extractOrientation(string $imageId): string
    {
        foreach (self::SUPPORTED_ORIENTATIONS as $orientation) {
            if (strpos($imageId, $orientation) !== false) {
                return $orientation;
            }
        }
        
        return self::DEFAULT_ORIENTATION;
    }

    /**
     * Check if orientation is valid
     *
     * @param string $orientation
     * @return bool
     */
    public function isValidOrientation(string $orientation): bool
    {
        return in_array($orientation, self::SUPPORTED_ORIENTATIONS, true);
    }

    /**
     * Validate orientation and throw exception if invalid
     *
     * @param string $orientation
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function validateOrientation(string $orientation): void
    {
        if (!$this->isValidOrientation($orientation)) {
            throw new \Magento\Framework\Exception\LocalizedException(
                __('Invalid orientation type: %1', $orientation)
            );
        }
    }
}
