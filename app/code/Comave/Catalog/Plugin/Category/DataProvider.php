<?php

declare(strict_types=1);

namespace Comave\Catalog\Plugin\Category;

use Magento\Catalog\Model\Category\DataProvider as CategoryDataProvider;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\Filesystem;
use Magento\Framework\UrlInterface;
use Magento\Store\Model\StoreManagerInterface;
use Psr\Log\LoggerInterface;

/**
 * Plugin to convert custom orientation image attributes for UI components
 */
class DataProvider
{
    /**
     * Orientation image attributes that need UI component conversion
     */
    public const ORIENTATION_IMAGE_ATTRIBUTES = ['horizontal_image', 'vertical_image'];

    /**
     * MIME type mappings for common image extensions
     */
    public const MIME_TYPES = [
        'jpg' => 'image/jpeg',
        'jpeg' => 'image/jpeg',
        'png' => 'image/png',
        'gif' => 'image/gif',
        'webp' => 'image/webp',
        'svg' => 'image/svg+xml',
        'avif' => 'image/avif'
    ];

    /**
     * @param Filesystem $filesystem
     * @param StoreManagerInterface $storeManager
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly Filesystem $filesystem,
        private readonly StoreManagerInterface $storeManager,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * Convert orientation image attributes to UI component format
     *
     * @param CategoryDataProvider $subject
     * @param array $result
     * @return array
     */
    public function afterGetData(CategoryDataProvider $subject, array $result): array
    {
        foreach ($result as $categoryId => &$categoryData) {
            if (!$this->isValidCategoryData($categoryData)) {
                continue;
            }

            foreach (self::ORIENTATION_IMAGE_ATTRIBUTES as $attributeCode) {
                if (!$this->hasImageData($categoryData, $attributeCode)) {
                    continue;
                }
                $convertedData = $this->convertImageToUiFormat($categoryData[$attributeCode]);
                if ($convertedData) {
                    $categoryData[$attributeCode] = $convertedData;
                }
            }
        }

        return $result;
    }

    /**
     * Check if category data is valid for processing
     *
     * @param mixed $categoryData
     * @return bool
     */
    private function isValidCategoryData($categoryData): bool
    {
        return is_array($categoryData) && isset($categoryData['entity_id']);
    }

    /**
     * Check if category has valid image data for attribute
     *
     * @param array $categoryData
     * @param string $attributeCode
     * @return bool
     */
    private function hasImageData(array $categoryData, string $attributeCode): bool
    {
        return isset($categoryData[$attributeCode]) &&
               is_string($categoryData[$attributeCode]) &&
               !empty($categoryData[$attributeCode]);
    }

    /**
     * Convert image path to UI component format
     *
     * @param string $imagePath
     * @return array|null
     */
    private function convertImageToUiFormat(string $imagePath): ?array
    {
        try {
            [$relativePath, $imageUrl] = $this->resolveImagePaths($imagePath);

            if (!$this->isImageFileExists($relativePath)) {
                return null;
            }

            $fileInfo = $this->getImageFileInfo($relativePath);

            return [
                [
                    'name' => basename($relativePath),
                    'url' => $imageUrl,
                    'size' => $fileInfo['size'],
                    'type' => $fileInfo['mime_type']
                ]
            ];
        } catch (\Exception $e) {
            $this->logger->error('Failed to convert image to UI format', [
                'exception' => $e->getMessage(),
                'image_path' => $imagePath,
                'trace' => $e->getTraceAsString()
            ]);
            return null;
        }
    }

    /**
     * Resolve relative and absolute paths for image
     *
     * @param string $imagePath
     * @return array [relativePath, imageUrl]
     */
    private function resolveImagePaths(string $imagePath): array
    {
        $baseUrl = $this->storeManager->getStore()->getBaseUrl(UrlInterface::URL_TYPE_MEDIA);

        if (strpos($imagePath, 'http') === 0) {
            $relativePath = str_replace($baseUrl, '', $imagePath);
            $imageUrl = $imagePath;
        } else {
            $relativePath = $imagePath;
            $imageUrl = $baseUrl . $relativePath;
        }

        return [$relativePath, $imageUrl];
    }

    /**
     * Check if image file exists
     *
     * @param string $relativePath
     * @return bool
     */
    private function isImageFileExists(string $relativePath): bool
    {
        $mediaDirectory = $this->filesystem->getDirectoryRead(DirectoryList::MEDIA);
        $exists = $mediaDirectory->isExist($relativePath);

        return $exists;
    }

    /**
     * Get image file information
     *
     * @param string $relativePath
     * @return array
     */
    private function getImageFileInfo(string $relativePath): array
    {
        $mediaDirectory = $this->filesystem->getDirectoryRead(DirectoryList::MEDIA);
        $stat = $mediaDirectory->stat($relativePath);
        $extension = strtolower(pathinfo($relativePath, PATHINFO_EXTENSION));

        return [
            'size' => $stat['size'] ?? 0,
            'mime_type' => self::MIME_TYPES[$extension] ?? 'image/jpeg'
        ];
    }
}
