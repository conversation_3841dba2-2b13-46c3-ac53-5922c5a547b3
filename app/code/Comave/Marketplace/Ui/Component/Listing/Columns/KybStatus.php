<?php
/**
 * Comave
 *
 * @category  Comave
 * @package   Comave_Marketplace
 * <AUTHOR>
 */
namespace Comave\Marketplace\Ui\Component\Listing\Columns;

use Magento\Framework\View\Element\UiComponent\ContextInterface;
use Magento\Framework\View\Element\UiComponentFactory;
use Magento\Ui\Component\Listing\Columns\Column;
use Comave\Marketplace\Model\Seller\Source\KybStatus as KybStatusSource;

class KybStatus extends Column
{
    /**
     * @var KybStatusSource
     */
    protected $kybStatusSource;

    /**
     * @param ContextInterface $context
     * @param UiComponentFactory $uiComponentFactory
     * @param KybStatusSource $kybStatusSource
     * @param array $components
     * @param array $data
     */
    public function __construct(
        ContextInterface $context,
        UiComponentFactory $uiComponentFactory,
        KybStatusSource $kybStatusSource,
        array $components = [],
        array $data = []
    ) {
        $this->kybStatusSource = $kybStatusSource;
        parent::__construct($context, $uiComponentFactory, $components, $data);
    }

    /**
     * Prepare Data Source
     *
     * @param array $dataSource
     * @return array
     */
    public function prepareDataSource(array $dataSource)
    {
        if (isset($dataSource['data']['items'])) {
            $fieldName = $this->getData('name');
            $options = $this->kybStatusSource->toOptionArray();
            $optionsArray = [];
            foreach ($options as $option) {
                if (isset($option['value'])) {
                    $optionsArray[$option['value']] = $option['label'];
                }
            }
            foreach ($dataSource['data']['items'] as & $item) {
                if (isset($item[$fieldName]) && isset($optionsArray[$item[$fieldName]])) {
                    $item[$fieldName] = $optionsArray[$item[$fieldName]];
                }
            }
        }

        return $dataSource;
    }
}
