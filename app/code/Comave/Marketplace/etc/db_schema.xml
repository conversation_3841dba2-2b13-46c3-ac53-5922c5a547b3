<?xml version="1.0"?>
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
    <table name="catalog_eav_attribute">
        <column name="is_seller_editable"
                xsi:type="boolean"
                nullable="false"
                default="0"
                comment="Is Seller Editable"/>
    </table>
    <table name="marketplace_userdata" resource="default" engine="innodb" comment="Marketplace User Data Extended">
        <column xsi:type="varchar" 
                name="kyb_status" 
                length="20" 
                nullable="false" 
                default="pending" 
                comment="KYB Status (pending, approved, rejected, processing)"/>
    </table>
</schema>
