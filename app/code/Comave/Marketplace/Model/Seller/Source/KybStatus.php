<?php

declare(strict_types=1);

namespace Comave\Marketplace\Model\Seller\Source;

use Magento\Framework\Data\OptionSourceInterface;

/**
 * Class KybStatus
 */
class KybStatus implements OptionSourceInterface
{
    /**
     * @var array
     */
    protected array $_options = [];

    /**
     * Get options
     *
     * @return array
     */
    public function toOptionArray(): array
    {
        if (!$this->_options) {
            $this->_options = [
                ['value' => '', 'label' => __('-- Please Select --')],
                ['value' => 'pending', 'label' => __('Pending')],
                ['value' => 'verified', 'label' => __('Verified')],
                ['value' => 'failed', 'label' => __('Failed')],
            ];
        }
        return $this->_options;
    }
}
