<?php

declare(strict_types=1);

namespace Comave\CatalogGraphQl\Model\Service;

use Magento\Catalog\Api\CategoryRepositoryInterface;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Filesystem;
use Magento\Framework\UrlInterface;
use Magento\Store\Model\StoreManagerInterface;
use Psr\Log\LoggerInterface;

/**
 * Service for handling category orientation image uploads via GraphQL
 */
class CategoryImageUploadService
{
    /**
     * Orientation mappings
     */
    public const ORIENTATION_MAPPING = [
        'HORIZONTAL' => 'horizontal',
        'VERTICAL' => 'vertical'
    ];

    /**
     * @param CategoryRepositoryInterface $categoryRepository
     * @param Filesystem $filesystem
     * @param StoreManagerInterface $storeManager
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly CategoryRepositoryInterface $categoryRepository,
        private readonly Filesystem $filesystem,
        private readonly StoreManagerInterface $storeManager,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * Upload category orientation image
     *
     * @param array $input
     * @return array
     * @throws LocalizedException
     */
    public function uploadCategoryImage(array $input): array
    {
        $categoryId = (int) $input['category_id'];
        $orientation = strtolower(self::ORIENTATION_MAPPING[$input['orientation']] ?? 'horizontal');
        $attributeName = $orientation . '_image';

        try {
            $category = $this->categoryRepository->get($categoryId, 0);
            $category->setStoreId(0);

            $uploadResult = $this->processBase64Upload($input, $orientation);

            $imageData = [
                [
                    'file' => $uploadResult['file_path'],
                    'url' => $uploadResult['tmp_url'],
                    'name' => $uploadResult['name'],
                    'type' => $input['type']
                ]
            ];

            $category->unsetData($attributeName);
            $category->setData($attributeName, $imageData);
            $category->setStoreId(0);

            $savedCategory = $this->categoryRepository->save($category);
            $reloadedCategory = $this->categoryRepository->get($categoryId, 0);
            $savedImagePath = $reloadedCategory->getData($attributeName);

        } catch (\Exception $e) {
            $this->logger->error('Category orientation image GraphQL upload failed', [
                'exception' => $e->getMessage(),
                'input' => $input,
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => $e->getMessage(),
                'image_url' => null,
                'file_path' => null
            ];
        }

        $finalImageUrl = $this->buildFinalImageUrl($savedImagePath);

        return [
            'success' => true,
            'message' => 'Image uploaded successfully',
            'image_url' => $finalImageUrl,
            'file_path' => $savedImagePath
        ];
    }

    /**
     * Process base64 upload to temporary directory
     * This mimics the UI component upload process that the backend model expects
     *
     * @param array $input
     * @param string $orientation
     * @return array
     * @throws LocalizedException
     */
    private function processBase64Upload(array $input, string $orientation): array
    {
        $imageData = base64_decode($input['base64_encoded_file']);
        if ($imageData === false) {
            throw new LocalizedException(__('Invalid base64 image data'));
        }

        $fileExtension = pathinfo($input['name'], PATHINFO_EXTENSION);
        $baseName = pathinfo($input['name'], PATHINFO_FILENAME);
        $uniqueId = uniqid() . '_' . microtime(true);
        $fileName = $uniqueId . '_' . $baseName . '.' . $fileExtension;

        try {
            $mediaDirectory = $this->filesystem->getDirectoryWrite(DirectoryList::MEDIA);
            $tmpPath = "tmp/catalog/category/{$orientation}";

            if (!$mediaDirectory->isExist($tmpPath)) {
                $mediaDirectory->create($tmpPath);
            }

            $filePath = $tmpPath . '/' . $fileName;

            if ($mediaDirectory->isExist($filePath)) {
                $mediaDirectory->delete($filePath);
            }

            $mediaDirectory->writeFile($filePath, $imageData);

        } catch (\Exception $e) {
            $this->logger->error('Failed to process base64 upload', [
                'exception' => $e->getMessage(),
                'orientation' => $orientation,
                'filename' => $input['name']
            ]);
            throw $e;
        }

        $mediaUrl = $this->storeManager->getStore()->getBaseUrl(UrlInterface::URL_TYPE_MEDIA);
        $fileUrl = $mediaUrl . $filePath;

        return [
            'file_path' => '/' . $fileName,
            'tmp_url' => $fileUrl,
            'name' => $input['name']
        ];
    }

    /**
     * Build final image URL from processed category data
     *
     * @param mixed $imageData
     * @return string|null
     */
    private function buildFinalImageUrl($imageData): ?string
    {
        if (is_string($imageData) && !empty($imageData)) {
            $mediaUrl = $this->storeManager->getStore()->getBaseUrl(UrlInterface::URL_TYPE_MEDIA);
            return $mediaUrl . ltrim($imageData, '/');
        }

        if (is_array($imageData) && isset($imageData[0]['url'])) {
            return $imageData[0]['url'];
        }

        return null;
    }
}
