<?php

declare(strict_types=1);

namespace Comave\CatalogGraphQl\Model\Service;

use Magento\Catalog\Api\CategoryRepositoryInterface;
use Magento\Catalog\Model\Category;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\UrlInterface;
use Magento\Store\Model\StoreManagerInterface;
use Psr\Log\LoggerInterface;

/**
 * Service for handling category orientation images in GraphQL context
 */
class CategoryImageService
{
    /**
     * URL detection
     */
    public const HTTP_PROTOCOL_PREFIX = 'http';

    /**
     * @param CategoryRepositoryInterface $categoryRepository
     * @param StoreManagerInterface $storeManager
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly CategoryRepositoryInterface $categoryRepository,
        private readonly StoreManagerInterface $storeManager,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * Get category orientation image URL
     *
     * @param Category $category
     * @param string $attributeName
     * @return string|null
     * @throws LocalizedException
     */
    public function getCategoryImageUrl(Category $category, string $attributeName): ?string
    {
        $categoryId = $category->getId();
        
        $adminCategory = $this->categoryRepository->get($categoryId, 0);
        $imageValue = $adminCategory->getData($attributeName);

        if (!$imageValue) {
            return null;
        }

        $imagePath = $this->extractImagePath($imageValue);

        if (!$imagePath) {
            return null;
        }

        return $this->buildImageUrl($imagePath);
    }

    /**
     * Build full image URL from relative path
     *
     * @param string $imagePath
     * @return string|null
     */
    private function buildImageUrl(string $imagePath): ?string
    {
        if (strpos($imagePath, self::HTTP_PROTOCOL_PREFIX) === 0) {
            return $imagePath;
        }

        try {
            $mediaUrl = $this->storeManager->getStore()->getBaseUrl(UrlInterface::URL_TYPE_MEDIA);
            return $mediaUrl . ltrim($imagePath, '/');
        } catch (\Exception $e) {
            $this->logger->error('Failed to build image URL for GraphQL', [
                'exception' => $e->getMessage(),
                'image_path' => $imagePath,
                'trace' => $e->getTraceAsString()
            ]);
            return null;
        }
    }

    /**
     * Extract image path from value (handles both string and array formats)
     *
     * @param mixed $imageValue
     * @return string|null
     */
    private function extractImagePath($imageValue): ?string
    {
        if (is_string($imageValue)) {
            return $imageValue;
        }

        if (is_array($imageValue) && isset($imageValue[0])) {
            $firstItem = $imageValue[0];

            if (isset($firstItem['file'])) {
                return $firstItem['file'];
            }

            if (isset($firstItem['url'])) {
                $url = $firstItem['url'];
                $mediaUrl = $this->storeManager->getStore()->getBaseUrl(UrlInterface::URL_TYPE_MEDIA);
                $relativePath = str_replace($mediaUrl, '', $url);
                return ltrim($relativePath, '/');
            }

            if (isset($firstItem['name'])) {
                return $firstItem['name'];
            }
        }

        return null;
    }
}
