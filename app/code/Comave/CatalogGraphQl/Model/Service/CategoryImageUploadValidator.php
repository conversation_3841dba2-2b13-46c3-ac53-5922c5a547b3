<?php

declare(strict_types=1);

namespace Comave\CatalogGraphQl\Model\Service;

use Magento\Framework\GraphQl\Exception\GraphQlInputException;

/**
 * Validator for category orientation image upload inputs
 */
class CategoryImageUploadValidator
{
    /**
     * Allowed file extensions
     */
    public const ALLOWED_EXTENSIONS = ['gif', 'jpg', 'png', 'jpeg', 'webp', 'avif', 'jfif', 'svg'];

    /**
     * Orientation mappings
     */
    public const ORIENTATION_MAPPING = [
        'HORIZONTAL' => 'horizontal',
        'VERTICAL' => 'vertical'
    ];

    /**
     * Validate input parameters
     *
     * @param array $input
     * @throws GraphQlInputException
     */
    public function validateInput(array $input): void
    {
        $requiredFields = ['category_id', 'orientation', 'name', 'base64_encoded_file', 'type'];

        foreach ($requiredFields as $field) {
            if (empty($input[$field])) {
                throw new GraphQlInputException(__('Field "%1" is required.', $field));
            }
        }

        if (!is_numeric($input['category_id']) || (int)$input['category_id'] <= 0) {
            throw new GraphQlInputException(__('Category ID must be a positive integer.'));
        }

        if (!isset(self::ORIENTATION_MAPPING[$input['orientation']])) {
            throw new GraphQlInputException(__('Invalid orientation. Must be HORIZONTAL or VERTICAL.'));
        }

        $extension = strtolower(pathinfo($input['name'], PATHINFO_EXTENSION));
        if (!in_array($extension, self::ALLOWED_EXTENSIONS, true)) {
            throw new GraphQlInputException(__(
                'Invalid file extension. Allowed: %1',
                implode(', ', self::ALLOWED_EXTENSIONS)
            ));
        }
    }
}
