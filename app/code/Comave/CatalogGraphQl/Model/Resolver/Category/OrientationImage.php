<?php

declare(strict_types=1);

namespace Comave\CatalogGraphQl\Model\Resolver\Category;

use Comave\CatalogGraphQl\Model\Service\CategoryImageService;
use Magento\Catalog\Model\Category;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;

/**
 * Unified resolver for category orientation images (horizontal/vertical)
 */
class OrientationImage implements ResolverInterface
{
    /**
     * @param CategoryImageService $categoryImageService
     */
    public function __construct(
        private readonly CategoryImageService $categoryImageService
    ) {
    }

    /**
     * Resolve orientation image URL
     *
     * @param Field $field
     * @param mixed $context
     * @param ResolveInfo $info
     * @param array|null $value
     * @param array|null $args
     * @return string|null
     * @throws LocalizedException
     */
    public function resolve(Field $field, $context, ResolveInfo $info, array $value = null, array $args = null): ?string
    {
        if (!isset($value['model']) || !$value['model'] instanceof Category) {
            throw new LocalizedException(__('"model" value should be specified'));
        }

        /** @var Category $category */
        $category = $value['model'];
        $attributeName = $field->getName();

        return $this->categoryImageService->getCategoryImageUrl($category, $attributeName);
    }

}
