<?php

declare(strict_types=1);

namespace Comave\CatalogGraphQl\Model\Resolver\Category;

use Comave\CatalogGraphQl\Model\Service\CategoryImageUploadService;
use Comave\CatalogGraphQl\Model\Service\CategoryImageUploadValidator;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;

/**
 * GraphQL resolver for uploading category orientation images via base64
 */
class UploadOrientationImage implements ResolverInterface
{
    /**
     * @param CategoryImageUploadValidator $validator
     * @param CategoryImageUploadService $uploadService
     */
    public function __construct(
        private readonly CategoryImageUploadValidator $validator,
        private readonly CategoryImageUploadService $uploadService
    ) {
    }

    /**
     * Upload category orientation image via GraphQL
     *
     * @param Field $field
     * @param mixed $context
     * @param ResolveInfo $info
     * @param array|null $value
     * @param array|null $args
     * @return array
     */
    public function resolve(Field $field, $context, ResolveInfo $info, array $value = null, array $args = null): array
    {
        $input = $args['input'] ?? [];
        $this->validator->validateInput($input);

        return $this->uploadService->uploadCategoryImage($input);
    }
}
