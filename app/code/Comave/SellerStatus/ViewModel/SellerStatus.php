<?php

declare(strict_types=1);

namespace Comave\SellerStatus\ViewModel;

use Comave\SellerStatus\Model\Command\SellerCompanyProvider;
use Magento\Company\Api\CompanyUserRoleManagementInterface;
use Magento\Customer\Helper\Session\CurrentCustomer;
use Magento\Framework\App\ResourceConnection;
use Magento\Framework\View\Element\Block\ArgumentInterface;
use Webkul\Marketplace\Helper\Data;

class SellerStatus implements ArgumentInterface
{
    /**
     * @param Data $marketplaceHelper
     * @param CurrentCustomer $currentCustomer
     * @param ResourceConnection $resourceConnection
     * @param CompanyUserRoleManagementInterface $companyUserRoleManagement
     * @param SellerCompanyProvider $sellerCompanyProvider
     */
    public function __construct(
        private readonly Data $marketplaceHelper,
        private readonly CurrentCustomer $currentCustomer,
        private readonly ResourceConnection $resourceConnection,
        private readonly CompanyUserRoleManagementInterface $companyUserRoleManagement,
        private readonly SellerCompanyProvider $sellerCompanyProvider
    ) {
    }

    /**
     * @return string|null
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getSellerStatus(): ?string
    {
        if (!$this->marketplaceHelper->isSeller()) {
            return null;
        }

        $sellerCompany = $this->sellerCompanyProvider->get();
        $userRole = current(
            $this->companyUserRoleManagement->getRolesForCompanyUser(
                (int) $this->marketplaceHelper->getCustomerId(),
                (int) $sellerCompany->getId()
            )
        );

        return $userRole ? $userRole->getRoleName() : null;
    }

    /**
     * @return string|null
     */
    public function getReason(): ?string
    {
        $connection = $this->resourceConnection->getConnection('read');
        $reasonSelect = $connection->select()
            ->from(
                ['main' => $connection->getTableName('company_user_roles')],
                [
                    'note' => new \Zend_Db_Expr('COALESCE(note, "N/A")')
                ]
            )->where(
                'user_id = ?',
                $this->currentCustomer->getCustomerId()
            );

        return $connection->fetchOne($reasonSelect) ?: null;
    }
}
