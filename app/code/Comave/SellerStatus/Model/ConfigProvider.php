<?php

declare(strict_types=1);

namespace Comave\SellerStatus\Model;

use Magento\Framework\App\Config\ScopeConfigInterface;

class ConfigProvider
{
    private const string XML_PATH_ENABLED_TEST_MODE = 'seller_onboarding/general/test_enabled';

    /**
     * @param ScopeConfigInterface $scopeConfig
     */
    public function __construct(
        private readonly ScopeConfigInterface $scopeConfig
    ) {
    }

    /**
     * @return bool
     */
    public function isTestModeEnabled(): bool
    {
        return $this->scopeConfig->isSetFlag(self::XML_PATH_ENABLED_TEST_MODE);
    }
}
