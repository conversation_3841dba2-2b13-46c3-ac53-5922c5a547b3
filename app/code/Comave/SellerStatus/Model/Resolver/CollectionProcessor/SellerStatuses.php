<?php

declare(strict_types=1);

namespace Comave\SellerStatus\Model\Resolver\CollectionProcessor;

use Comave\SellerStatus\Model\RoleValidator\Active;
use Magento\Catalog\Model\ResourceModel\Product\Collection;
use Magento\CatalogGraphQl\Model\Resolver\Products\DataProvider\Product\CollectionProcessorInterface;
use Magento\Framework\Api\SearchCriteriaInterface;
use Magento\GraphQl\Model\Query\ContextInterface;

class SellerStatuses implements CollectionProcessorInterface
{
    private bool $processed = false;
    /**
     * @param Collection $collection
     * @param SearchCriteriaInterface $searchCriteria
     * @param array $attributeNames
     * @param ContextInterface|null $context
     * @return Collection
     */
    public function process(Collection $collection, SearchCriteriaInterface $searchCriteria, array $attributeNames, ContextInterface $context = null): Collection
    {
        if (
            $this->processed ||
            !$collection->getConnection()->isTableExists('marketplace_userdata') ||
            !$collection->getConnection()->isTableExists('company_user_roles') ||
            !$collection->getConnection()->isTableExists('company_roles')
        ) {
            $this->processed = true;

            return $collection;
        }

        $collection->getSelect()
            ->join(
                ['mpu' => $collection->getTable('marketplace_userdata')],
                'mp.seller_id = mpu.seller_id',
                []
            )->join(
                ['ur' => $collection->getTable('company_user_roles')],
                'ur.user_id = mp.seller_id',
                []
            )->join(
                ['r' => $collection->getTable('company_roles')],
                'ur.role_id = r.role_id',
                []
            )->where(
                'r.role_name = ?',
                Active::ROLE_NAME
            );

        $this->processed = true;

        return $collection;
    }
}
