<?php

declare(strict_types=1);

namespace Comave\SellerStatus\Model\Command;

use Comave\SellerStatus\Setup\Patch\Data\CreateCompanyAndRoles;
use Magento\Company\Api\CompanyRepositoryInterface;
use Magento\Company\Api\Data\CompanyInterface;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\Exception\LocalizedException;

class SellerCompanyProvider
{
    private ?CompanyInterface $comaveSellerCompany = null;
    /**
     * @param CompanyRepositoryInterface $companyRepository
     * @param SearchCriteriaBuilder $searchCriteriaBuilder
     */
    public function __construct(
        private readonly CompanyRepositoryInterface $companyRepository,
        private readonly SearchCriteriaBuilder $searchCriteriaBuilder,
    ) {
    }

    /**
     * @return CompanyInterface
     * @throws LocalizedException
     */
    public function get(): CompanyInterface
    {
        if ($this->comaveSellerCompany) {
            return $this->comaveSellerCompany;
        }

        $companyList = $this->companyRepository->getList(
            $this->searchCriteriaBuilder->addFilter(
                CompanyInterface::NAME,
                CreateCompanyAndRoles::COMAVE_SELLER_INC
            )->create()
        );

        if (!$companyList->getTotalCount()) {
            throw new LocalizedException(__('Unable to find seller company'));
        }

        /** @var CompanyInterface $company */
        $this->comaveSellerCompany = current($companyList->getItems());

        return $this->comaveSellerCompany;
    }
}
