<?php

declare(strict_types=1);

namespace Comave\SellerStatus\Model\Command;

use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Framework\App\Area;
use Magento\Framework\App\AreaInterface;
use Magento\Framework\App\AreaList;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\Mail\Template\TransportBuilder;
use Magento\Framework\Translate\Inline\StateInterface;
use Magento\Store\Model\Store;
use Webkul\Marketplace\Model\Seller;

class EmailNotifier
{
    /**
     * @param StateInterface $inlineTranslation
     * @param TransportBuilder $transportBuilder
     * @param ScopeConfigInterface $scopeConfig
     * @param AreaList $areaList
     * @param CustomerRepositoryInterface $customerRepository
     */
    public function __construct(
        private readonly StateInterface $inlineTranslation,
        private readonly TransportBuilder $transportBuilder,
        private readonly ScopeConfigInterface $scopeConfig,
        private readonly AreaList $areaList,
        private readonly CustomerRepositoryInterface $customerRepository,
    ) {
    }

    /**
     * @param Seller $seller
     * @param string $emailTemplate
     * @param array $templateVars
     * @return void
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\MailException
     */
    public function sendEmail(
        Seller $seller,
        string $emailTemplate,
        array $templateVars = []
    ): void {
        $this->inlineTranslation->suspend();

        $areaObject = $this->areaList->getArea(Area::AREA_FRONTEND);
        $areaObject->load(AreaInterface::PART_TRANSLATE);

        $sender = [
            'email' => $this->scopeConfig->getValue('trans_email/ident_sales/email'),
            'name' => $this->scopeConfig->getValue('trans_email/ident_sales/name'),
        ];

        $transport = $this->transportBuilder
            ->setTemplateIdentifier($emailTemplate)
            ->setTemplateOptions(
                [
                    'area' => Area::AREA_FRONTEND,
                    'store' => Store::DEFAULT_STORE_ID,
                ]
            )
            ->setTemplateVars($templateVars)
            ->setFromByScope($sender, Store::DEFAULT_STORE_ID);

        $customer = $this->customerRepository->getById($seller->getSellerId());
        $transport->addTo($customer->getEmail());

        $transport = $transport->getTransport();
        $transport->sendMessage();
        $this->inlineTranslation->resume();
    }
}
