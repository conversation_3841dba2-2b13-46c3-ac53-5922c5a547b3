<?php

declare(strict_types=1);

namespace Comave\SellerStatus\Model\Command;

use Comave\SellerStatus\Model\RoleValidator\HolidayMode;
use Magento\Framework\App\ResourceConnection;
use Magento\Framework\Exception\LocalizedException;

class GetHolidayDates
{
    /**
     * @param ResourceConnection $resourceConnection
     * @param SellerCompanyRolesProvider $sellerCompanyRolesProvider
     */
    public function __construct(
        private readonly ResourceConnection $resourceConnection,
        private readonly SellerCompanyRolesProvider $sellerCompanyRolesProvider,
    ) {
    }

    /**
     * @param string $sellerId
     * @return array{start_date: string, end_date: string}
     * @throws LocalizedException
     */
    public function get(string $sellerId): array
    {
        $roles = $this->sellerCompanyRolesProvider->get(
            HolidayMode::ROLE_NAME
        );
        $holidayMode = current($roles);

        $connection = $this->resourceConnection->getConnection('read');
        $dateSelect = $connection->select()
            ->from(
                ['main' => $connection->getTableName('company_user_roles')],
                [
                    'note' => new \Zend_Db_Expr('COALESCE(note, "N/A")')
                ]
            )->where(
                'user_id = ?',
                $sellerId
            )->where(
                'role_id = ?',
                $holidayMode->getId()
            );

        $noteArr = $connection->fetchOne($dateSelect);

        if (empty($noteArr)) {
            throw new LocalizedException(__('Holiday mode not active for %1', $sellerId));
        }

        $parsedNote = explode(', ', $noteArr);

        return [
            'start_date' => str_replace(
                'Start Date: ',
                '',
                $parsedNote[0]
            ),
            'end_date' => str_replace(
                'End Date: ',
                '',
                $parsedNote[1]
            )
        ];
    }
}
