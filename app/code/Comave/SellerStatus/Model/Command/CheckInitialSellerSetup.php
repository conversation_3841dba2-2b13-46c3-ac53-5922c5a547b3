<?php

declare(strict_types=1);

namespace Comave\SellerStatus\Model\Command;

use Comave\SellerStatus\Model\RoleValidator\UnderReview;
use Comave\SellerStatus\Setup\Patch\Data\CreateCompanyAndRoles;
use Magento\Company\Api\AclInterface;
use Magento\Company\Api\CompanyCustomerAssignmentInterface;
use Magento\Company\Api\CompanyRepositoryInterface;
use Magento\Company\Api\Data\CompanyInterface;
use Magento\Company\Api\Data\RoleInterface;
use Magento\Company\Api\RoleManagementInterface;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\App\Area;
use Magento\Framework\App\ResourceConnection;
use Magento\Framework\App\State;

class CheckInitialSellerSetup
{
    /**
     * @param State $appState
     * @param ResourceConnection $resourceConnection
     * @param CompanyRepositoryInterface $companyRepository
     * @param CompanyCustomerAssignmentInterface $companyCustomerAssignment
     * @param SearchCriteriaBuilder $searchCriteriaBuilder
     * @param AclInterface $companyRolesAssignment
     * @param RoleManagementInterface $roleManagement
     */
    public function __construct(
        private readonly State $appState,
        private readonly ResourceConnection $resourceConnection,
        private readonly CompanyRepositoryInterface $companyRepository,
        private readonly CompanyCustomerAssignmentInterface $companyCustomerAssignment,
        private readonly SearchCriteriaBuilder $searchCriteriaBuilder,
        private readonly AclInterface $companyRolesAssignment,
        private readonly RoleManagementInterface $roleManagement,
    ) {
    }

    public function execute(): void
    {
        $connection = $this->resourceConnection->getConnection('read');
        $sellerIdsSelect = $connection->select()
            ->distinct()
            ->from(
                ['main' => $connection->getTableName('marketplace_userdata')],
                [
                    'seller_id'
                ]
            )->join(
                ['c' => $connection->getTableName('customer_entity')],
                'c.entity_id = main.seller_id'
            )->joinLeft(
                ['ur' => $connection->getTableName('company_user_roles')],
                'c.entity_id = ur.user_id',
                []
            )->where(
                new \Zend_Db_Expr('ur.user_id IS NULL')
            );

        $sellerIds = $connection->fetchCol($sellerIdsSelect);

        if (empty($sellerIds)) {
            return;
        }

        $companyList = $this->companyRepository->getList(
            $this->searchCriteriaBuilder->addFilter(
                CompanyInterface::NAME,
                CreateCompanyAndRoles::COMAVE_SELLER_INC
            )->create()
        )->getItems();
        /** @var CompanyInterface $company */
        $company = current($companyList);
        $roles = $this->roleManagement->getRolesByCompanyId((int) $company->getId(), false);
        $underReview = array_filter(
            $roles,
            static fn($role) => $role->getRoleName() === UnderReview::ROLE_NAME
        ) ?? [];

        foreach ($sellerIds as $sellerId) {
            $this->appState->emulateAreaCode(
                Area::AREA_FRONTEND,
                [$this, 'addUserToRoles'],
                [
                    'company' => $company,
                    'sellerId' => $sellerId,
                    'role' => current($underReview)
                ]
            );
        }
    }

    /**
     * @param CompanyInterface $company
     * @param RoleInterface $role
     * @param string $sellerId
     * @return void
     */
    public function addUserToRoles(CompanyInterface $company, RoleInterface $role, string $sellerId): void
    {
        try {
            $this->companyCustomerAssignment->assignCustomer(
                (int) $company->getId(),
                (int) $sellerId
            );
            $this->companyRolesAssignment->assignRoles(
                (int)$sellerId,
                [$role]
            );
            echo "Successfully assigned seller " . $sellerId . PHP_EOL;
        } catch (\Throwable $e) {
            echo sprintf('Error encountered for seller %s, error message %s', $sellerId, $e->getMessage()) . PHP_EOL;
        }
    }
}
