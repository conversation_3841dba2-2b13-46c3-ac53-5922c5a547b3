<?php

declare(strict_types=1);

namespace Comave\SellerStatus\Model\Command;

use Magento\Company\Api\Data\RoleInterface;
use Magento\Company\Api\RoleManagementInterface;

class SellerCompanyRolesProvider
{
    /**
     * @var RoleInterface[]|null
     */
    private ?array $companyRoles = null;

    /**
     * @param SellerCompanyProvider $sellerCompanyProvider
     * @param RoleManagementInterface $roleManagement
     */
    public function __construct(
        private readonly SellerCompanyProvider $sellerCompanyProvider,
        private readonly RoleManagementInterface $roleManagement,
    ) {
    }

    /**
     * @param string|null $roleName
     * @return RoleInterface[]
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function get(?string $roleName = null): array
    {
        if (empty($this->companyRoles)) {
            $sellerCompany = $this->sellerCompanyProvider->get();
            $this->companyRoles = $this->roleManagement->getRolesByCompanyId(
                $sellerCompany->getId(),
                false
            );
        }

        return !empty($roleName) ? array_filter(
            $this->companyRoles,
            static fn ($role) => $role->getRoleName() === $roleName
        ) : $this->companyRoles;
    }
}
