<?php

declare(strict_types=1);

namespace Comave\SellerStatus\Model\RoleValidator;

use Comave\SellerStatus\Api\ActionableInterface;
use Comave\SellerStatus\Api\RoleProviderManagementInterface;
use Comave\SellerStatus\Api\SelfAwareInterface;
use Comave\SellerStatus\Model\Command\SellerCompanyProvider;
use Comave\SellerStatus\Model\Command\SellerCompanyRolesProvider;
use Magento\Framework\App\Area;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\App\ResponseInterface;
use Magento\Framework\App\State;
use Magento\Framework\UrlInterface;

class Closed extends AbstractRoleValidator implements SelfAwareInterface, ActionableInterface
{
    public const string ROLE_NAME = 'Closed';

    /**
     * @param SellerCompanyProvider $sellerCompanyProvider
     * @param SellerCompanyRolesProvider $sellerCompanyRolesProvider
     * @param State $appState
     * @param RequestInterface $request
     * @param UrlInterface $urlBuilder
     */
    public function __construct(
        SellerCompanyProvider $sellerCompanyProvider,
        SellerCompanyRolesProvider $sellerCompanyRolesProvider,
        private readonly State $appState,
        private readonly RequestInterface $request,
        private readonly UrlInterface $urlBuilder,
    ) {
        parent::__construct($sellerCompanyProvider, $sellerCompanyRolesProvider);
    }

    /**
     * @param int $sellerId
     * @return \Comave\SellerStatus\Api\RoleProviderManagementInterface|null
     */
    public function getForSeller(int $sellerId): ?RoleProviderManagementInterface
    {
        return null;
    }

    /**
     * @param ResponseInterface $response
     * @return void
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function act(ResponseInterface $response): void
    {
        if ($this->appState->getAreaCode() !== Area::AREA_FRONTEND) {
            return;
        }

        if ($response->isRedirect()) {
            return;
        }

        $response->setRedirect(
            $this->urlBuilder->getUrl('marketplace/account/closed')
        );
        $this->request->setDispatched();
    }
}
