<?php

declare(strict_types=1);

namespace Comave\SellerStatus\Model\RoleValidator;

use Comave\SellerStatus\Api\RoleProviderManagementInterface;
use Comave\SellerStatus\Model\Command\SellerCompanyProvider;
use Comave\SellerStatus\Model\Command\SellerCompanyRolesProvider;
use Magento\Company\Api\CompanyUserRoleManagementInterface;

class Active extends AbstractRoleValidator
{
    public const string ROLE_NAME = 'Active';

    /**
     * @param SellerCompanyProvider $sellerCompanyProvider
     * @param SellerCompanyRolesProvider $sellerCompanyRolesProvider
     * @param CompanyUserRoleManagementInterface $companyUserRoleManagement
     */
    public function __construct(
        protected readonly SellerCompanyProvider $sellerCompanyProvider,
        SellerCompanyRolesProvider $sellerCompanyRolesProvider,
        private readonly CompanyUserRoleManagementInterface $companyUserRoleManagement
    ) {
        parent::__construct($sellerCompanyProvider, $sellerCompanyRolesProvider);
    }

    /**
     * @param int $sellerId
     * @return RoleProviderManagementInterface|null
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getForSeller(int $sellerId): ?RoleProviderManagementInterface
    {
        $sellerCompany = $this->sellerCompanyProvider->get();
        $userRole = $this->companyUserRoleManagement->getRolesForCompanyUser(
            $sellerId,
            (int) $sellerCompany->getId()
        );

        $currentRole = current($userRole);

        return in_array(
            $currentRole->getRoleName(),
            [
                UnderReview::ROLE_NAME,
                Onboarding::ROLE_NAME,
                OutOfStock::ROLE_NAME,
            ]
        ) ? null : $this;
    }
}
