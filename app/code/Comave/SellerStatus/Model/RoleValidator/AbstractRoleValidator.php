<?php

declare(strict_types=1);

namespace Comave\SellerStatus\Model\RoleValidator;

use Comave\SellerStatus\Api\RoleProviderInterface;
use Comave\SellerStatus\Api\RoleProviderManagementInterface;
use Comave\SellerStatus\Model\Command\SellerCompanyProvider;
use Comave\SellerStatus\Model\Command\SellerCompanyRolesProvider;
use Magento\Company\Api\Data\CompanyInterface;
use Magento\Company\Api\Data\RoleInterface;
use Magento\Framework\Api\SimpleDataObjectConverter;
use Magento\Framework\Exception\LocalizedException;

abstract class AbstractRoleValidator implements RoleProviderManagementInterface, RoleProviderInterface
{
    /**
     * @param SellerCompanyProvider $sellerCompanyProvider
     * @param SellerCompanyRolesProvider $sellerCompanyRolesProvider
     */
    public function __construct(
        private readonly SellerCompanyProvider $sellerCompanyProvider,
        private readonly SellerCompanyRolesProvider $sellerCompanyRolesProvider
    ) {
    }

    /**
     * @param int $sellerId
     * @return \Comave\SellerStatus\Api\RoleProviderManagementInterface|null
     */
    abstract public function getForSeller(int $sellerId): ?RoleProviderManagementInterface;

    /**
     * @return RoleInterface[]
     * @throws LocalizedException
     */
    protected function getCompanyRoles(): array
    {
        return $this->sellerCompanyRolesProvider->get();
    }

    /**
     * @return CompanyInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    protected function getSellerCompany(): CompanyInterface
    {
        return $this->sellerCompanyProvider->get();
    }

    /**
     * @return RoleInterface
     * @throws LocalizedException
     */
    public function getRole(): RoleInterface
    {
        $className = explode('\\', static::class);
        $roleName = SimpleDataObjectConverter::camelCaseToSnakeCase(
            end($className)
        );
        $formattedRoleName = ucwords(
            str_replace(
                '_',
                ' ',
                $roleName
            )
        );

        $companyRoles = $this->getCompanyRoles();
        $filteredRoles =
            array_filter(
                $companyRoles,
                static fn($companyRole) => $companyRole->getRoleName() === $formattedRoleName
            ) ?? [];

        if (!count($filteredRoles)) {
            throw new LocalizedException(__('Invalid role provided %1', $formattedRoleName));
        }

        return current($filteredRoles);
    }
}
