<?php

declare(strict_types=1);

namespace Comave\SellerStatus\Model\RoleValidator;

use Comave\SellerStatus\Api\ActionableInterface;
use Comave\SellerStatus\Api\RoleProviderManagementInterface;
use Comave\SellerStatus\Api\SelfAwareInterface;
use Comave\SellerStatus\Model\Command\SellerCompanyProvider;
use Comave\SellerStatus\Model\Command\SellerCompanyRolesProvider;
use Magento\Framework\App\ResourceConnection;
use Magento\Framework\App\ResponseInterface;
use Magento\Framework\Message\ManagerInterface;
use Zend_Db_Expr;

class OutOfStock extends AbstractRoleValidator implements ActionableInterface, SelfAwareInterface
{
    public const string ROLE_NAME = 'Out Of Stock';

    /**
     * @param ResourceConnection $resourceConnection
     * @param ManagerInterface $messageManager
     * @param SellerCompanyProvider $sellerCompanyProvider
     * @param SellerCompanyRolesProvider $sellerCompanyRolesProvider
     */
    public function __construct(
        private readonly ResourceConnection $resourceConnection,
        private readonly ManagerInterface $messageManager,
        SellerCompanyProvider $sellerCompanyProvider,
        SellerCompanyRolesProvider $sellerCompanyRolesProvider
    ) {
        parent::__construct($sellerCompanyProvider, $sellerCompanyRolesProvider);
    }

    /**
     * @param int $sellerId
     * @return \Comave\SellerStatus\Api\RoleProviderManagementInterface|null
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getForSeller(int $sellerId): ?RoleProviderManagementInterface
    {
        $connection = $this->resourceConnection->getConnection('read');
        $stockCountSelect = $connection->select()
            ->from(
                ['main' => $connection->getTableName('marketplace_product')],
                []
            )->join(
                ['ci' => $connection->getTableName('cataloginventory_stock_status')],
                'ci.product_id = main.mageproduct_id',
                [
                    'instock' => new Zend_Db_Expr('SUM(ci.stock_status)')
                ]
            )->where(
                'main.seller_id = ?',
                $sellerId
            )->group('seller_id');

        /** @var bool|string $stockCount */
        $stockCount = $connection->fetchOne($stockCountSelect);

        //if the stock status of any given item is returned then it means the seller has stock
        $currentRole = $stockCount === false ||
            (is_numeric($stockCount) && $stockCount > 0) ?
                null : $this;

        return $currentRole === null ? null : $currentRole;
    }

    /**
     * @param ResponseInterface $response
     * @return void
     */
    public function act(ResponseInterface $response): void
    {
        $this->messageManager->addNoticeMessage(
            __('Add stock to your items to continue selling')
        );
    }
}
