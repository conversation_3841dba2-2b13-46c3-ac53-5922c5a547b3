<?php

declare(strict_types=1);

namespace Comave\SellerStatus\Model\RoleValidator;

use Comave\SellerStatus\Api\ActionableInterface;
use Comave\SellerStatus\Api\RoleProviderManagementInterface;
use Comave\SellerStatus\Api\SelfAwareInterface;
use Comave\SellerStatus\Model\Command\EmailNotifier;
use Comave\SellerStatus\Model\Command\GetHolidayDates;
use Comave\SellerStatus\Model\Command\SellerCompanyProvider;
use Comave\SellerStatus\Model\Command\SellerCompanyRolesProvider;
use Comave\SellerStatus\Model\SellerRegistry;
use Magento\Framework\App\Area;
use Magento\Framework\App\ResourceConnection;
use Magento\Framework\App\ResponseInterface;
use Magento\Framework\App\State;
use Psr\Log\LoggerInterface;
use Webkul\Marketplace\Model\ResourceModel\Seller;
use Webkul\Marketplace\Model\SellerFactory;

class HolidayMode extends AbstractRoleValidator implements SelfAwareInterface, ActionableInterface
{
    public const string ROLE_NAME = 'Holiday Mode';

    /**
     * @param SellerCompanyProvider $sellerCompanyProvider
     * @param SellerCompanyRolesProvider $sellerCompanyRolesProvider
     * @param State $appArea
     * @param GetHolidayDates $getHolidayDates
     * @param LoggerInterface $logger
     * @param EmailNotifier $emailNotifier
     * @param SellerRegistry $sellerRegistry
     * @param SellerFactory $sellerFactory
     * @param Seller $sellerResource
     */
    public function __construct(
        SellerCompanyProvider $sellerCompanyProvider,
        SellerCompanyRolesProvider $sellerCompanyRolesProvider,
        private readonly State $appArea,
        private readonly GetHolidayDates $getHolidayDates,
        private readonly LoggerInterface $logger,
        private readonly EmailNotifier $emailNotifier,
        private readonly SellerRegistry $sellerRegistry,
        private readonly SellerFactory $sellerFactory,
        private readonly Seller $sellerResource,
    ) {
        parent::__construct($sellerCompanyProvider, $sellerCompanyRolesProvider);
    }

    /**
     * @param int $sellerId
     * @return \Comave\SellerStatus\Api\RoleProviderManagementInterface|null
     */
    public function getForSeller(int $sellerId): ?RoleProviderManagementInterface
    {
        return null;
    }

    /**
     * @param ResponseInterface $response
     * @return void
     */
    public function act(ResponseInterface $response): void
    {
        if (!$this->sellerRegistry->getSellerId() || $this->appArea->getAreaCode() !== Area::AREA_FRONTEND) {
            return;
        }

        $seller = $this->sellerFactory->create();
        $this->sellerResource->load($seller, $this->sellerRegistry->getSellerId(), 'seller_id');

        try {
            $dates = $this->getHolidayDates->get(
                $this->sellerRegistry->getSellerId()
            );
            $this->emailNotifier->sendEmail(
                $seller,
                'comave_seller_status_email_templates_holiday_mode',
                [
                    'start_date' => \DateTime::createFromFormat(
                        'Y-m-d',
                        $dates['start_date']
                    )->format('l jS \o\f F Y'),
                    'end_date' => \DateTime::createFromFormat(
                        'Y-m-d',
                        $dates['end_date']
                    )->format('l jS \o\f F Y'),
                ]
            );
        } catch (\Exception $e) {
            $this->logger->warning(
                'Unable to send notification email',
                [
                    'message' => $e->getMessage(),
                    'type' => self::class
                ]
            );
        }

        $this->sellerRegistry->reset();
    }
}
