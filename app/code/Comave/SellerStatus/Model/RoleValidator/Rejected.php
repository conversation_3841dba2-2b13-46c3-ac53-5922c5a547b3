<?php

declare(strict_types=1);

namespace Comave\SellerStatus\Model\RoleValidator;

use Comave\SellerStatus\Api\ActionableInterface;
use Comave\SellerStatus\Api\RoleProviderManagementInterface;
use Comave\SellerStatus\Api\SelfAwareInterface;
use Comave\SellerStatus\Model\Command\EmailNotifier;
use Comave\SellerStatus\Model\Command\SellerCompanyProvider;
use Comave\SellerStatus\Model\Command\SellerCompanyRolesProvider;
use Comave\SellerStatus\Model\SellerRegistry;
use Magento\Framework\App\Area;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\App\ResourceConnection;
use Magento\Framework\App\ResponseInterface;
use Magento\Framework\App\State;
use Magento\Framework\UrlInterface;
use Psr\Log\LoggerInterface;
use Webkul\Marketplace\Model\ResourceModel\Seller;
use Webkul\Marketplace\Model\SellerFactory;

class Rejected extends AbstractRoleValidator implements ActionableInterface, SelfAwareInterface
{
    public const string ROLE_NAME = 'Rejected';

    /**
     * @param SellerCompanyProvider $sellerCompanyProvider
     * @param SellerCompanyRolesProvider $sellerCompanyRolesProvider
     * @param ResourceConnection $resourceConnection
     * @param State $appArea
     * @param SellerFactory $sellerFactory
     * @param Seller $sellerResource
     * @param LoggerInterface $logger
     * @param EmailNotifier $emailNotifier
     * @param SellerRegistry $sellerRegistry
     * @param RequestInterface $request
     * @param UrlInterface $urlBuilder
     */
    public function __construct(
        SellerCompanyProvider $sellerCompanyProvider,
        SellerCompanyRolesProvider $sellerCompanyRolesProvider,
        private readonly ResourceConnection $resourceConnection,
        private readonly State $appArea,
        private readonly SellerFactory $sellerFactory,
        private readonly Seller $sellerResource,
        private readonly LoggerInterface $logger,
        private readonly EmailNotifier $emailNotifier,
        private readonly SellerRegistry $sellerRegistry,
        private readonly RequestInterface $request,
        private readonly UrlInterface $urlBuilder
    ) {
        parent::__construct($sellerCompanyProvider, $sellerCompanyRolesProvider);
    }

    /**
     * @param int $sellerId
     * @return \Comave\SellerStatus\Api\RoleProviderManagementInterface|null
     */
    public function getForSeller(int $sellerId): ?RoleProviderManagementInterface
    {
        return null;
    }

    /**
     * @param ResponseInterface $response
     * @return void
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function act(ResponseInterface $response): void
    {
        if (!$this->sellerRegistry->getSellerId() || $this->appArea->getAreaCode() !== Area::AREA_ADMINHTML) {
            if (!$response->isRedirect() && $this->appArea->getAreaCode() === Area::AREA_FRONTEND) {
                $response->setRedirect(
                    $this->urlBuilder->getUrl('marketplace/account/rejected')
                );
                $this->request->setDispatched();
            }

            return;
        }

        $seller = $this->sellerFactory->create();
        $this->sellerResource->load($seller, $this->sellerRegistry->getSellerId(), 'seller_id');

        try {
            $this->emailNotifier->sendEmail(
                $seller,
                'comave_seller_status_email_templates_rejected',
                [
                    'reason' => $this->getReason(),
                    'support_contact' => '<EMAIL>'
                ]
            );
        } catch (\Exception $e) {
            $this->logger->warning(
                'Unable to send notification email',
                [
                    'message' => $e->getMessage(),
                    'type' => self::class
                ]
            );
        }

        $this->sellerRegistry->reset();
    }

    /**
     * @return string
     */
    private function getReason(): string
    {
        $connection = $this->resourceConnection->getConnection('read');
        $reasonSelect = $connection->select()
            ->from(
                ['main' => $connection->getTableName('company_user_roles')],
                [
                    'note' => new \Zend_Db_Expr('COALESCE(note, "N/A")')
                ]
            )->where(
                'user_id = ?',
                $this->sellerRegistry->getSellerId()
            );

        return $connection->fetchOne($reasonSelect);
    }
}
