<?php

declare(strict_types=1);

namespace Comave\SellerStatus\Model\RoleValidator;

use Comave\SellerStatus\Api\ActionableInterface;
use Comave\SellerStatus\Api\RoleProviderManagementInterface;
use Comave\SellerStatus\Api\SelfAwareInterface;
use Comave\SellerStatus\Model\Command\SellerCompanyProvider;
use Comave\SellerStatus\Model\Command\SellerCompanyRolesProvider;
use Magento\Framework\App\Area;
use Magento\Framework\App\ResponseInterface;
use Magento\Framework\App\State;
use Magento\Framework\Message\ManagerInterface;

class Onboarding extends AbstractRoleValidator implements SelfAwareInterface, ActionableInterface
{
    public const string ROLE_NAME = 'Onboarding';

    /**
     * @param SellerCompanyProvider $sellerCompanyProvider
     * @param SellerCompanyRolesProvider $sellerCompanyRolesProvider
     * @param State $appState
     * @param ManagerInterface $messageManager
     */
    public function __construct(
        SellerCompanyProvider $sellerCompanyProvider,
        SellerCompanyRolesProvider $sellerCompanyRolesProvider,
        private readonly State $appState,
        private readonly ManagerInterface $messageManager,
    ) {
        parent::__construct($sellerCompanyProvider, $sellerCompanyRolesProvider);
    }

    /**
     * @param int $sellerId
     * @return \Comave\SellerStatus\Api\RoleProviderManagementInterface|null
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getForSeller(int $sellerId): ?RoleProviderManagementInterface
    {
        return null;
    }

    /**
     * @param ResponseInterface $response
     * @return void
     */
    public function act(ResponseInterface $response): void
    {
        if ($this->appState->getAreaCode() !== Area::AREA_FRONTEND) {
            return;
        }

        $this->messageManager->getMessages(true);
        $this->messageManager->addNoticeMessage(
            __('Complete onboarding to activate your shop')
        );
    }
}
