<?php

declare(strict_types=1);

namespace Comave\SellerStatus\Model;

use Comave\SellerStatus\Api\RoleProviderManagementInterface;
use Magento\Company\Api\Data\RoleInterface;
use Magento\Framework\Exception\LocalizedException;

class RoleProviderManagementComposite implements RoleProviderManagementInterface
{
    /**
     * @param RoleProviderManagementInterface[] $roleValidators
     */
    public function __construct(
        private readonly array $roleValidators = []
    ) {
    }

    /**
     * @param int $sellerId
     * @return \Comave\SellerStatus\Api\RoleProviderManagementInterface|null
     */
    public function getForSeller(int $sellerId): ?RoleProviderManagementInterface
    {
        foreach ($this->roleValidators as $roleValidator) {
            if (!$result = $roleValidator->getForSeller($sellerId)) {
                continue;
            }

            return $result;
        }

        return null;
    }
}
