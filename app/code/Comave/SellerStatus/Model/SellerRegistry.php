<?php

declare(strict_types=1);

namespace Comave\SellerStatus\Model;

class SellerRegistry
{
    private ?string $sellerId = null;

    /**
     * @return string|null
     */
    public function getSellerId(): ?string
    {
        return $this->sellerId;
    }

    /**
     * @param string $sellerId
     * @return $this
     */
    public function setSellerId(string $sellerId): self
    {
        $this->sellerId = $sellerId;
        return $this;
    }

    /**
     * @return void
     */
    public function reset(): void
    {
        $this->sellerId = null;
    }
}
