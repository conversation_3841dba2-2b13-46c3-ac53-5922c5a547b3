<?php

declare(strict_types=1);

namespace Comave\SellerStatus\Model;

use Magento\Company\Model\Email\Sender;
use Magento\Customer\Api\Data\CustomerInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;

//We specifically don't want company emails to be sent as the company is used as a wrapper for seller ACL's
class CompanyEmailSender extends Sender
{
    /**
     * Send email to customer with assign message.
     *
     * @param CustomerInterface $customer
     * @param int $companyId
     * @return $this
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function sendAssignSuperUserNotificationEmail(CustomerInterface $customer, $companyId): self
    {
        return $this;
    }

    /**
     * Send email to customer with remove message.
     *
     * @param CustomerInterface $customer
     * @param int $companyId
     * @return $this
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function sendRemoveSuperUserNotificationEmail(CustomerInterface $customer, $companyId): self
    {
        return $this;
    }

    /**
     * Send email to customer with inactivate message.
     *
     * @param CustomerInterface $customer
     * @param int $companyId
     * @return $this
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function sendInactivateSuperUserNotificationEmail(CustomerInterface $customer, $companyId): self
    {
        return $this;
    }

    /**
     * Send email to sales representative.
     *
     * @param int $companyId
     * @param int $salesRepresentativeId [optional]
     * @return $this
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function sendSalesRepresentativeNotificationEmail($companyId, $salesRepresentativeId = 0): self
    {
        return $this;
    }

    /**
     * Send email to customer after assign company to him.
     *
     * @param \Magento\Customer\Api\Data\CustomerInterface $customer
     * @param int $companyId
     * @return $this
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function sendCustomerCompanyAssignNotificationEmail(
        \Magento\Customer\Api\Data\CustomerInterface $customer,
                                                     $companyId
    ): self {
        return $this;
    }

    /**
     * Notify admin about new company.
     *
     * @param CustomerInterface $customer
     * @param string $companyName
     * @param string $companyUrl
     * @return $this
     */
    public function sendAdminNotificationEmail(CustomerInterface $customer, $companyName, $companyUrl): self
    {
        return $this;
    }

    /**
     * Notify company admin of company status change.
     *
     * @param CustomerInterface $customer
     * @param int $companyId
     * @param string $templatePath
     * @return $this
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function sendCompanyStatusChangeNotificationEmail(CustomerInterface $customer, $companyId, $templatePath): self
    {
        return $this;
    }

    /**
     * Send email to customer with status update message.
     *
     * @param CustomerInterface $customer
     * @param int $status
     * @param int $companyId
     * @return $this
     * @throws LocalizedException
     * @throws NoSuchEntityException
     */
    public function sendUserStatusChangeNotificationEmail(
        CustomerInterface $customer,
        int $status,
        int $companyId
    ): self {
        return $this;
    }
}
