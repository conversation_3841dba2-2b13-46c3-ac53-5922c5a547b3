<?php

declare(strict_types=1);

namespace Comave\SellerStatus\Model\Option;

use Comave\SellerStatus\Model\Command\SellerCompanyRolesProvider;
use Magento\Framework\Data\OptionSourceInterface;
use Magento\Framework\Phrase;

class StatusProvider implements OptionSourceInterface
{
    /**
     * @param SellerCompanyRolesProvider $sellerCompanyRolesProvider
     */
    public function __construct(private readonly SellerCompanyRolesProvider $sellerCompanyRolesProvider)
    {
    }

    /**
     * @return array<array{value: string, label: Phrase|string}>
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function toOptionArray(): array
    {
        $options = [];

        foreach ($this->sellerCompanyRolesProvider->get() as $role) {
            if (str_contains($role->getRoleName(), 'Default')) {
                continue;
            }

            $options[] = [
                'value' => $role->getId(),
                'label' => $role->getRoleName()
            ];
        }

        return $options;
    }
}
