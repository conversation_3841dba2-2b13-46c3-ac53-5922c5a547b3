<?php

declare(strict_types=1);

namespace Comave\SellerStatus\Setup\Patch\Data;

use Magento\Company\Model\PermissionManagementInterface;
use Magento\Company\Setup\CompanySetup;
use Magento\Customer\Api\Data\CustomerInterface;
use Magento\CustomerGraphQl\Model\Customer\CreateCustomerAccount;
use Magento\Framework\App\Area;
use Magento\Framework\App\ResourceConnection;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Setup\Patch\DataPatchInterface;
use Magento\Company\Api\Data\CompanyInterfaceFactory;
use Magento\Company\Api\CompanyRepositoryInterface;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Company\Api\Data\RoleInterfaceFactory;
use Magento\Company\Api\RoleRepositoryInterface;
use Magento\Framework\App\State;
use Magento\Store\Model\StoreManagerInterface;

class CreateCompanyAndRoles implements DataPatchInterface
{
    public const string COMAVE_SELLER_INC = 'Comave Seller Inc.';

    /**
     * @param CompanySetup $companySetup
     * @param State $appState
     * @param ResourceConnection $resourceConnection
     * @param CompanyInterfaceFactory $companyFactory
     * @param CompanyRepositoryInterface $companyRepo
     * @param CustomerRepositoryInterface $customerRepo
     * @param PermissionManagementInterface $permissionManagement
     * @param RoleInterfaceFactory $roleFactory
     * @param CreateCustomerAccount $createCustomerAccount
     * @param RoleRepositoryInterface $roleRepo
     * @param StoreManagerInterface $storeManager
     */
    public function __construct(
        private readonly CompanySetup $companySetup,
        private readonly State $appState,
        private readonly ResourceConnection $resourceConnection,
        private readonly CompanyInterfaceFactory $companyFactory,
        private readonly CompanyRepositoryInterface $companyRepo,
        private readonly CustomerRepositoryInterface $customerRepo,
        private readonly PermissionManagementInterface $permissionManagement,
        private readonly RoleInterfaceFactory $roleFactory,
        private readonly CreateCustomerAccount $createCustomerAccount,
        private readonly RoleRepositoryInterface $roleRepo,
        private readonly StoreManagerInterface $storeManager,
    ) {}

    /**
     * @return $this
     * @throws \Magento\Framework\Exception\CouldNotSaveException
     * @throws \Magento\Framework\Exception\InputException
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function apply(): self
    {
        try {
            $this->appState->setAreaCode('adminhtml');
        } catch (\Exception) {
        }

        $adminEmail = '<EMAIL>';

        try {
            $adminCustomer = $this->customerRepo->get($adminEmail);
        } catch (NoSuchEntityException) {
            $adminCustomer = $this->appState->emulateAreaCode(
                Area::AREA_FRONTEND,
                [$this, 'createAdmin'],
                [
                    'adminEmail' =>  $adminEmail
                ]
            );
        }

        $sellerGroup = $this->getSellerGroupId();

        $company = $this->companyFactory->create();
        $company->setCompanyName('Comave Seller Inc.')
            ->setLegalName('Comave Seller Inc.')
            /* add bogus address data */
            ->setCountryId('FR')
            ->setRegionId(1)
            ->setTelephone('00000000000')
            ->setCity('Paris')
            ->setPostcode('000000')
            ->setStreet('Comave seller inc. street')
            /* end */
            ->setCompanyEmail($adminEmail)
            ->setEmail($adminEmail)
            ->setCustomerGroupId($sellerGroup)
            ->setStatus(1)
            ->setSuperUserId($adminCustomer->getId());

        $company = $this->companyRepo->save($company);
        $adminCustomer->setData('ignore_validation_flag', true);
        $adminCustomer->setGroupId($sellerGroup);
        $this->customerRepo->save($adminCustomer);

        $roles = [
            'under review' => [],
            'onboarding' => [
                'Comave_SellerStatus::seller_menu',
                'Comave_SellerStatus::menu_dashboard',
                'Comave_SellerStatus::menu_settings',
            ],
            'active' => [
                'Comave_SellerStatus::seller_menu',
                'Comave_SellerStatus::menu_dashboard',
                'Comave_SellerStatus::menu_order',
                'Comave_SellerStatus::menu_product',
                'Comave_SellerStatus::menu_customer',
                'Comave_SellerStatus::menu_finance',
                'Comave_SellerStatus::menu_settings',
            ],
            'out of stock' => [
                'Comave_SellerStatus::seller_menu',
                'Comave_SellerStatus::menu_dashboard',
                'Comave_SellerStatus::menu_order',
                'Comave_SellerStatus::menu_product',
                'Comave_SellerStatus::menu_customer',
                'Comave_SellerStatus::menu_finance',
                'Comave_SellerStatus::menu_settings',
            ],
            'deactivated' => [],
            'suspended' => [],
            'rejected' => [],
            'closed' => [],
            'holiday mode' => [
                'Comave_SellerStatus::seller_menu',
                'Comave_SellerStatus::menu_dashboard',
                'Comave_SellerStatus::menu_order',
                'Comave_SellerStatus::menu_product',
                'Comave_SellerStatus::menu_customer',
                'Comave_SellerStatus::menu_finance',
                'Comave_SellerStatus::menu_settings',
            ]
        ];

        foreach ($roles as $roleName => $permissions) {
            $permissionsArr = $this->permissionManagement->populatePermissions($permissions);
            $role = $this->roleFactory->create();
            $role->setCompanyId($company->getId())
                ->setRoleName(ucwords($roleName))
                ->setPermissions($permissionsArr);

            $this->roleRepo->save($role);
        }

        $this->companySetup->applyPermissions();

        return $this;
    }

    /**
     * @return array|string[]
     */
    public static function getDependencies(): array
    {
        return [];
    }

    /**
     * @return array|string[]
     */
    public function getAliases(): array
    {
        return [];
    }

    /**
     * @return int
     */
    private function getSellerGroupId(): int
    {
        $connection = $this->resourceConnection->getConnection('read');
        $groupIdSelect = $connection->select()
            ->from(
                ['main' => $connection->getTableName('customer_group')],
                [
                    'customer_group_id',
                ]
            )->where(
                'customer_group_code = ?',
                'Sellers'
            );

        $groupId = $connection->fetchOne($groupIdSelect);

        return is_numeric($groupId) ? (int) $groupId : 1;
    }

    /**
     * @param string $adminEmail
     * @return CustomerInterface
     * @throws \Exception
     */
    public function createAdmin(string $adminEmail): CustomerInterface
    {
        $currentStore = $this->storeManager->getDefaultStoreView();
        $customerData = [
            'firstname' => 'Admin',
            'email' => $adminEmail,
            'lastname' => 'Comave',
            'password' => '@dminC0m@v3!',
        ];

        return $this->createCustomerAccount->execute(
            $customerData,
            $currentStore
        );
    }
}
