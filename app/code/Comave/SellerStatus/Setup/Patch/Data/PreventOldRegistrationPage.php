<?php

declare(strict_types=1);

namespace Comave\SellerStatus\Setup\Patch\Data;

use Comave\Marketplace\Service\BaseUrlResolver;
use Magento\Framework\App\ResourceConnection;
use Magento\Store\Api\StoreRepositoryInterface;
use Magento\Framework\Setup\Patch\DataPatchInterface;

class PreventOldRegistrationPage implements DataPatchInterface
{
    /**
     * @param \Comave\Marketplace\Service\BaseUrlResolver $baseUrlResolver
     * @param ResourceConnection $resourceConnection
     * @param StoreRepositoryInterface $storeRepository
     */
    public function __construct(
        private readonly BaseUrlResolver $baseUrlResolver,
        private readonly ResourceConnection $resourceConnection,
        private readonly StoreRepositoryInterface $storeRepository
    ) {
    }

    /**
     * @return string[]
     */
    public static function getDependencies(): array
    {
        return [];
    }

    /**
     * @return string[]
     */
    public function getAliases(): array
    {
        return [];
    }

    /**
     * @return $this
     * @throws \Exception
     */
    public function apply(): self
    {
        $sqlInsertString = <<<SQLSTRING
INSERT INTO `url_rewrite` (`entity_type`, `entity_id`, `request_path`, `target_path`, `redirect_type`, `store_id`, `description`, `is_autogenerated`, `metadata`)
VALUES
	('custom', 0, 'marketplace', '%s/en/retailers', 301, %s, NULL, 0, NULL);

SQLSTRING;

        $connection = $this->resourceConnection->getConnection();
        $connection->beginTransaction();

        foreach ($this->storeRepository->getList() as $store) {
            try {
                if (!$store->getIsActive() || $store->getId() == 0) {
                    continue;
                }

                $frontendUrl = $this->baseUrlResolver->getFrontendUrl();
                $query = sprintf(
                    $sqlInsertString,
                    rtrim($frontendUrl, '/'),
                    $store->getId()
                );
                $connection->query($query);
            } catch (\Exception $e) {
                $connection->rollBack();
                throw $e;
            }
        }

        $connection->commit();

        return $this;
    }
}
