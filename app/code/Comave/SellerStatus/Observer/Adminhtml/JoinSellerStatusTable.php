<?php

declare(strict_types=1);

namespace Comave\SellerStatus\Observer\Adminhtml;

use Magento\Framework\DB\Select;
use Magento\Framework\Event\Observer;
use Webkul\Marketplace\Model\ResourceModel\Seller\Grid\Collection;

class JoinSellerStatusTable implements \Magento\Framework\Event\ObserverInterface
{
    private bool $isLinked = false;

    /**
     * @param Observer $observer
     * @return void
     */
    public function execute(Observer $observer): void
    {
        if ($this->isLinked) {
            return;
        }

        /** @var Collection $collection */
        $collection = $observer->getSellerGridCollection();
        $collection->getSelect()
            ->join(
                ['cr' => $collection->getTable('company_user_roles')],
                'cr.user_id = main_table.seller_id',
                [
                    'role_id',
                    'note'
                ]
            )->joinLeft(
                ['aa' => $collection->getTable('admin_user')],
                'aa.user_id = cr.force_assigned',
                [
                    'force_assigned' => new \Zend_Db_Expr(
                        'CASE WHEN force_assigned = 0 THEN "Automated" ELSE aa.username END'
                    )
                ]
            );

        $this->isLinked = true;
    }
}
