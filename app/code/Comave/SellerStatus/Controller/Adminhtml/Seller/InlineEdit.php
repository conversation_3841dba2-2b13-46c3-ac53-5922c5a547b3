<?php

declare(strict_types=1);

namespace Comave\SellerStatus\Controller\Adminhtml\Seller;

use Comave\SellerStatus\Api\ActionableInterface;
use Comave\SellerStatus\Model\Command\SellerCompanyProvider;
use Comave\SellerStatus\Model\RoleValidator\Closed;
use Comave\SellerStatus\Model\RoleValidator\Rejected;
use Comave\SellerStatus\Model\SellerRegistry;
use Magento\Backend\App\Action;
use Magento\Backend\App\Action\Context;
use Magento\Company\Api\CompanyUserRoleManagementInterface;
use Magento\Framework\App\Action\HttpPostActionInterface;
use Magento\Framework\App\ResourceConnection;
use Magento\Framework\Controller\Result\Json;
use Magento\Framework\Controller\ResultFactory;
use Magento\Framework\Event\ManagerInterface;

class InlineEdit extends Action implements HttpPostActionInterface
{
    public const string ADMIN_RESOURCE = 'Comave_SellerStatus::change_status';

    /**
     * @param Context $context
     * @param CompanyUserRoleManagementInterface $companyUserRoleManagement
     * @param SellerCompanyProvider $sellerCompanyProvider
     * @param SellerRegistry $sellerRegistry
     * @param ManagerInterface $eventManager
     * @param ResourceConnection $resourceConnection
     */
    public function __construct(
        protected readonly Context $context,
        private readonly CompanyUserRoleManagementInterface $companyUserRoleManagement,
        private readonly SellerCompanyProvider $sellerCompanyProvider,
        private readonly SellerRegistry $sellerRegistry,
        private readonly ManagerInterface $eventManager,
        private readonly ResourceConnection $resourceConnection,
    ) {
        parent::__construct($context);
    }

    /**
     * @return Json
     */
    public function execute(): Json
    {
        $resultJson = $this->resultFactory->create(ResultFactory::TYPE_JSON);
        $messages = [];
        $error = false;

        if (empty($this->getRequest()->getParam('items'))) {
            $error = true;
            $messages[] = __('Invalid Request');
        } else {
            $currentUpdated = current($this->getRequest()->getParam('items'));
            $connection = $this->resourceConnection->getConnection('write');
            $sellerIdSelect = $connection->select()
                ->from(
                    $connection->getTableName('marketplace_userdata'),
                    [
                        'seller_id'
                    ]
                )->where(
                    'entity_id IN (?)',
                    (int) $currentUpdated['entity_id']
                );

            $sellerId = $connection->fetchOne($sellerIdSelect);
            $currentRole = current(
                $this->companyUserRoleManagement->getRolesForCompanyUser(
                    (int) $sellerId,
                    (int) $this->sellerCompanyProvider->get()->getId()
                )
            );

            if (in_array($currentRole->getRoleName(), [Closed::ROLE_NAME, Rejected::ROLE_NAME])) {
                $error = true;
                $messages[] = __('The role cannot be changed as it has already been terminated');

                return $resultJson->setData([
                    'messages' => $messages,
                    'error' => $error
                ]);
            }

            $connection->beginTransaction();

            try {
                $this->eventManager->dispatch(
                    'before_seller_status_change',
                    [
                        'seller' => $sellerId,
                        'currentRole' => $currentRole,
                        'newRoleId' => $currentUpdated['role_id'],
                    ]
                );

                $connection->update(
                    $connection->getTableName('company_user_roles'),
                    [
                        'role_id' => (int) $currentUpdated['role_id'],
                        'force_assigned' => $this->context->getAuth()->getUser()->getId(), //set the user who assigned the role,
                        'note' => !empty($currentUpdated['note']) ?
                            strip_tags($currentUpdated['note']) : null
                    ],
                    [
                        'user_id = ?' => $sellerId
                    ]
                );

                $sellerCompany = $this->sellerCompanyProvider->get();
                $companyUserRole = current(
                    $this->companyUserRoleManagement->getRolesForCompanyUser(
                        (int) $sellerId,
                        (int) $sellerCompany->getId()
                    )
                );
                $statusInstance = $companyUserRole->getExtensionAttributes()->getStatusInstance();

                if ($statusInstance instanceof ActionableInterface) {
                    $this->sellerRegistry->setSellerId($sellerId);
                    $statusInstance->act($this->getResponse());
                }

                $connection->commit();
            } catch (\Throwable $exception) {
                $connection->rollBack();
                $error = true;
                $messages[] = $exception->getMessage();
            }
        }

        $resultJson->setData([
            'messages' => $messages,
            'error' => $error
        ]);

        return $resultJson;
    }
}
