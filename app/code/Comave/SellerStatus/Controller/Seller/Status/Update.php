<?php

declare(strict_types=1);

namespace Comave\SellerStatus\Controller\Seller\Status;

use Comave\SellerStatus\Api\ActionableInterface;
use Comave\SellerStatus\Api\RoleProviderManagementInterface;
use Comave\SellerStatus\Model\Command\SellerCompanyRolesProvider;
use Comave\SellerStatus\Model\RoleValidator\Active;
use Comave\SellerStatus\Model\RoleValidator\HolidayMode;
use Comave\SellerStatus\Model\SellerRegistry;
use Magento\Company\Api\CompanyUserRoleManagementInterface;
use Magento\Company\Api\Data\RoleInterface;
use Magento\Customer\Controller\AccountInterface;
use Magento\Framework\App\Action\Action;
use Magento\Framework\App\Action\Context;
use Magento\Framework\App\Action\HttpPostActionInterface;
use Magento\Framework\App\ResourceConnection;
use Magento\Framework\Controller\Result\Json;
use Magento\Framework\Controller\ResultFactory;
use Webkul\Marketplace\Helper\Data;

class Update extends Action implements AccountInterface, HttpPostActionInterface
{
    /**
     * @param Context $context
     * @param ResourceConnection $resourceConnection
     * @param Data $marketplaceHelper
     * @param RoleProviderManagementInterface $roleProviderManagement
     * @param SellerCompanyRolesProvider $sellerCompanyRolesProvider
     * @param CompanyUserRoleManagementInterface $companyUserRoleManagement
     */
    public function __construct(
        Context $context,
        private readonly ResourceConnection $resourceConnection,
        private readonly Data $marketplaceHelper,
        private readonly RoleProviderManagementInterface $roleProviderManagement,
        private readonly SellerCompanyRolesProvider $sellerCompanyRolesProvider,
        private readonly SellerRegistry $sellerRegistry,
    ) {
        parent::__construct($context);
    }

    /**
     * @return Json
     */
    public function execute(): Json
    {
        /** @var Json $result */
        $result = $this->resultFactory->create(ResultFactory::TYPE_JSON);

        if (!$this->getRequest()->isAjax() || !$this->marketplaceHelper->isSeller()) {
            return $result->setData([
                'error' => true,
                'message' => __('Invalid request')
            ]);
        }

        $shouldSet = $this->getRequest()->getParam('flag');

        if ((int) $shouldSet === 1) {
            $startDate = $this->getRequest()->getParam('start_date');
            $endDate = $this->getRequest()->getParam('end_date');
            $isValidStart = $this->isValidDate($startDate);
            $isValidEnd = $this->isValidDate($endDate);

            if (!$isValidStart || !$isValidEnd) {
                return $result->setData([
                    'success' => false,
                    'message' => __('Invalid date format.')
                ]);
            }

            $roles = $this->sellerCompanyRolesProvider->get(
                HolidayMode::ROLE_NAME
            );
            $role = current($roles);
            $this->updateRole(
                (int) $role->getId(),
                sprintf(
                    'Start Date: %s, End Date: %s',
                    $startDate,
                    $endDate
                ),
            );

            $statusInstance = $role->getExtensionAttributes()->getStatusInstance();
            if ($statusInstance instanceof ActionableInterface) {
                $this->sellerRegistry->setSellerId(
                    (string) $this->marketplaceHelper->getCustomerId()
                );
                $statusInstance->act($this->getResponse());
            }
        } else {
            $getRoleForUser = $this->roleProviderManagement->getForSeller(
                (int) $this->marketplaceHelper->getCustomerId()
            );
            /** @var RoleInterface $role */
            $role = $getRoleForUser->getRole();

            if ($role->getRoleName() !== Active::ROLE_NAME) {
                $this->updateRole(
                    (int) $getRoleForUser->getRole()->getId(),
                    'Automatically set after checks'
                );

                return $result->setData([
                    'error' => false,
                    'message' => __(
                        'We detected that we cannot reactivate your account, new provided status is %1',
                        $getRoleForUser->getRole()->getRoleName()
                    )
                ]);
            }

            $this->updateRole((int) $role->getId());
        }

        return $result->setData([
            'error' => false,
            'message' => __('Successfully updated your status')
        ]);
    }

    /**
     * @param string $date
     * @param string $format
     * @return bool
     */
    private function isValidDate(string $date, string $format = 'Y-m-d'): bool
    {
        $d = \DateTime::createFromFormat($format, $date);
        return $d && $d->format($format) === $date;
    }

    /**
     * @param string $note
     * @param int $roleId
     * @return void
     */
    private function updateRole(int $roleId, string $note = ''): void
    {
        $connection = $this->resourceConnection->getConnection();
        $connection->update(
            $connection->getTableName('company_user_roles'),
            [
                'note' => strip_tags($note),
                'role_id' => $roleId,
                'force_assigned' => 0
            ],
            [
                'user_id = ?' => $this->marketplaceHelper->getCustomerId()
            ]
        );
    }
}
