<?php

declare(strict_types=1);

namespace Comave\SellerStatus\Controller\Account;

use Comave\SellerStatus\Model\Command\SellerCompanyProvider;
use Magento\Company\Api\CompanyUserRoleManagementInterface;
use Magento\Customer\Controller\AccountInterface;
use Magento\Customer\Helper\Session\CurrentCustomer;
use Magento\Framework\App\Action\HttpGetActionInterface;
use Magento\Framework\App\Action\HttpPostActionInterface;
use Magento\Framework\Controller\Result\Redirect;
use Magento\Framework\Controller\ResultFactory;
use Magento\Framework\View\Result\Page;

class Closed implements AccountInterface, HttpGetActionInterface, HttpPostActionInterface
{
    /**
     * @param CompanyUserRoleManagementInterface $companyUserRoleManagement
     * @param CurrentCustomer $currentCustomer
     * @param SellerCompanyProvider $sellerCompanyProvider
     * @param ResultFactory $resultFactory
     */
    public function __construct(
        private readonly CompanyUserRoleManagementInterface $companyUserRoleManagement,
        private readonly CurrentCustomer $currentCustomer,
        private readonly SellerCompanyProvider $sellerCompanyProvider,
        private readonly ResultFactory $resultFactory
    ) {
    }

    /**
     * @return Page|Redirect
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function execute(): Page|Redirect
    {
        $userRoles = $this->companyUserRoleManagement->getRolesForCompanyUser(
            (int) $this->currentCustomer->getCustomerId(),
            (int) $this->sellerCompanyProvider->get()->getId()
        );
        $currentRole = current($userRoles ?? []);

        if (!empty($currentRole) && $currentRole->getRoleName() !== \Comave\SellerStatus\Model\RoleValidator\Closed::ROLE_NAME) {
            return $this->resultFactory->create(ResultFactory::TYPE_REDIRECT)
                ->setPath('marketplace/account/dashboard');
        }

        $resultPage = $this->resultFactory->create(ResultFactory::TYPE_PAGE);
        $resultPage->getConfig()->getTitle()->set(
            __('Your seller account has been terminated')
        );

        return $resultPage;
    }
}
