<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="seller_onboarding" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="1234" translate="label">
            <tab>comave</tab>
            <label>Seller Onboarding Settings</label>
            <resource>Magento_Backend::config</resource>
            <group id="general" translate="label" type="text" sortOrder="950" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>General Settings</label>
                <field id="test_enabled" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1" canRestore="1">
                    <label>Test Mode</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>This disables any check done on seller status changes</comment>
                </field>
            </group>
        </section>

        <section id="comave_logger">
            <group id="comave_seller_status" translate="label" type="text" sortOrder="950" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Comave Seller Status Logger</label>
                <field id="enabled" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enable Logging</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <depends>
                    <field id="comave_logger/general/enabled">1</field>
                </depends>
            </group>
        </section>
    </system>
</config>
