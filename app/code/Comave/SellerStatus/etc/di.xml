<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <preference for="Comave\SellerStatus\Api\RoleProviderManagementInterface"
        type="Comave\SellerStatus\Model\RoleProviderManagementComposite"/>
    <preference for="Magento\Company\Model\Email\Sender" type="Comave\SellerStatus\Model\CompanyEmailSender"/>

    <type name="Comave\SellerStatus\Model\RoleProviderManagementComposite">
        <arguments>
            <argument xsi:type="array" name="roleValidators">
                <item xsi:type="object" name="closed">Comave\SellerStatus\Model\RoleValidator\Closed</item>
                <item xsi:type="object" name="under_review">Comave\SellerStatus\Model\RoleValidator\UnderReview</item>
                <item xsi:type="object" name="out_of_stock">Comave\SellerStatus\Model\RoleValidator\OutOfStock</item>
                <item xsi:type="object" name="active">Comave\SellerStatus\Model\RoleValidator\Active</item>
                <item xsi:type="object" name="deactivated">Comave\SellerStatus\Model\RoleValidator\Deactivated</item>
                <item xsi:type="object" name="suspended">Comave\SellerStatus\Model\RoleValidator\Suspended</item>
                <item xsi:type="object" name="rejected">Comave\SellerStatus\Model\RoleValidator\Rejected</item>
                <item xsi:type="object" name="holiday_mode">Comave\SellerStatus\Model\RoleValidator\HolidayMode</item>
                <item xsi:type="object" name="onboarding">Comave\SellerStatus\Model\RoleValidator\Onboarding</item>
            </argument>
        </arguments>
    </type>

    <virtualType name="ComaveSellerStatusLogger" type="Comave\Logger\Model\ComaveLogger">
        <arguments>
            <argument name="name" xsi:type="string">ComaveSellerStatus</argument>
            <argument name="loggerPath" xsi:type="string">comave_seller_status</argument>
        </arguments>
    </virtualType>

    <type name="Webkul\Marketplace\Model\ResourceModel\Seller">
        <plugin name="setInitialRole" type="Comave\SellerStatus\Plugin\AssignSellerStatus"/>
    </type>

    <type name="Comave\SellerStatus\Cron\OutOfStockSellers">
        <arguments>
            <argument xsi:type="object" name="logger">ComaveSellerStatusLogger</argument>
        </arguments>
    </type>

    <type name="Comave\SellerStatus\Plugin\AssignSellerStatus">
        <arguments>
            <argument xsi:type="object" name="logger">ComaveSellerStatusLogger</argument>
        </arguments>
    </type>

    <type name="Comave\SellerStatus\Model\RoleValidator\HolidayMode">
        <arguments>
            <argument xsi:type="object" name="logger">ComaveSellerStatusLogger</argument>
        </arguments>
    </type>

    <type name="Comave\SellerStatus\Cron\CheckHolidayModes">
        <arguments>
            <argument xsi:type="object" name="logger">ComaveSellerStatusLogger</argument>
        </arguments>
    </type>

    <type name="Magento\Company\Api\CompanyUserRoleManagementInterface">
        <plugin name="isForcedAssigned" type="Comave\SellerStatus\Plugin\IsForceAssigned"/>
    </type>

    <type name="Magento\Company\Api\Data\RoleInterface">
        <plugin name="getRoleInstance" type="Comave\SellerStatus\Plugin\GetInstanceForRole"/>
    </type>

    <type name="Magento\Company\Model\ResourcePool">
        <arguments>
            <argument name="resources" xsi:type="array">
                <item xsi:type="string" name="Comave_SellerStatus::seller_menu">Comave_SellerStatus::seller_menu</item>
                <item xsi:type="string" name="Comave_SellerStatus::menu_dashboard">Comave_SellerStatus::menu_dashboard</item>
                <item xsi:type="string" name="Comave_SellerStatus::menu_order">Comave_SellerStatus::menu_order</item>
                <item xsi:type="string" name="Comave_SellerStatus::menu_product">Comave_SellerStatus::menu_product</item>
                <item xsi:type="string" name="Comave_SellerStatus::menu_customer">Comave_SellerStatus::menu_customer</item>
                <item xsi:type="string" name="Comave_SellerStatus::menu_finance">Comave_SellerStatus::menu_finance</item>
                <item xsi:type="string" name="Comave_SellerStatus::menu_settings">Comave_SellerStatus::menu_settings</item>
            </argument>
        </arguments>
    </type>

    <type name="Magento\CatalogGraphQl\Model\Resolver\Products\DataProvider\Product\CompositeCollectionProcessor">
        <arguments>
            <argument name="collectionProcessors" xsi:type="array">
                <item name="marketplaceSellerAccount" xsi:type="object">Comave\SellerStatus\Model\Resolver\CollectionProcessor\SellerStatuses</item>
            </argument>
        </arguments>
    </type>

    <type name="Magento\Framework\Console\CommandListInterface">
        <arguments>
            <argument name="commands" xsi:type="array">
                <item name="comave:seller-onboarding:check-status" xsi:type="object">Comave\SellerStatus\Console\Command\CheckSellers</item>
            </argument>
        </arguments>
    </type>
</config>
