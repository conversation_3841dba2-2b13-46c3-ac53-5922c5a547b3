<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
<!--    <type name="Magento\Framework\App\Router\Base">-->
<!--        <plugin name="checkSellerStatus" type="Comave\SellerStatus\Plugin\CheckSellerStatus"/>-->
<!--    </type>-->
    <type name="Magento\Company\CustomerData\Authorization">
        <arguments>
            <argument xsi:type="array" name="authorizationResources">
                <item xsi:type="string" name="Comave_SellerStatus::seller_menu">Comave_SellerStatus::seller_menu</item>
                <item xsi:type="string" name="Comave_SellerStatus::menu_dashboard">Comave_SellerStatus::menu_dashboard</item>
                <item xsi:type="string" name="Comave_SellerStatus::menu_order">Comave_SellerStatus::menu_order</item>
                <item xsi:type="string" name="Comave_SellerStatus::menu_product">Comave_SellerStatus::menu_product</item>
                <item xsi:type="string" name="Comave_SellerStatus::menu_customer">Comave_SellerStatus::menu_customer</item>
                <item xsi:type="string" name="Comave_SellerStatus::menu_finance">Comave_SellerStatus::menu_finance</item>
                <item xsi:type="string" name="Comave_SellerStatus::menu_settings">Comave_SellerStatus::menu_settings</item>
            </argument>
        </arguments>
    </type>
</config>
