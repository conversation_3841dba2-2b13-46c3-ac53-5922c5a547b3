<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Api/etc/extension_attributes.xsd">
    <extension_attributes for="Magento\Company\Api\Data\RoleInterface">
        <attribute code="is_forced_assigned" type="int" />
        <attribute code="status_instance" type="Comave\SellerStatus\Api\SelfAwareInterface" />
    </extension_attributes>
</config>
