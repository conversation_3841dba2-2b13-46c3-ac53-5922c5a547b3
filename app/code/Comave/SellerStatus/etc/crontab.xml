<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Cron:etc/crontab.xsd">
	<group id="default">
		<job name="comave_outofstock_status" instance="Comave\SellerStatus\Cron\OutOfStockSellers" method="execute">
			<schedule>*/5 * * * *</schedule>
		</job>
        <job name="comave_holiday_reminder" instance="Comave\SellerStatus\Cron\CheckHolidayModes" method="execute">
			<schedule>12 3 * * *</schedule>
		</job>
	</group>
</config>
