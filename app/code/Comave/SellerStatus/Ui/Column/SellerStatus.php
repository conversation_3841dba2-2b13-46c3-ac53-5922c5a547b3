<?php

declare(strict_types=1);

namespace Comave\SellerStatus\Ui\Column;

use Magento\Framework\App\ResourceConnection;
use Magento\Framework\View\Element\UiComponent\ContextInterface;
use Magento\Framework\View\Element\UiComponentFactory;
use Magento\Ui\Component\Listing\Columns\Column;

class SellerStatus extends Column
{
    /**
     * @var array|null
     */
    private ?array $sellerStatuses = [];
    /**
     * @param ResourceConnection $resourceConnection
     * @param ContextInterface $context
     * @param UiComponentFactory $uiComponentFactory
     * @param array $components
     * @param array $data
     */
    public function __construct(
        private readonly ResourceConnection $resourceConnection,
        ContextInterface $context,
        UiComponentFactory $uiComponentFactory,
        array $components = [],
        array $data = []
    ) {
        parent::__construct($context, $uiComponentFactory, $components, $data);
    }

    /**
     * @param array $dataSource
     * @return array
     */
    public function prepareDataSource(array $dataSource): array
    {
        if (!isset($dataSource['data']['items'])) {
            return $dataSource;
        }

        foreach ($dataSource['data']['items'] as &$item) {
            $item[$this->getData('name')] = $this->getSellerStatus(
                (int) $item['seller_id']
            );
        }

        return $dataSource;
    }

    /**
     * @param int $sellerId
     * @return string|null
     */
    private function getSellerStatus(int $sellerId): ?string
    {
        if (empty($this->sellerStatuses)) {
            $connection = $this->resourceConnection->getConnection('read');
            $sellerStatusSelect = $connection->select()
                ->distinct()
                ->from(
                    [
                        'main' => $connection->getTableName('marketplace_userdata'),
                    ],
                    []
                )->join(
                    ['c' => $connection->getTableName('customer_entity')],
                    'c.entity_id = main.seller_id',
                    []
                )->join(
                    ['cr' => $connection->getTableName('company_user_roles')],
                    'cr.user_id = main.seller_id',
                    [
                        'user_id',
                        'role_id'
                    ]
                );

            $this->sellerStatuses = $connection->fetchAssoc($sellerStatusSelect);
        }

        return $this->sellerStatuses[$sellerId]['role_id'] ?? null;
    }
}
