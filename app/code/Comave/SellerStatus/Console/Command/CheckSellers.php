<?php

declare(strict_types=1);

namespace Comave\SellerStatus\Console\Command;

use Comave\SellerStatus\Model\Command\CheckInitialSellerSetup;
use Magento\Framework\Console\Cli;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class CheckSellers extends Command
{
    /**
     * @param CheckInitialSellerSetup $checkInitialSellerSetup
     * @param string|null $name
     */
    public function __construct(
        private readonly CheckInitialSellerSetup $checkInitialSellerSetup,
        ?string $name = null
    ) {
        parent::__construct($name);
    }

    /**
     * @return void
     */
    protected function configure(): void
    {
        $this->setName('comave:seller-onboarding:check-status')
            ->setDescription('Checks initial seller status setup');
    }

    /**
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->checkInitialSellerSetup->execute();

        return Cli::RETURN_SUCCESS;
    }
}
