<?php

declare(strict_types=1);

namespace Comave\SellerStatus\Plugin;

use Magento\Company\Api\CompanyUserRoleManagementInterface;
use Magento\Company\Api\Data\RoleExtensionInterface;
use Magento\Company\Api\Data\RoleExtensionInterfaceFactory;
use Magento\Company\Api\Data\RoleInterface;
use Magento\Framework\App\ResourceConnection;

class IsForceAssigned
{
    /**
     * @param RoleExtensionInterfaceFactory $extensionFactory
     * @param ResourceConnection $resourceConnection
     */
    public function __construct(
        private readonly RoleExtensionInterfaceFactory $extensionFactory,
        private readonly ResourceConnection $resourceConnection,
    ) {
    }

    /**
     * @param CompanyUserRoleManagementInterface $companyUserRoleManagement
     * @param RoleInterface[]|array $roles
     * @return RoleInterface[]|array
     * @param int $sellerId
     */
    public function afterGetRolesForCompanyUser(
        CompanyUserRoleManagementInterface $companyUserRoleManagement,
        array $roles,
        int $sellerId
    ): array {
        if (empty($roles)) {
            return $roles;
        }

        $connection = $this->resourceConnection->getConnection('read');

        foreach ($roles as $role) {
            /** @var RoleExtensionInterface $extension */
            $extension = $role->getExtensionAttributes() ?: $this->extensionFactory->create();

            if ($extension->getIsForcedAssigned() !== null) {
                continue;
            }

            $query = $connection->select()->from(
                ['main' => $connection->getTableName('company_user_roles')],
                [
                    'force_assigned'
                ]
            )->where(
                'user_id = ?',
                $sellerId
            )->where(
                'role_id = ?',
                $role->getId()
            );

            $forcedAssigned = $connection->fetchOne($query);
            $extension->setIsForcedAssigned((int) $forcedAssigned);
            $role->setExtensionAttributes($extension);
        }

        return $roles;
    }
}
