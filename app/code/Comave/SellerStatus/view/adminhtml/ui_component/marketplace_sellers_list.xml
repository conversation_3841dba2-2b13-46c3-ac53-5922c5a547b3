<?xml version="1.0" encoding="UTF-8"?>
<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Ui/etc/ui_configuration.xsd">
    <dataSource name="marketplace_sellers_list_data_source" component="Magento_Ui/js/grid/provider">
        <settings>
            <storageConfig>
                <param name="indexField" xsi:type="string">entity_id</param>
            </storageConfig>
            <updateUrl path="mui/index/render"/>
        </settings>
    </dataSource>
    <columns name="marketplace_sellers_columns">
        <settings>
            <editorConfig>
                <param name="clientConfig" xsi:type="array">
                    <item name="saveUrl" xsi:type="url" path="*/*/inlineEdit"/>
                    <item name="validateBeforeSave" xsi:type="boolean">false</item>
                </param>
                <param name="indexField" xsi:type="string">entity_id</param>
                <param name="enabled" xsi:type="boolean">true</param>
                <param name="selectProvider" xsi:type="string">marketplace_sellers_list.marketplace_sellers_list.marketplace_sellers_columns.ids</param>
            </editorConfig>
            <childDefaults>
                <param name="fieldAction" xsi:type="array">
                    <item name="provider" xsi:type="string">marketplace_sellers_list.marketplace_sellers_list.marketplace_sellers_columns_editor</item>
                    <item name="target" xsi:type="string">startEdit</item>
                    <item name="params" xsi:type="array">
                        <item name="0" xsi:type="string">${ $.$data.rowIndex }</item>
                        <item name="1" xsi:type="boolean">true</item>
                    </item>
                </param>
            </childDefaults>
        </settings>
        <column name="role_id" component="Magento_Ui/js/grid/columns/select" class="Comave\SellerStatus\Ui\Column\SellerStatus">
            <settings>
                <editor>
                    <editorType>select</editorType>
                </editor>
                <options class="Comave\SellerStatus\Model\Option\StatusProvider"/>
                <filter>select</filter>
                <dataType>select</dataType>
                <label translate="true">Seller Status</label>
            </settings>
        </column>
        <column name="force_assigned">
            <settings>
                <filter>false</filter>
                <sortable>false</sortable>
                <dataType>text</dataType>
                <label translate="true">Status Assigned By</label>
            </settings>
        </column>
        <column name="note">
            <settings>
                <filter>false</filter>
                <sortable>false</sortable>
                <dataType>text</dataType>
                <dataScope>note</dataScope>
                <label translate="true">Note</label>
            </settings>
        </column>
    </columns>
</listing>
