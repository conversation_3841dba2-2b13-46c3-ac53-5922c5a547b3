define([
    'Magento_Ui/js/modal/modal',
    'jquery',
    'uiRegistry',
    'underscore'
], function (modal, $, registry, _) {
    'use strict';

    return function (Editor) {
        const specialValues = ['Suspended' , 'Rejected'];

        return Editor.extend({
            /**
             * Returns active records data, indexed by their ids.
             *
             * @returns {Object} Collection of records data.
             */
            getData: function () {
                var data = this.activeRecords.map(function (record) {
                    var elemKey,
                        recordData = record.getData();

                    for (elemKey in recordData) {
                        if (_.isUndefined(recordData[elemKey])) {
                            recordData[elemKey] = null;
                        }
                    }

                    const note = $('#custom-role-note').val();
                    if (!_.isUndefined(note)) {
                        recordData['note'] = note;
                    }

                    return recordData;
                });

                return _.indexBy(data, this.indexField);
            },

            save: function () {
                const selectedIds = this.selections().selected();
                const allData = this.getData();

                let showModal = false;
                let triggeredRoleLabel = '';
                let triggeredEntityId = null;

                for (let id of selectedIds) {
                    const rowData = allData[id];
                    const roleId = rowData['role_id'];
                    const dropdownComponent = registry.get(`${this.index}.${id}.role_id`);
                    const options = dropdownComponent?.options() || [];
                    const match = options.find(opt => opt.value === roleId && _.contains(specialValues, opt.label));

                    if (match) {
                        triggeredRoleLabel = match?.label || roleId;
                        triggeredEntityId = id;
                        showModal = true;
                        break;
                    }
                }

                if (showModal) {
                    // Dynamically build modal DOM
                    const modalId = 'custom-role-modal';
                    let $modalEl = $('#' + modalId);

                    if ($modalEl.length) {
                        $modalEl.remove();
                    }

                    $modalEl = $('<div id="' + modalId + '"></div>').appendTo('body');
                    $modalEl.html(`
                        <div>
                            <p>You selected a sensitive role. Please provide a reason for this change:</p>
                            <textarea class="required-entry" id="custom-role-note" style="width: 100%; height: 80px;"></textarea>
                        </div>
                    `);
                    const uiEditor = this;

                    modal({
                        title: 'Role Change Confirmation',
                        modalClass: 'custom-role-modal',
                        buttons: [{
                            text: 'Confirm',
                            class: 'action-primary',
                            click: () => {
                                const note = $('#custom-role-note').val().trim();

                                if (!note) {
                                    alert('Please enter a reason before confirming.');
                                    $('#custom-role-note').focus();
                                    return;
                                }

                                $modalEl.modal('closeModal');
                                uiEditor.constructor.__super__.save.call(uiEditor);
                            }
                        }, {
                            text: 'Cancel',
                            class: 'action-secondary',
                            click: () => {
                                $modalEl.modal('closeModal');
                            }
                        }]
                    }, $modalEl);

                    $modalEl.modal('openModal');
                } else {
                    this._super();
                }
            }
        });
    };
});
