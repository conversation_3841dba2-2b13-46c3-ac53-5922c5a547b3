<?xml version="1.0"?>
<layout xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_layout.xsd">
    <referenceContainer name="seller.header">
        <block name="seller.status" before="-" cacheable="false" template="Comave_SellerStatus::seller_status.phtml">
            <arguments>
                <argument name="sellerStatusViewModel" xsi:type="object">Comave\SellerStatus\ViewModel\SellerStatus</argument>
            </arguments>
        </block>
    </referenceContainer>
</layout>
