require([
    'underscore',
    'Magento_Customer/js/customer-data',
    'domReady!'
], function (_, customerData) {
    'use strict';

    const menuId = 'wk-mp-menu-',
        sellerMenuAcl = 'Comave_SellerStatus::menu_';

    const validateCompanyRole = function () {
        customerData.reload(['loggedAsCustomer','customer','company','company_authorization']).done(function (result) {
            if (result?.loggedAsCustomer?.adminUserId) { //this means it's an admin user logged in as a seller
                return;
            }

            if (result?.customer?.isSeller && result?.company?.has_customer_company) {
                _.each(result?.company_authorization?.resources || [], function (isAllowed, resourceName) {
                    if (resourceName.indexOf(sellerMenuAcl) !== -1 && isAllowed === false) {
                        const menuType = resourceName.replace(sellerMenuAcl, '');
                        document.getElementById(menuId + menuType)?.remove();
                    }
                });
            }
        });
    };

    window.validateCompanyRole = validateCompanyRole;
    validateCompanyRole();
});
