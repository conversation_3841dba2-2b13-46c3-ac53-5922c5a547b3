<?php

declare(strict_types=1);

/** @var \Magento\Framework\View\Element\Template $block */
/** @var \Magento\Framework\Escaper $escaper */
/** @var \Magento\Framework\View\Helper\SecureHtmlRenderer $secureRenderer */
/** @var \Comave\SellerStatus\ViewModel\SellerStatus $viewModel */
$viewModel = $block->getData('sellerStatusViewModel');
$sellerStatus = $viewModel?->getSellerStatus() ?? null;
?>
<?php if ($sellerStatus): ?>
    <div class="seller-status" style="display: flex; justify-content: center; align-items: center; flex-direction: column;">
        <p>Current Status: <strong><?= $escaper->escapeHtml($sellerStatus); ?></strong></p>
        <?php if (in_array($sellerStatus, [\Comave\SellerStatus\Model\RoleValidator\Active::ROLE_NAME, \Comave\SellerStatus\Model\RoleValidator\HolidayMode::ROLE_NAME])): ?>
            <?php
            $applyStatus = $sellerStatus === \Comave\SellerStatus\Model\RoleValidator\Active::ROLE_NAME;
            $scriptString = <<<SCRIPTSTRING
require([
    'jquery',
    'Magento_Ui/js/modal/confirm',
    'mage/calendar',
    'mage/translate'
], function ($, confirmModal, calendar, \$t) {
    $(document).on('click', '#no_holiday', function (e) {
        $.ajax({
            url: '{$block->getUrl('marketplace/seller_status/update')}',
            type: 'POST',
            data: {
                flag: '$applyStatus',
                form_key: window.FORM_KEY
            },
            success: function (response) {
                $('body').trigger('processStop');
                alert(response.message);
                setTimeout(function () {
                    window.location.reload();
                }, 1500);
            },
            error: function (xhr) {
                $('body').trigger('processStop');
                alert(xhr.responseText);
            }
        });
    });
    $(document).on('click', '#holiday_mode', function (e) {
        e.preventDefault();
        confirmModal({
            title: 'Activate your holiday mode',
            content: `
                <div style="margin-bottom: 10px;">
                    <label for="start_date">\${\$t('Start Date')}:</label><br/>
                    <input type="text" id="start_date" class="admin__control-text" readonly/>
                </div>
                <div style="margin-bottom: 10px;">
                    <label for="end_date">\${\$t('End Date')}:</label><br/>
                    <input type="text" id="end_date" class="admin__control-text" readonly/>
                </div>
                <div id="date_error" style="color: red; display: none;"></div>
            `,
            actions: {
                confirm: function () {
                    const start = $('#start_date').datepicker('getDate');
                    const end = $('#end_date').datepicker('getDate');

                    if (!start || !end) {
                        $('#date_error').text(\$t('Both dates are required.')).show();
                        return false;
                    }

                    const diff = (end - start) / (1000 * 60 * 60 * 24);

                    if (diff < 0) {
                        $('#date_error').text(\$t('End Date must be after Start Date.')).show();
                        return false;
                    }

                    if (diff > 28) {
                        $('#date_error').text(\$t('Date range must not exceed 28 days.')).show();
                        return false;
                    }

                    $('body').trigger('processStart');
                    $('#date_error').hide();

                    $.ajax({
                        url: '{$block->getUrl('marketplace/seller_status/update')}',
                        type: 'POST',
                        data: {
                            flag: '$applyStatus',
                            start_date: $.datepicker.formatDate('yy-mm-dd', start),
                            end_date: $.datepicker.formatDate('yy-mm-dd', end),
                            form_key: window.FORM_KEY
                        },
                        success: function (response) {
                            $('body').trigger('processStop');
                            alert(response.message);
                            setTimeout(function () {
                                window.location.reload();
                            }, 1000);
                        },
                        error: function (xhr) {
                            $('body').trigger('processStop');
                            alert(xhr.responseText);
                        }
                    });
                },
                cancel: function () {
                    $('#date_error').hide();
                }
            }
        });

        // Init calendars after modal renders
        setTimeout(function () {
            const today = new Date();

            $('#start_date').calendar({
                dateFormat: 'yy-mm-dd',
                minDate: today,
                onSelect: function (dateText, inst) {
                    const selected = $('#start_date').datepicker('getDate');
                    const maxEnd = new Date(selected.getTime());
                    maxEnd.setDate(maxEnd.getDate() + 28);

                    $('#end_date').datepicker('option', {
                        minDate: selected,
                        maxDate: maxEnd
                    });
                }
            });

            $('#end_date').calendar({
                dateFormat: 'yy-mm-dd'
            });
        }, 100);
    });
});
SCRIPTSTRING;
            ?>
            <p>
                <button type="button" id="<?= $applyStatus ? 'holiday_mode' : 'no_holiday' ?>">
                    <?= $escaper->escapeHtml(
                        __('%1 Holiday Mode', $applyStatus ? 'Activate' : 'Deactivate')
                    ); ?>
                </button>
            </p>
            <?= $secureRenderer->renderTag('script', [], $scriptString, false); ?>
        <?php endif; ?>
    </div>
<?php endif; ?>
