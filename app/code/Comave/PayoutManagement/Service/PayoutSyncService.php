<?php

declare(strict_types=1);

namespace Comave\PayoutManagement\Service;

use Comave\PayoutManagement\Model\Payout;
use Comave\PayoutManagement\Model\PayoutFactory;
use Comave\PayoutManagement\Model\ResourceModel\Payout as PayoutResource;
use Comave\PayoutManagement\Model\ResourceModel\Payout\CollectionFactory;
use Psr\Log\LoggerInterface;

class PayoutSyncService
{
    public function __construct(
        private readonly StripePayoutService $stripePayoutService,
        private readonly PayoutFactory $payoutFactory,
        private readonly PayoutResource $payoutResource,
        private readonly CollectionFactory $payoutCollectionFactory,
        private readonly LoggerInterface $logger
    ) {}

    /**
     * Synchronize payouts from Stripe API to local database
     */
    public function syncPayouts(): array
    {
        try {
            $stripePayouts = $this->stripePayoutService->getAllPayouts();
            $updated = 0;
            $added = 0;
            $errors = [];

            $existingPayouts = $this->getExistingPayoutsMap();

            foreach ($stripePayouts as $stripePayoutData) {
                try {
                    $result = $this->syncSinglePayout($stripePayoutData, $existingPayouts);

                    if ($result['action'] === 'updated') {
                        $updated++;
                    } elseif ($result['action'] === 'added') {
                        $added++;
                    }
                } catch (\Exception $e) {
                    $errors[] = 'Error syncing payout ' . ($stripePayoutData['stripe_payout_id'] ?? 'unknown') . ': ' . $e->getMessage();
                    $this->logger->info('Error syncing payout', [
                        'error' => $e->getMessage(),
                        'stripe_payout_id' => $stripePayoutData['stripe_payout_id'] ?? 'unknown'
                    ]);
                }
            }

            if (!empty($errors)) {
                $this->logger->info('Some payouts failed to sync', ['errors' => $errors]);
            }

            return [
                'success' => true,
                'updated' => $updated,
                'added' => $added,
                'errors' => $errors
            ];
        } catch (\Exception $e) {
            $this->logger->error('Error during payout sync: ' . $e->getMessage());

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'updated' => 0,
                'added' => 0
            ];
        }
    }

    /**
     * Sync a single payout
     *
     * @param array $stripePayoutData
     * @param array $existingPayouts
     * @return array
     */
    private function syncSinglePayout(array $stripePayoutData, array $existingPayouts): array
    {
        $stripePayoutId = $stripePayoutData['stripe_payout_id'];

        if (isset($existingPayouts[$stripePayoutId])) {
            $existingPayout = $this->payoutFactory->create();
            $existingPayout->load($existingPayouts[$stripePayoutId]);
            $payoutModel = $existingPayout;
            $action = 'updated';
        } else {
            $payoutModel = $this->payoutFactory->create();
            $action = 'added';
        }

        $this->updatePayoutData($payoutModel, $stripePayoutData);
        $this->payoutResource->save($payoutModel);

        return ['action' => $action, 'payout_id' => $payoutModel->getId()];
    }

    /**
     * Update payout model with Stripe data
     *
     * @param Payout $payout
     * @param array $stripePayoutData
     * @return void
     */
    private function updatePayoutData(Payout $payout, array $stripePayoutData): void
    {
        $payout->setStripePayoutId($stripePayoutData['stripe_payout_id']);
        $payout->setSellerId((int)$stripePayoutData['seller_id']);
        $payout->setSellerName($stripePayoutData['seller_name']);
        $payout->setStripeAccountId($stripePayoutData['stripe_account_id']);
        $payout->setAmount((float)$stripePayoutData['amount']);
        $payout->setCurrency($stripePayoutData['currency']);
        $payout->setStatus($stripePayoutData['status']);
        $payout->setPaymentMethod($stripePayoutData['payment_method']);


        if (!empty($stripePayoutData['scheduled_date'])) {
            $payout->setScheduledDate($stripePayoutData['scheduled_date']);
        }

        if (!empty($stripePayoutData['completion_date'])) {
            $payout->setCompletionDate($stripePayoutData['completion_date']);
        }

        $payout->setLastSyncAt($stripePayoutData['last_sync_at']);
    }

    /**
     * Get existing payouts as a map for performance optimization
     *
     * @return array
     */
    private function getExistingPayoutsMap(): array
    {
        $connection = $this->payoutResource->getConnection();
        $tableName = $this->payoutResource->getMainTable();

        $select = $connection->select()
            ->from($tableName, ['stripe_payout_id', 'entity_id'])
            ->where('stripe_payout_id IS NOT NULL');

        return $connection->fetchPairs($select);
    }

    /**
     * Clean up old payout records
     *
     * @param int $daysOld
     * @return int Number of deleted records
     */
    public function cleanupOldPayouts(int $daysOld = 90): int
    {
        $connection = $this->payoutResource->getConnection();
        $tableName = $this->payoutResource->getMainTable();
        $cutoffDate = date('Y-m-d H:i:s', strtotime("-{$daysOld} days"));

        $deletedCount = $connection->delete(
            $tableName,
            [
                'created_at < ?' => $cutoffDate,
                'status IN (?)' => ['paid', 'failed']
            ]
        );

        if ($deletedCount > 0) {
            $this->logger->info("Cleaned up {$deletedCount} old payout records");
        }

        return $deletedCount;
    }
}
