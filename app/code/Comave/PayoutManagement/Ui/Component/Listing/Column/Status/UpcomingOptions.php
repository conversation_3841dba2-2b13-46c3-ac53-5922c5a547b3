<?php

declare(strict_types=1);

namespace Comave\PayoutManagement\Ui\Component\Listing\Column\Status;

use Magento\Framework\Data\OptionSourceInterface;

/**
 * Upcoming Payouts Status Options
 *
 * Provides status options for upcoming payouts filter
 */
class UpcomingOptions implements OptionSourceInterface
{
    /**
     * Get options array for upcoming payout statuses
     *
     * @return array
     */
    public function toOptionArray(): array
    {
        return [
            ['value' => 'pending', 'label' => __('Pending')],
            ['value' => 'in_transit', 'label' => __('In Transit')]
        ];
    }
}
