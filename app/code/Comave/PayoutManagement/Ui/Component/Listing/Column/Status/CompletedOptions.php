<?php

declare(strict_types=1);

namespace Comave\PayoutManagement\Ui\Component\Listing\Column\Status;

use Magento\Framework\Data\OptionSourceInterface;

/**
 * Completed Payouts Status Options
 *
 * Provides status options for completed payouts filter
 */
class CompletedOptions implements OptionSourceInterface
{
    /**
     * Get options array for completed payout statuses
     *
     * @return array
     */
    public function toOptionArray(): array
    {
        return [
            ['value' => 'paid', 'label' => __('Paid')],
            ['value' => 'failed', 'label' => __('Failed')]
        ];
    }
}
