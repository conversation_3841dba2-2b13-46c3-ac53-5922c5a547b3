<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Acl/etc/acl.xsd">
    <acl>
        <resources>
            <resource id="Magento_Backend::admin">
                <resource id="Magento_Sales::sales">
                    <resource id="Comave_PayoutManagement::payout_management" title="Payout Management" sortOrder="50">
                        <resource id="Comave_PayoutManagement::upcoming_payouts" title="Upcoming Payouts" sortOrder="10"/>
                        <resource id="Comave_PayoutManagement::completed_payouts" title="Completed Payouts" sortOrder="20"/>
                        <resource id="Comave_PayoutManagement::refresh_payouts" title="Refresh Payouts" sortOrder="30"/>
                        <resource id="Comave_PayoutManagement::export_payouts" title="Export Payouts" sortOrder="40"/>
                    </resource>
                </resource>
            </resource>
        </resources>
    </acl>
</config>
