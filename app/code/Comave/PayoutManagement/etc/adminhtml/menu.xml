<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Backend:etc/menu.xsd">
    <menu>
        <add id="Comave_PayoutManagement::payout_management"
             title="Payout Management"
             module="Comave_PayoutManagement"
             sortOrder="50"
             parent="Magento_Sales::sales"
             resource="Comave_PayoutManagement::payout_management"/>
        <add id="Comave_PayoutManagement::upcoming_payouts"
             title="Upcoming Payouts"
             module="Comave_PayoutManagement"
             sortOrder="10"
             parent="Comave_PayoutManagement::payout_management"
             action="payout_management/upcoming/index"
             resource="Comave_PayoutManagement::upcoming_payouts"/>
        <add id="Comave_PayoutManagement::completed_payouts"
             title="Completed Payouts"
             module="Comave_PayoutManagement"
             sortOrder="20"
             parent="Comave_PayoutManagement::payout_management"
             action="payout_management/completed/index"
             resource="Comave_PayoutManagement::completed_payouts"/>
    </menu>
</config>
