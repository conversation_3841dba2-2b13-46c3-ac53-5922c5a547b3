<?php

declare(strict_types=1);

namespace Comave\PayoutManagement\Block\Adminhtml\Button;

use Magento\Framework\View\Element\UiComponent\Control\ButtonProviderInterface;
use Magento\Backend\Block\Widget\Context;

/**
 * Refresh Button Provider
 *
 * Provides refresh button configuration for payout management grids
 */
class RefreshButton implements ButtonProviderInterface
{
    /**
     * Constructor
     *
     * @param Context $context
     */
    public function __construct(
        private readonly Context $context
    ) {}

    /**
     * Get button data configuration
     *
     * @return array
     */
    public function getButtonData(): array
    {
        return [
            'label' => __('Refresh from Stripe'),
            'class' => 'primary',
            'on_click' => 'require([\'Comave_PayoutManagement/js/refresh-payouts\'], function(refreshPayouts) { ' .
                'refreshPayouts.refresh(\'' . $this->getRefreshUrl() . '\'); })',
            'sort_order' => 10,
            'aclResource' => 'Comave_PayoutManagement::refresh_payouts'
        ];
    }

    /**
     * Get refresh URL
     *
     * @return string
     */
    private function getRefreshUrl(): string
    {
        return $this->context->getUrlBuilder()->getUrl('payout_management/refresh/index');
    }
}
