<?php

declare(strict_types=1);

namespace Comave\PayoutManagement\Api\Data;

interface PayoutInterface
{
    public const ENTITY_ID = 'entity_id';
    public const STRIPE_PAYOUT_ID = 'stripe_payout_id';
    public const SELLER_ID = 'seller_id';
    public const SELLER_NAME = 'seller_name';
    public const STRIPE_ACCOUNT_ID = 'stripe_account_id';
    public const AMOUNT = 'amount';
    public const CURRENCY = 'currency';
    public const STATUS = 'status';
    public const PAYMENT_METHOD = 'payment_method';
    public const SCHEDULED_DATE = 'scheduled_date';
    public const COMPLETION_DATE = 'completion_date';
    public const CREATED_AT = 'created_at';
    public const UPDATED_AT = 'updated_at';
    public const LAST_SYNC_AT = 'last_sync_at';

    /**
     * Get Stripe payout ID
     *
     * @return string|null
     */
    public function getStripePayoutId(): ?string;

    /**
     * Set Stripe payout ID
     *
     * @param string $stripePayoutId
     * @return PayoutInterface
     */
    public function setStripePayoutId(string $stripePayoutId): PayoutInterface;

    /**
     * Get seller ID
     *
     * @return int|null
     */
    public function getSellerId(): ?int;

    /**
     * Set seller ID
     *
     * @param int $sellerId
     * @return PayoutInterface
     */
    public function setSellerId(int $sellerId): PayoutInterface;

    /**
     * Get seller name
     *
     * @return string|null
     */
    public function getSellerName(): ?string;

    /**
     * Set seller name
     *
     * @param string|null $sellerName
     * @return PayoutInterface
     */
    public function setSellerName(?string $sellerName): PayoutInterface;

    /**
     * Get Stripe account ID
     *
     * @return string|null
     */
    public function getStripeAccountId(): ?string;

    /**
     * Set Stripe account ID
     *
     * @param string $stripeAccountId
     * @return PayoutInterface
     */
    public function setStripeAccountId(string $stripeAccountId): PayoutInterface;

    /**
     * Get amount
     *
     * @return float|null
     */
    public function getAmount(): ?float;

    /**
     * Set amount
     *
     * @param float $amount
     * @return PayoutInterface
     */
    public function setAmount(float $amount): PayoutInterface;

    /**
     * Get currency
     *
     * @return string|null
     */
    public function getCurrency(): ?string;

    /**
     * Set currency
     *
     * @param string $currency
     * @return PayoutInterface
     */
    public function setCurrency(string $currency): PayoutInterface;

    /**
     * Get status
     *
     * @return string|null
     */
    public function getStatus(): ?string;

    /**
     * Set status
     *
     * @param string $status
     * @return PayoutInterface
     */
    public function setStatus(string $status): PayoutInterface;

    /**
     * Get payment method
     *
     * @return string|null
     */
    public function getPaymentMethod(): ?string;

    /**
     * Set payment method
     *
     * @param string $paymentMethod
     * @return PayoutInterface
     */
    public function setPaymentMethod(string $paymentMethod): PayoutInterface;

    /**
     * Get scheduled date
     *
     * @return string|null
     */
    public function getScheduledDate(): ?string;

    /**
     * Set scheduled date
     *
     * @param string|null $scheduledDate
     * @return PayoutInterface
     */
    public function setScheduledDate(?string $scheduledDate): PayoutInterface;

    /**
     * Get completion date
     *
     * @return string|null
     */
    public function getCompletionDate(): ?string;

    /**
     * Set completion date
     *
     * @param string|null $completionDate
     * @return PayoutInterface
     */
    public function setCompletionDate(?string $completionDate): PayoutInterface;

    /**
     * Get last sync timestamp
     *
     * @return string|null
     */
    public function getLastSyncAt(): ?string;

    /**
     * Set last sync timestamp
     *
     * @param string|null $lastSyncAt
     * @return PayoutInterface
     */
    public function setLastSyncAt(?string $lastSyncAt): PayoutInterface;
}
