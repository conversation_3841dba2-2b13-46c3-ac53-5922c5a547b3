<?php

declare(strict_types=1);

namespace Comave\PayoutManagement\Controller\Adminhtml\Export;

use Magento\Backend\App\Action;
use Magento\Backend\App\Action\Context;
use Magento\Framework\App\Action\HttpGetActionInterface;
use Magento\Framework\App\Response\Http\FileFactory;
use Comave\PayoutManagement\Model\ResourceModel\Payout\CollectionFactory;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\App\ResponseInterface;
use Magento\Framework\File\Csv as CsvProcessor;
use Comave\PayoutManagement\Model\ResourceModel\Payout\Collection;

/**
 * Unified CSV Export Controller
 *
 * Exports payout data to CSV format based on type parameter
 */
class Csv extends Action implements HttpGetActionInterface
{
    /**
     * Admin resource for ACL
     */
    public const ADMIN_RESOURCE = 'Comave_PayoutManagement::export_payouts';

    /**
     * Payout type constants
     */
    private const TYPE_UPCOMING = 'upcoming';
    private const TYPE_COMPLETED = 'completed';

    /**
     * Constructor
     *
     * @param Context $context
     * @param FileFactory $fileFactory
     * @param CollectionFactory $collectionFactory
     * @param CsvProcessor $csvProcessor
     */
    public function __construct(
        Context $context,
        private readonly FileFactory $fileFactory,
        private readonly CollectionFactory $collectionFactory,
        private readonly CsvProcessor $csvProcessor
    ) {
        parent::__construct($context);
    }

    /**
     * Execute CSV export based on type parameter
     *
     * @return ResponseInterface
     */
    public function execute(): ResponseInterface
    {
        $type = $this->getRequest()->getParam('type', self::TYPE_COMPLETED);
        
        if (!in_array($type, [self::TYPE_UPCOMING, self::TYPE_COMPLETED])) {
            $type = self::TYPE_COMPLETED;
        }

        $fileName = $type . '_payouts_' . date('Y-m-d_H-i-s') . '.csv';
        
        $collection = $this->collectionFactory->create();
        $this->applyTypeFilter($collection, $type);
        
        $csvContent = $this->generateCsvContent($collection, $type);
        
        return $this->fileFactory->create(
            $fileName,
            $csvContent,
            DirectoryList::VAR_DIR,
            'text/csv'
        );
    }

    /**
     * Apply filter based on payout type
     *
     * @param Collection $collection
     * @param string $type
     * @return void
     */
    private function applyTypeFilter(Collection $collection, string $type): void
    {
        if ($type === self::TYPE_UPCOMING) {
            $collection->addFieldToFilter('status', ['in' => ['pending', 'in_transit']]);
        } else {
            $collection->addFieldToFilter('status', ['in' => ['paid', 'failed']]);
        }
    }

    /**
     * Generate CSV content using proper CSV processor
     *
     * @param Collection $collection
     * @param string $type
     * @return string
     */
    private function generateCsvContent(Collection $collection, string $type): string
    {
        $csvData = [];
        
        $csvData[] = $this->getHeaderRow($type);
        
        foreach ($collection as $payout) {
            $csvData[] = $this->getDataRow($payout, $type);
        }
        
        $tmpFile = tmpfile();
        $tmpPath = stream_get_meta_data($tmpFile)['uri'];
        
        $this->csvProcessor->saveData($tmpPath, $csvData);
        $csvContent = file_get_contents($tmpPath);
        
        fclose($tmpFile);
        
        return $csvContent;
    }

    /**
     * Get header row based on type
     *
     * @param string $type
     * @return array
     */
    private function getHeaderRow(string $type): array
    {
        $baseHeaders = [
            'Seller ID',
            'Seller Name', 
            'Payout Amount (EUR)',
        ];

        if ($type === self::TYPE_UPCOMING) {
            $baseHeaders[] = 'Scheduled Date';
        } else {
            $baseHeaders[] = 'Completion Date';
        }

        $baseHeaders[] = 'Payment Method';
        $baseHeaders[] = 'Status';
        $baseHeaders[] = 'Last Sync';

        return $baseHeaders;
    }

    /**
     * Get data row for payout
     *
     * @param \Comave\PayoutManagement\Model\Payout $payout
     * @param string $type
     * @return array
     */
    private function getDataRow($payout, string $type): array
    {
        $row = [
            $payout->getSellerId(),
            $payout->getSellerName() ?? '',
            number_format((float)$payout->getAmount(), 2),
        ];

        if ($type === self::TYPE_UPCOMING) {
            $row[] = $payout->getScheduledDate() ?? '';
        } else {
            $row[] = $payout->getCompletionDate() ?? '';
        }

        $row[] = $payout->getPaymentMethod();
        $row[] = $payout->getStatus();
        $row[] = $payout->getLastSyncAt() ?? '';

        return $row;
    }
}
