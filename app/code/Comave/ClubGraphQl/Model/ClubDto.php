<?php
declare(strict_types=1);


namespace Comave\ClubGraphQl\Model;


use Comave\ClubGraphQl\Api\RenderToArrayInterface;
use Comave\Club\Api\Data\ClubInterface;

class ClubDto implements RenderToArrayInterface
{
    private ClubInterface $club;

    final public function setClub(ClubInterface $club): void
    {
        $this->club = $club;
    }

    public function toArray(): array
    {
        return [
            ClubInterface::CLUB_ID => $this->club->getId(),
            ClubInterface::CLUB_NAME => $this->club->getName(),
            ClubInterface::CLUB_DESCRIPTION => $this->club->getDescription(),
            ClubInterface::CLUB_SUBTITLE => $this->club->getSubtitle(),
            ClubInterface::CLUB_IMAGE => $this->club->getImageUrl(),
            ClubInterface::CLUB_URL_KEY => $this->club->getUrl(),
            ClubInterface::CLUB_STATUS => $this->club->getStatus(),
            ClubInterface::CLUB_POSITION => $this->club->getPosition(),
            ClubInterface::CLUB_LOGO => $this->club->getClogoUrl(),
            ClubInterface::CLUB_BANNER => $this->club->getClubBannerUrl(),
            ClubInterface::CLUB_PREFIX => $this->club->getClubPrefix(),
            ClubInterface::CLUB_UNIQUE_ID => $this->club->getUniqueId(),
            ClubInterface::CLUB_PARTNERED => $this->club->getPartnered(),
        ];
    }
}
