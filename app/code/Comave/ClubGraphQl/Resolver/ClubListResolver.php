<?php
declare(strict_types=1);

namespace Comave\ClubGraphQl\Resolver;

use Comave\Club\Api\ClubRepositoryInterface;
use Comave\ClubGraphQl\Model\ClubDto;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;

class ClubListResolver implements ResolverInterface
{
    public function __construct(
        private readonly ClubRepositoryInterface $clubRepository,
        private readonly ClubDto $clubDto
    ) {
    }

    /**
     * @inheritdoc
     */
    public function resolve(
        Field $field,
        $context,
        ResolveInfo $info,
        array $value = null,
        array $args = null
    ) {
        $websiteId = $context?->getExtensionAttributes()?->getStore()?->getWebsiteId();
        if (!$websiteId) {
            throw new LocalizedException(__('Website ID is not found'));
        }
        $clubs = [];
        $clubCollection = $this->clubRepository->getActiveClubs();
        
        // Apply partnered filter if provided
        if (isset($args['partnered']) && is_numeric($args['partnered'])) {
            $clubCollection->addFieldToFilter('partnered', (int) $args['partnered']);
        }
        
        $clubListItems = $clubCollection->getItems();
        foreach ($clubListItems as $club) {
            $this->clubDto->setClub($club);
            $clubs[] = $this->clubDto->toArray();
        }

        return [
            'items' => $clubs
        ];
    }
}
