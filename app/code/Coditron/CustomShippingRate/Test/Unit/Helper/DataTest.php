<?php
declare(strict_types=1);

namespace Coditron\CustomShippingRate\Test\Unit\Helper;

use PHPUnit\Framework\TestCase;
use PHPUnit\Framework\MockObject\MockObject;
use Coditron\CustomShippingRate\Helper\Data;
use Coditron\CustomShippingRate\Model\ResourceModel\ShipTableRates\CollectionFactory;
use Coditron\CustomShippingRate\Model\ResourceModel\ShipTableRates\Collection;
use Coditron\CustomShippingRate\Model\ShipTableRates;

/**
 * Unit test for the consolidated threshold logic in Helper\Data
 */
class DataTest extends TestCase
{
    /**
     * @var Data|MockObject
     */
    private $helper;

    /**
     * @var CollectionFactory|MockObject
     */
    private $collectionFactory;

    /**
     * @var Collection|MockObject
     */
    private $collection;

    protected function setUp(): void
    {
        $this->collectionFactory = $this->createMock(CollectionFactory::class);
        $this->collection = $this->createMock(Collection::class);
        
        $this->helper = $this->getMockBuilder(Data::class)
            ->setConstructorArgs([
                $this->createMock(\Magento\Framework\App\Helper\Context::class),
                $this->createMock(\Magento\Framework\App\Request\Http::class),
                $this->createMock(\Webkul\Marketplace\Helper\Data::class),
                $this->collectionFactory,
                $this->createMock(\Magento\Framework\App\Cache\Manager::class),
                $this->createMock(\Magento\Framework\Stdlib\DateTime\DateTime::class),
                $this->createMock(\Magento\Framework\Stdlib\DateTime\TimezoneInterface::class),
                $this->createMock(\Magento\Framework\App\Config\ScopeConfigInterface::class),
                $this->createMock(\Magento\Store\Model\StoreManagerInterface::class),
                $this->createMock(\Magento\Framework\Locale\CurrencyInterface::class),
                $this->createMock(\Magento\Directory\Model\CurrencyFactory::class),
                $this->createMock(\Magento\Framework\Pricing\PriceCurrencyInterface::class)
            ])
            ->onlyMethods(['getSellerId'])
            ->getMock();
    }


}
